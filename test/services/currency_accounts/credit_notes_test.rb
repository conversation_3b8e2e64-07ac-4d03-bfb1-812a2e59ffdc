require 'test_helper'

class CurrencyAccounts::CreditNotesTest < ActiveSupport::TestCase
  test "credit notes and create alior file" do
    note = project_debit_summaries(:pds)
    note_2 = project_debit_summaries(:debit_pds)

    currency_account = currency_accounts(:cro_credit_currency_account)
    transfer = clinical_transfers(:clinical_transfer)

    ProjectDebitSummaries::Credit
      .expects(:call)
      .with(note: note, amount: 300, currency_account: currency_account)
      .returns(bank_transfer: transfer, amount_used: 100)

    ProjectDebitSummaries::Credit
      .expects(:call)
      .with(note: note_2, amount: 200, currency_account: currency_account)
      .returns(bank_transfer: transfer, amount_used: 100)

    assert_difference 'AliorFile.count' do
      assert_changes 'transfer.reload.alior_file_id' do
        CurrencyAccounts::CreditNotes.call(
          notes: [note, note_2],
          currency_account: currency_account,
          amount: 300
        )
      end
    end
  end
end