require 'test_helper'

class SitePayments::Payments::SendSmsPaymentSentTest < ActiveSupport::TestCase
  test "send sms" do
    payment = site_payments_payments(:sp_payment)

    assert_difference 'payment.to_researcher.clinical_mobile_messages.count' do
      SitePayments::Payments::SendSmsPaymentSent.call(payment: payment, account_number: '123')
    end
  end

  test "not send sms if phone missing" do
    payment = site_payments_payments(:sp_payment)
    researcher = researchers(:jax)
    researcher.update_columns phone_number: nil

    assert_no_difference 'payment.to_researcher.clinical_mobile_messages.count' do
      SitePayments::Payments::SendSmsPaymentSent.call(payment: payment, account_number: '123')
    end
  end
end
