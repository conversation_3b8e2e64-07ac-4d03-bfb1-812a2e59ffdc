require 'test_helper'

class Alior::FileContentForTransfersTest < ActiveSupport::TestCase
  test "content for file" do
    transfer = clinical_transfers(:clinical_transfer)

    result = Alior::FileContentForTransfers.call(transfers: [transfer])

    assert_includes result, transfer.name
  end

  test "content for file with transfer source_acc_nr" do
    source_acc_nr = 'source_acc_nr'
    transfer = clinical_transfers(:clinical_transfer)
    transfer.source_acc_nr = source_acc_nr

    result = Alior::FileContentForTransfers.call(transfers: [transfer], sender_from_transfer: true)

    assert_includes result, source_acc_nr
  end
end