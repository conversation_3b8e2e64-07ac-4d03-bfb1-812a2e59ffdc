require 'test_helper'

class Alior::ParseFileLineTest < ActiveSupport::TestCase
  setup do
    @currency_account = currency_accounts(:cro_currency_account)
    @balance = @currency_account.balance
    @acc_nr = @currency_account.account_number
    @source_acc_nr = "12345"
    @title = 'title'
    @amount = 100
    @source_name = "MSD POLSKA SP. Z O.O. 00-867 WARSZAWA"
    @transaction_id = '9'
    @line = "********|#{@acc_nr}|0510|#{@amount}00|#{@source_acc_nr}|#{@source_name}\xFE|#{@title}\xFE|#{@transaction_id}"
    @alior_file = alior_files(:alior_file)
    @alior_file_id = @alior_file.id
  end

  test "should create transfer" do
    assert_difference 'AliorTransferRelation.count' do
      assert_changes '@alior_file.reload.currency' do
        ct = Alior::ParseFileLine.call(line: @line, alior_file_id: @alior_file_id)

        assert_equal @amount, ct.amount
        assert_equal @acc_nr, ct.account_number
        assert_equal @title, ct.title
        assert_equal @currency_account, ct.currency_account
        assert_equal @balance + @amount, @currency_account.reload.balance
        assert_equal @amount, ct.balance
        assert_equal @amount + @balance, ct.currency_account_balance_after
        assert_equal @alior_file_id, ct.alior_file_id
        assert_equal @source_acc_nr, ct.source_acc_nr
        assert_equal @source_name, ct.source_name
        assert_equal @transaction_id, ct.alior_transaction_id
      end
    end
  end

  test 'not update currency_account balance if transfer with alior_transaction_id exists' do
    existing_transfer = clinical_transfers(:clinical_transfer)
    existing_transfer.update_columns alior_transaction_id: @transaction_id

    assert_difference 'AliorTransferRelation.count' do
      result = Alior::ParseFileLine.call(line: @line, alior_file_id: @alior_file_id)

      assert_nil result
    end
  end
end