require 'test_helper'

class Researchers::BlockAndRemoveRolesTest < ActiveSupport::TestCase
  test "remove roles" do
    researcher = researchers(:researcher)

    assert_changes 'researcher.project_roles.count', to: 0 do
      assert_changes 'researcher.clinical_center_roles.count', to: 0 do
        assert_changes 'researcher.blocked_at' do
          assert_changes 'researcher.reload.deleted_at' do
            Researchers::BlockAndRemoveRoles.call(researcher: researcher)
          end
        end
      end
    end
  end
end
