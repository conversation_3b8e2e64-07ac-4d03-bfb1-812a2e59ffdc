require 'test_helper'

class ClinicalUserForms::CreateHelpdeskEmailTest < ActiveSupport::TestCase
  test "create helpdesk email" do
    cuf = clinical_user_forms(:clinical_user_form)
    file = Tempfile.new(['test', '.pdf'])
    file.write("Test PDF content")
    file.rewind
    cuf.file.attach(io: file, filename: 'test.pdf', content_type: 'application/pdf')

    assert_emails 1 do
      assert_difference 'ClinicalMobileMessage.count' do
        assert_changes 'cuf.helpdesk_emails.count' do
          assert_difference 'HelpdeskEmailAttachment.count' do
            ClinicalUserForms::CreateHelpdeskEmail.call(clinical_user_form: cuf)
          end
        end
      end
    end

    he = HelpdeskEmail.last
    assert_equal I18n.t('helpdesk_email.from_clinical_user_form.subject', patient_code: cuf.patient_code, protocol: cuf.clinical_protocol_code), he.subject
    assert_equal 'Form', he.request_type
  end
end