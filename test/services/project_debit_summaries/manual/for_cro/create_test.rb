require 'test_helper'

class ProjectDebitSummaries::Manual::ForCro::CreateTest < ActiveSupport::TestCase
  test "create note" do
    amount = 100
    cro = contract_research_organizations(:cro)
    currency = 'USD'
    ProjectDebitSummary.any_instance.stubs(:create_currency_account)

    assert_difference "ProjectDebitSummary.count" do
      note = ProjectDebitSummaries::Manual::ForCro::Create.call(
        cro: cro,
        amount: amount,
        currency: currency
      )

      assert_equal currency, note.currency
      assert_equal amount, note.manual_amount
    end
  end
end
