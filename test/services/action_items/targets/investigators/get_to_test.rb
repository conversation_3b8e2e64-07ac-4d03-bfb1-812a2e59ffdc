require 'test_helper'

class ActionItems::Targets::Investigators::GetToTest < ActiveSupport::TestCase
  test "site AI - return investigator who logged in lately" do
    ai = action_items(:site_action_item)
    inv = researchers(:jack)
    result = ActionItems::Targets::Investigators::GetTo.call(action_item: ai)

    assert_equal inv, result
  end

  test "site AI - returns investigator who created latest action for resource" do
    ai = action_items(:site_action_item)
    inv = researchers(:jax)
    ClinicalUserAccountActivity.create!(clinical_user: clinical_users(:clinical_user), researcher: inv, activity_type: 'view_account')
    result = ActionItems::Targets::Investigators::GetTo.call(action_item: ai)

    assert_equal inv.id, result.id
  end

  test "patient AI - returns investigator who created latest action for patient" do
    ai = action_items(:patient_action_item)
    inv = researchers(:jax)
    ClinicalUserAccountActivity.create!(clinical_user: clinical_users(:clinical_user), researcher: inv, activity_type: 'view_account')
    result = ActionItems::Targets::Investigators::GetTo.call(action_item: ai)

    assert_equal inv.id, result.id
  end

  test "patient AI - returns investigator who created latest action for site" do
    ai = action_items(:patient_action_item)
    inv = researchers(:jax)
    ClinicalUserAccountActivity.create!(clinical_user: clinical_users(:patient_kkk), researcher: inv, activity_type: 'view_account')
    result = ActionItems::Targets::Investigators::GetTo.call(action_item: ai)

    assert_equal inv.id, result.id
  end

  test "patient AI - return investigator who logged in lately" do
    ai = action_items(:patient_action_item)
    inv = researchers(:jack)
    result = ActionItems::Targets::Investigators::GetTo.call(action_item: ai)

    assert_equal inv, result
  end
end
