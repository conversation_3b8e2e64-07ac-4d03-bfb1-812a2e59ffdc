require 'test_helper'

class ActionItems::Targets::Managers::GetToTest < ActiveSupport::TestCase
  test "site AI - return manager who logged in lately" do
    ai = action_items(:site_action_item)
    inv = researchers(:researcher)
    result = ActionItems::Targets::Managers::GetTo.call(action_item: ai)

    assert_equal inv, result
  end

  test "site AI - returns manager who created latest action for resource" do
    ai = action_items(:site_action_item)
    inv = researchers(:researcher)
    ClinicalUserAccountActivity.create!(clinical_user: clinical_users(:clinical_user), researcher: inv, activity_type: 'view_account')
    result = ActionItems::Targets::Managers::GetTo.call(action_item: ai)

    assert_equal inv.id, result.id
  end

  test "patient AI - returns manager who created latest action for patient" do
    ai = action_items(:patient_action_item)
    inv = researchers(:researcher)
    ClinicalUserAccountActivity.create!(clinical_user: clinical_users(:clinical_user), researcher: inv, activity_type: 'view_account')
    result = ActionItems::Targets::Managers::GetTo.call(action_item: ai)

    assert_equal inv.id, result.id
  end

  test "patient AI - returns manager who created latest action for site" do
    ai = action_items(:patient_action_item)
    inv = researchers(:researcher)
    ClinicalUserAccountActivity.create!(clinical_user: clinical_users(:patient_kkk), researcher: inv, activity_type: 'view_account')
    result = ActionItems::Targets::Managers::GetTo.call(action_item: ai)

    assert_equal inv.id, result.id
  end

  test "patient AI - return manager who logged in lately" do
    ai = action_items(:patient_action_item)
    inv = researchers(:researcher)
    result = ActionItems::Targets::Managers::GetTo.call(action_item: ai)

    assert_equal inv, result
  end
end
