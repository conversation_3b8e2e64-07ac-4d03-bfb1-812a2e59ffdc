require 'test_helper'

class ActionItems::Targets::Managers::GetCcTest < ActiveSupport::TestCase
  test "site AI - return managers except the one for To" do
    ai = action_items(:site_action_item)
    to = researchers(:researcher)
    researchers(:jax).project_roles.update_all(project_role: 'Manager')
    ActionItems::Targets::Managers::GetTo.expects(:call).with(action_item: ai).returns(to)
    result = ActionItems::Targets::Managers::GetCc.call(action_item: ai)

    assert_not result.include?(to)
    assert_includes result, researchers(:jax)
  end

  test "patient AI - return managers except the one for To" do
    ai = action_items(:patient_action_item)
    to = researchers(:researcher)
    researchers(:jax).project_roles.update_all(project_role: 'Manager')
    ActionItems::Targets::Managers::GetTo.expects(:call).with(action_item: ai).returns(to)
    result = ActionItems::Targets::Managers::GetCc.call(action_item: ai)

    assert_not result.include?(to)
    assert_includes result, researchers(:jax)
  end
end
