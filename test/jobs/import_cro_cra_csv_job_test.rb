require 'test_helper'

class ImportCroCraCsvJobTest < ActiveSupport::TestCase
  test "imports csv" do
    preview = mock()

    CroCra.expects(:import_csv).with(preview: preview)

    ImportCroCraCsvJob.new.perform(preview: preview)
  end

  test 'update existing researcher roles' do
    new_project = projects(:multi_currency_project_pln)
    cro = new_project.cro
    researcher = researchers(:researcher)
    cro.cro_cras.create!(researcher: researcher)
    researcher.project_roles.where(project: new_project).destroy_all

    refute researcher.projects.exists?(new_project.id)

    preview = CroCraUploadPreview.new(cro: cro, data: [[
                                                         researcher.title,
                                                         researcher.first_name,
                                                         researcher.last_name,
                                                         researcher.email,
                                                         researcher.phone_number,
                                                       ]])

    ImportCroCraCsvJob.new.perform(preview: preview)

    assert researcher.projects.exists?(new_project.id)
  end
end
