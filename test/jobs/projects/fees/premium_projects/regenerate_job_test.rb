require 'test_helper'

class Projects::Fees::PremiumProjects::RegenerateJobTest < ActiveJob::TestCase
  test "creates new fee if no fee exists for given month" do
    project = Project.new

    Fees::ChargeForPremiumProject.expects(:call).with(project: project)

    Projects::Fees::PremiumProjects::RegenerateJob.perform_now(project: project)
  end

  test "destroys existsing fee and creates new one" do
    fee = fees(:for_premium_project)
    project = fee.project

    Fees::ChargeForPremiumProject.expects(:call).with(project: project)

    Projects::Fees::PremiumProjects::RegenerateJob.perform_now(project: project)

    assert Fee.where(id: fee.id).none?
  end
end
