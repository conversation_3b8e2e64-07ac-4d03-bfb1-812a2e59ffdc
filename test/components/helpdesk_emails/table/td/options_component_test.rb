require "test_helper"

class HelpdeskEmails::Table::Td::OptionsComponentTest < ViewComponent::TestCase
  setup do
    @researcher = Researcher.new
  end

  test 'New HelpdeskEmail' do
    he = HelpdeskEmail.new(id: 1, status: 'new')

    render_inline(HelpdeskEmails::Table::Td::OptionsComponent.new(helpdesk_email: he, researcher: @researcher))

    assert_equal page.all('a').collect(&:text), ['Check-in', 'Show', 'Add a new comment', 'Set high priority']
  end

  test 'Checked in free Other HelpdeskEmail' do
    he = HelpdeskEmail.new(request_type: 'Email', checked_in_at: Time.new, id: 1, status: 'pending', tag: 'vip', task_category: helpdesk_emails_task_categories(:other))
    he.stubs(:free_plan?).returns(true)

    render_inline(HelpdeskEmails::Table::Td::OptionsComponent.new(helpdesk_email: he, researcher: @researcher))

    assert_equal [
                   'Show',
                   'Reassign',
                   'Reply',
                   'Suspend',
                   'Resolve with notification',
                   'Resolve silently',
                   'Close',
                   'Add a new comment',
                   'Set high priority'
                 ], page.all('a').collect(&:text)

    he.status = 'suspended'
    render_inline(HelpdeskEmails::Table::Td::OptionsComponent.new(helpdesk_email: he, researcher: @researcher))

    assert_equal [
                   "Show",
                   "Reassign",
                   "Reactivate",
                   "Remind",
                   "Close",
                   "Add a new comment",
                   "Set high priority"
                 ], page.all('a').collect(&:text)
  end

  test 'Checked in Forms HelpdeskEmail' do
    he = HelpdeskEmail.new(request_type: 'Form', checked_in_at: Time.new, id: 1, status: 'pending', task_category: helpdesk_emails_task_categories(:forms))

    render_inline(HelpdeskEmails::Table::Td::OptionsComponent.new(helpdesk_email: he, researcher: @researcher))

    assert_includes page.all('a').collect(&:text), 'Register forms'
  end

  test 'request authorization' do
    he = HelpdeskEmail.new(request_type: 'Form', checked_in_at: Time.new, id: 1, status: 'pending', task_category: helpdesk_emails_task_categories(:forms))
    he.from = '<EMAIL>'

    render_inline(HelpdeskEmails::Table::Td::OptionsComponent.new(helpdesk_email: he, researcher: @researcher))

    assert_includes page.all('a').collect(&:text), 'Request authorization'
  end
end
