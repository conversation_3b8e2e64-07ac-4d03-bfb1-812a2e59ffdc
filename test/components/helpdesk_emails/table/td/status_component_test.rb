require "test_helper"

class HelpdeskEmails::Table::Td::StatusComponentTest < ViewComponent::TestCase
  test "New status with CRO operators" do
    cro = contract_research_organizations(:cro)
    email = HelpdeskEmail.new(status: 'new', request_type: 'Form', contract_research_organization: cro)

    render_inline(HelpdeskEmails::Table::Td::StatusComponent.new(helpdesk_email: email))
    assert_selector('p.red', text: 'New')
    assert_text 'MM/HM'
  end

  test "New status with one CRO operator" do
    cro = contract_research_organizations(:cro)
    cro.update_columns secondary_operator_id: nil
    email = HelpdeskEmail.new(status: 'new', request_type: 'Form', contract_research_organization: cro)

    render_inline(HelpdeskEmails::Table::Td::StatusComponent.new(helpdesk_email: email))
    assert_selector('p.red', text: 'New')
    assert_text 'MM/None'
  end

  test "New status without CRO operators" do
    cro = contract_research_organizations(:cro)
    email = HelpdeskEmail.new(status: 'new', request_type: 'Form', contract_research_organization: cro)

    render_inline(HelpdeskEmails::Table::Td::StatusComponent.new(helpdesk_email: email))
    assert_selector('p.red', text: 'New')
    assert_text 'MM/HM'
  end

  test "Pending status" do
    email = HelpdeskEmail.new(status: 'pending')

    render_inline(HelpdeskEmails::Table::Td::StatusComponent.new(helpdesk_email: email))
    assert_selector('p.red', text: 'Pending', count: 0)
    assert_selector('p', text: 'Pending')
  end
end
