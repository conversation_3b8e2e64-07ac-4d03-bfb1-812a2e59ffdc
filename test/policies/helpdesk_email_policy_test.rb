require 'test_helper'

class HelpdeskEmailPolicyTest < ActiveSupport::TestCase
  setup do
    @user = Researcher.new
    @record = HelpdeskEmail.new(status: :new)
  end

  test "create_shipment" do
    assert_not HelpdeskEmailPolicy.new(@user, @record).create_shipment?

    @record.stubs(:operator).returns(@user)

    assert HelpdeskEmailPolicy.new(@user, @record).create_shipment?
  end

  test "create_ticket" do
    assert_not HelpdeskEmailPolicy.new(@user, @record).create_ticket?

    @record.stubs(:operator).returns(@user)

    assert HelpdeskEmailPolicy.new(@user, @record).create_ticket?
  end

  test '#escalate?' do
    he = HelpdeskEmail.new(from: '<EMAIL>')
    assert HelpdeskEmailPolicy.new(@user, he).escalate?

    he.from = researchers(:researcher).email
    refute HelpdeskEmailPolicy.new(@user, he).escalate?

    cu = clinical_users(:clinical_user)
    cu.update_column :email, '<EMAIL>'
    he.from = cu.email
    refute HelpdeskEmailPolicy.new(@user, he).escalate?
  end
end