require 'test_helper'

class ProjectDebitSignaturePolicyTest < ActiveSupport::TestCase
  setup do
    @pds = project_debit_signatures(:signature)
    @researcher = researchers(:jack)
    @pds.project_debit.update! researcher: @researcher
  end

  test "update?" do
    assert_not ProjectDebitSignaturePolicy.new(@researcher, @pds).update?
    assert ProjectDebitSignaturePolicy.new(researchers(:researcher), @pds).update?
  end

  test 'update? post_paid_sign_notes_by_manager_only cro' do
    @pds.project.update! debit_allowed: true
    @pds.contract_research_organization.update! post_paid_sign_notes_by_manager_only: true

    user_with_changed_role = researchers(:researcher)
    assert ProjectDebitSignaturePolicy.new(user_with_changed_role, @pds).update?

    project_roles(:project_role).update! project_role: 'CRA+'
    user_with_changed_role = Researcher.find(user_with_changed_role.id) # Re-instantiate to refresh memoization
    assert_not ProjectDebitSignaturePolicy.new(user_with_changed_role, @pds).update?
  end

  test 'edit?' do
    assert_not ProjectDebitSignaturePolicy.new(@researcher, @pds).edit?
    assert ProjectDebitSignaturePolicy.new(researchers(:researcher), @pds).edit?
  end
end
