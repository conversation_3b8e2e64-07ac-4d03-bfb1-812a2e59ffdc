require 'test_helper'

class AddFundsToPdsFormTest < ActiveSupport::TestCase
  setup do
    @amount = 50
    @pds = project_debit_summaries(:pds)
    @subject = AddFundsToPdsForm.new(project_debit_summary_id: @pds.id, amount: @amount, date: Date.today, source_account_number: '12345')
  end


  test 'is invalid if @pds is paid' do
    @pds.update! state: 'p'
    assert_not @subject.valid?
  end

  test 'is invalid if amount is higher than saldo abs' do
    assert_not @subject.valid?
  end

  test "updates @pds as credit" do
    assert_equal @pds.note_type, ProjectDebitSummary::NORMAL

    @subject.source = 'credit'
    assert @subject.valid?
    @subject.save

    assert_equal @pds.reload.note_type, ProjectDebitSummary::CREDIT
  end

  # test "creates transfer with technical_account" do
  #   project = @pds.project
  #   ta = FactoryBot.create :technical_account, project: project, balance: 100
  #   @subject.source = 'technical_account'
  #   @subject.save

  #   assert_equal ClinicalTransfer.last.source_record, ta
  #   assert_equal ta.reload.balance, 50
  # end

  test "updates note currency account balance and creates internal transfer" do
    @subject.source = 'transfer_for_paid_note'
    @pds.currency_account.update_columns balance: 0

    assert_changes 'InternalTransfer.count' do
      @subject.save
    end

    it = InternalTransfer.last

    assert_equal it.amount, @subject.amount.to_d
    assert_equal it.source.resource, @pds
    assert_equal it.destination.resource, @pds.project
  end

  test "create internal_transfer from cro credit account" do
    cro_credit_acc = currency_accounts(:cro_credit_currency_account)
    pds_acc = @pds.currency_account

    assert_difference 'cro_credit_acc.reload.balance', -@amount do
      assert_no_difference 'pds_acc.reload.balance' do
        assert_difference 'InternalTransfer.count', 2 do
          @subject.source = 'credit'
          @subject.save
        end
      end
    end
  end
end