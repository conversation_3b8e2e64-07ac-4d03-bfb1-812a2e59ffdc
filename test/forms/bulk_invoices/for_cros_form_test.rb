require 'test_helper'

class BulkInvoices::ForCrosFormTest < ActiveSupport::TestCase
  test "call job" do
    ids = [1,2]
    date = Date.today
    nr = 3

    form = BulkInvoices::ForCrosForm.new(
      cro_ids: ids,
      order_number: nr,
      date: date
    )

    BulkInvoices::GenerateForCrosJob.expects(:perform_later).with(
      cro_ids: ids,
      order_number: nr,
      date: date
    )

    form.save
  end
end
