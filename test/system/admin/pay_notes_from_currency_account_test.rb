require 'application_system_test_case'

class Admin::PayNotesFromCurrencyAccountTest < ApplicationSystemTestCase
  setup do
    @admin = admin_users(:admin)
    @transfer = clinical_transfers(:transfer_to_cro_currency_account)
    @pds = project_debit_summaries(:pds)
    @cro_currency_account = currency_accounts(:cro_currency_account)
  end

  test "pay for notes from clinical_transfer view" do
    sign_in @admin
    visit admin_clinical_transfer_path(@transfer)
    click_link 'Zaproponuj zapła<PERSON>'

    assert_changes '@pds.reload.state', from: ProjectDebitSummary::CREATED, to: ProjectDebitSummary::PAID do
      assert_changes '@transfer.reload.balance', from: @transfer.balance, to: @transfer.balance - @pds.saldo.abs do
        click_button 'Opłać'
        sleep 1
      end
    end
  end

  test "pay for note from clinical_transfer view - amount less than note balance" do
    @pds.update_columns saldo: -100
    @transfer.update_columns balance: 90

    sign_in @admin
    visit admin_clinical_transfer_path(@transfer)
    click_link 'Zaproponuj zapła<PERSON>'
    click_link 'Ręcznie'
    check "pds_#{ @pds.id }"

    assert_no_changes '@pds.reload.state' do
      assert_changes '@transfer.reload.balance', from: @transfer.balance, to: 0 do
        click_button 'Opłać'
        assert_text 'Noty zostały opłacone.'
      end
    end
  end

  test "pay for notes from cro currency_account view" do
    sign_in @admin
    visit admin_currency_account_path(@cro_currency_account)
    click_link 'Zaproponuj zapłaty'

    assert_changes '@pds.reload.state', from: ProjectDebitSummary::CREATED, to: ProjectDebitSummary::PAID do
      click_button 'Opłać'
      assert_text 'Noty zostały opłacone.'
    end
  end

  test "pay for notes from project currency_account view" do
    project_currency_account = currency_accounts(:project_currency_account)

    sign_in @admin
    visit admin_currency_account_path(project_currency_account)
    click_link 'Zaproponuj zapłaty'


    assert_changes '@pds.reload.state', from: ProjectDebitSummary::CREATED, to: ProjectDebitSummary::PAID do
      click_button 'Opłać'
      sleep 1
      assert_text 'Noty zostały opłacone.'
    end
  end
end