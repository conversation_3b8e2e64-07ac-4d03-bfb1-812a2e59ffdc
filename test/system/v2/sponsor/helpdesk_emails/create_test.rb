require 'application_system_test_case'

module V2
  module Sponsor
    module HelpdeskEmails
      class CreateTest < ApplicationSystemTestCase
        def setup
          @researcher = researchers(:researcher)
        end

        test 'create helpdesk email from phone conversation' do
          subject = 'HE subject'
          body = 'HE body'

          sign_in researchers(:operator)
          visit v2_sponsor_helpdesk_emails_path
          click_link 'Add a new request'

          execute_script <<-JS
            $('.researcher-select').select2('open')
            $('.select2-input').val('#{@researcher.last_name}').trigger('input')
          JS
          find('.select2-result-label', match: :first).click
          fill_in :helpdesk_email_cc, with: '<EMAIL>'
          fill_in :helpdesk_email_subject, with: subject
          fill_in :helpdesk_email_body, with: body

          assert_difference 'ClinicalMobileMessage.count' do
            assert_emails 1 do
              click_button 'Create'
              assert_text 'New Helpdesk request has been created.'
              assert_text body
            end
          end

          he = HelpdeskEmail.last
          assert_equal he.request_type, 'Phone'
        end

        test 'create internal helpdesk email' do
          subject = 'HE subject'
          body = 'HE body'

          sign_in researchers(:operator)
          visit v2_sponsor_helpdesk_emails_path
          click_link 'Add a new request'

          execute_script <<-JS
            $('.researcher-select').select2('open')
            $('.select2-input').val('#{@researcher.last_name}').trigger('input')
          JS
          find('.select2-result-label', match: :first).click
          fill_in :helpdesk_email_cc, with: '<EMAIL>'
          fill_in :helpdesk_email_subject, with: subject
          fill_in :helpdesk_email_body, with: body
          select_mdb_option(
            text: 'Internal',
            parent_el_selector: '.helpdesk_email_request_type'
          )

          click_button 'Create'
          assert_text 'New Helpdesk request has been created.'
          assert_text body

          he = HelpdeskEmail.last
          assert_equal he.request_type, 'Internal'
        end
      end
    end
  end
end
