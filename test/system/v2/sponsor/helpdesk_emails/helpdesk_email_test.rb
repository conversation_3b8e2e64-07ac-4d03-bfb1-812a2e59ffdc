require 'application_system_test_case'

module V2
  module Sponsor
    module HelpdeskEmails
      class HelpdeskEmailTest < NoJsSystemTestCase
        setup do
          @operator = researchers(:operator)
          @email = helpdesk_emails(:premium_pending_other_helpdesk_email)
          @email.update_columns(task_category_id: helpdesk_emails_task_categories(:forms).id)
          @manual_response = 'manual response'
          sign_in @operator
        end

        test 'suspend' do
          visit v2_sponsor_helpdesk_emails_path

          within "tr#he_#{@email.id}" do
            click_link 'Suspend'
          end

          fill_in 'Response', with: @manual_response

          assert_changes '@email.reload.status', to: 'suspended' do
            assert_emails 1 do
              click_button 'Confirm'

              assert_text 'Request has been suspended.'
              email = ActionMailer::Base.deliveries.last
              assert_includes email.body, @manual_response
            end
          end
        end

        test 'close' do
          visit v2_sponsor_helpdesk_emails_path

          within "tr#he_#{@email.id}" do
            click_link 'Close'
          end

          fill_in 'Response', with: @manual_response

          assert_changes '@email.reload.status', to: 'closed' do
            assert_emails 1 do
              click_button 'Confirm'

              assert_text 'Request has been closed.'
              email = ActionMailer::Base.deliveries.last
              assert_includes email.body, @manual_response
            end
          end
        end

        test 'reactivate' do
          @email.update_columns status: 'suspended'
          visit v2_sponsor_helpdesk_emails_path
          click_link 'All'

          within "tr#he_#{@email.id}" do
            click_link 'Reactivate'
          end

          fill_in 'Response', with: @manual_response

          assert_changes '@email.reload.status', to: 'pending' do
            assert_emails 1 do
              click_button 'Confirm'

              assert_text 'Request has been reactivated.'
              email = ActionMailer::Base.deliveries.last
              assert_includes email.body, @manual_response
            end
          end
        end
      end

      class HelpdeskEmailJsTest < ApplicationSystemTestCase
        setup do
          @operator = researchers(:operator)
          @email = helpdesk_emails(:premium_pending_other_helpdesk_email)
          @email.update_columns(task_category_id: helpdesk_emails_task_categories(:forms).id)
          @manual_response = 'manual response'
          sign_in @operator
        end

        test 'suspend with HelpdeskReplyTemplate' do
          template = helpdesk_reply_templates(:template_1)
          visit v2_sponsor_helpdesk_emails_path

          within "tr#he_#{@email.id}" do
            click_button 'Select'
            click_link 'Suspend'
          end

          fill_in 'Response', with: @manual_response

          assert_no_selector '.reply_template_body'
          assert_no_text template.body

          select_mdb_option(
            text: template.subject,
            parent_el_selector: '#reply_template_id_wrapper'
          )

          assert_selector '.reply_template_body'

          select_mdb_option(
            text: 'Reply templates',
            parent_el_selector: '#reply_template_id_wrapper'
          )

          assert_no_selector '.reply_template_body'

          select_mdb_option(
            text: template.subject,
            parent_el_selector: '#reply_template_id_wrapper'
          )

          assert_selector '.reply_template_body'

          assert_changes '@email.reload.status', to: 'suspended' do
            assert_emails 1 do
              click_button 'Confirm'

              assert_text 'Request has been suspended.'
              email = ActionMailer::Base.deliveries.last
              assert_includes email.body, template.body
              assert_not_includes email.body, @manual_response
            end
          end
        end
      end
    end
  end
end
