require 'application_system_test_case'

module V2
  module Sponsor
    module Researchers
      class OperatorEditsTest < NoJsSystemTestCase
        setup do
          @operator = researchers(:operator)
          @researcher = researchers(:researcher)
        end

        test "send password by operator" do
          sign_in @operator
          visit v2_sponsor_researcher_path(@researcher)


          assert_changes '@researcher.reload.password_changed_at' do
            click_button 'Send password'
          end
        end

        test "send password link not available if researcher does not have phone number" do
          @researcher.update_columns phone_number: nil

          sign_in @operator
          visit v2_sponsor_researcher_path(@researcher)

          assert_selector 'button', text: 'Send password', count: 0
        end

        test "edit researcher by operator" do
          sign_in @operator
          visit v2_sponsor_researcher_path(@researcher)

          click_link 'Edit'
          new_name = 'new_name'
          fill_in 'researcher_first_name', with: new_name

          assert_changes '@researcher.reload.first_name', to: new_name do
            click_button 'Save changes'
          end
        end
      end
    end
  end
end