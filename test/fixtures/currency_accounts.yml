cro_currency_account:
  account_number: "44249010283565000201000001"
  balance: 100
  type: 'CurrencyAccount::ForCro'
  resource: cro (ContractResearchOrganization)
  currency: 'PLN'

cro_eur_currency_account:
  account_number: "44249010283565000201000001"
  balance: 100
  type: 'CurrencyAccount::ForCro'
  resource: cro (ContractResearchOrganization)
  currency: 'EUR'

czk_cro_currency_account:
  account_number: "4424901028356500020100345"
  balance: 100
  type: 'CurrencyAccount::ForCro'
  resource: cro (ContractResearchOrganization)
  currency: 'CZK'

project_currency_account:
  account_number: "90249010283565001511000275"
  balance: 100
  type: 'CurrencyAccount::ForProject'
  resource: project (Project)
  currency: 'PLN'

usd_project_currency_account:
  account_number: "11249010283583099932004625"
  balance: 100
  type: 'CurrencyAccount::ForProject'
  resource: project (Project)
  currency: 'USD'

cra_project_currency_account:
  account_number: "03249000050000453055197781"
  balance: 100
  type: 'CurrencyAccount::ForProject'
  resource: cra_project (Project)
  currency: 'PLN'

note_currency_account:
  account_number: "57249010283565001521001472"
  balance: 100
  type: 'CurrencyAccount::ForNote'
  resource: pds (ProjectDebitSummary)

eur_note_currency_account:
  account_number: "572490102835650131521001472"
  balance: 100
  type: 'CurrencyAccount::ForNote'
  resource: eur_pds (ProjectDebitSummary)

paid_note_currency_account:
  account_number: "57249010283565001521001132"
  balance: 100
  type: 'CurrencyAccount::ForNote'
  resource: debit_pds (ProjectDebitSummary)

prepaid_note_currency_account:
  account_number: "57249010283565001521001498"
  balance: 100
  type: 'CurrencyAccount::ForNote'
  resource: prepaid_pds (ProjectDebitSummary)

unpaid_credit_note_currency_account:
  account_number: "57249010283565001521001666"
  balance: 100
  type: 'CurrencyAccount::ForNote'
  resource: unpaid_credit_pds (ProjectDebitSummary)

patient_currency_account:
  account_number: "48160014753300110000030200"
  balance: 100
  type: 'CurrencyAccount::ForPatient'
  resource: clinical_user (ClinicalUser)

debit_project_currency_account:
  account_number: "90249010283565001511000712"
  balance: 100
  type: 'CurrencyAccount::ForProject'
  resource: debit_project (Project)

cro_credit_currency_account:
  account_number: "44249010283565000201000789"
  balance: 100
  type: 'CurrencyAccount::ForCroCredit'
  resource: cro (ContractResearchOrganization)

eur_cro_credit_currency_account:
  account_number: "44249010283565000201000789"
  balance: 10000
  type: 'CurrencyAccount::ForCroCredit'
  resource: eur_cro (ContractResearchOrganization)
  currency: 'EUR'

eur_cro_currency_account:
  account_number: "44249010283565000201000789"
  balance: 10000
  type: 'CurrencyAccount::ForCro'
  resource: eur_cro (ContractResearchOrganization)
  currency: 'EUR'

main_lmp_pln:
  account_number: '03249000050000453055197781'
  currency: 'PLN'
  type: 'CurrencyAccount::MainLmp'
  balance: 100

prepaid_eur_pds_currency_account:
  resource: prepaid_eur_pds (ProjectDebitSummary)
  balance: 100
  currency: 'PLN'

clinical_center_pln_account:
  resource: clinical_center (ClinicalCenter)
  balance: 100
  currency: 'PLN'

project_sp_note_pln_acc:
  resource: project_sp_note (SitePayments::Note)
  balance: 100
  currency: 'PLN'

sp_note_with_payment_pln_acc:
  resource: sp_note_with_payment (SitePayments::Note)
  balance: 100
  currency: 'PLN'

clinical_center_pln_account:
  resource: clinical_center (ClinicalCenter)
  balance: 100
  currency: 'PLN'
  type: 'CurrencyAccount::ForClinicalCenter'

jax_pln_account:
  resource: jax (Researcher)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForResearcher'
  account_number: '************'

operator_pln_account:
  resource: operator (Researcher)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForResearcher'

researcher_pln_account:
  resource: researcher (Researcher)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForResearcher'

jack_pln_account:
  resource: jack (Researcher)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForResearcher'

max_pln_account:
  resource: max (Researcher)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForResearcher'

jax_pln_bank_currency_account:
  resource: jax_pln_bank_account (BankAccount)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForBankAccount'

premium_project_currency_account:
  resource: premium_project (Project)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForProject'

premium_subject_with_visit_currency_account:
  resource: premium_subject_with_visit (ClinicalUser)
  balance: 0
  currency: 'PLN'
  type: 'CurrencyAccount::ForPatient'

multi_currency_project_account:
  resource: premium_cro (ContractResearchOrganization)
  balance: 10_000
  currency: 'PLN'
  type: 'CurrencyAccount::ForMultiCurrencyProject'
  clinical_protocol_code: 'premium_project_666'