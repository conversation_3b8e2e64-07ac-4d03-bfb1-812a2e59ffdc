pds:
  state: <%= ProjectDebitSummary::CREATED %>
  contract_research_organization: cro
  file_path: 'fixtures/files/test.pdf'
  note_number: '35-0070-0320-0002-0216'
  saldo: -100
  project: project

eur_pds:
  state: <%= ProjectDebitSummary::CREATED %>
  contract_research_organization: eur_cro
  file_path: 'fixtures/files/test.pdf'
  note_number: '66-0070-0320-0002-0216'
  saldo: -100
  project: project

prepaid_eur_pds:
  state: <%= ProjectDebitSummary::CREATED %>
  contract_research_organization: eur_cro
  file_path: 'fixtures/files/test.pdf'
  note_number: '66-0070-0320-0002-0098'
  saldo: -10000
  project: project
  manual_amount: 10000

debit_pds:
  state: <%= ProjectDebitSummary::PAID %>
  contract_research_organization: cro
  file_path: 'fixtures/files/test.pdf'
  note_number: '35-0070-0320-0002-0211'
  saldo: 0
  project: debit_project

prepaid_pds:
  state: <%= ProjectDebitSummary::CREATED %>
  contract_research_organization: cro
  file_path: 'fixtures/files/test.pdf'
  note_number: '35-0070-0320-0002-0214'
  saldo: 0
  project: project
  manual_amount: 100

unpaid_credit_pds:
  state: <%= ProjectDebitSummary::CREATED %>
  contract_research_organization: cro
  file_path: 'fixtures/files/test.pdf'
  note_number: '35-0070-0320-0002-0210'
  saldo: -100
  project: project
  note_type: <%= ProjectDebitSummary::CREDIT %>