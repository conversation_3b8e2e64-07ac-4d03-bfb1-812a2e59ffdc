require 'test_helper'

class SitePayments::PaymentsVisibleToResearcherQueryTest < ActiveSupport::TestCase
  setup do
    @cra_payment = site_payments_payments(:cra_clinical_center_note_payment)
  end

  test "query by Investigator" do
    @cra_payment.update_columns to_researcher_id: researchers(:jack).id

    result = SitePayments::PaymentsVisibleToResearcherQuery.run(
      researcher: researchers(:jack),
      payments: SitePayments::Payment.all,
      role: project_roles(:project_jack_role),
      source: projects(:project)
    )

    assert_includes result, @cra_payment
    assert_not_includes result, site_payments_payments(:processing_payment)
  end

  test "query by Investigator - for site" do
    jack = researchers(:jack)
    @cra_payment.update_columns to_researcher_id: researchers(:jack).id

    result = SitePayments::PaymentsVisibleToResearcherQuery.run(
      researcher: jack,
      payments: SitePayments::Payment.all,
      role: project_roles(:project_jack_role),
      source: clinical_centers(:clinical_center)
    )
    assert_includes result, @cra_payment
    assert_not_includes result, site_payments_payments(:processing_payment)

    jack.clinical_center_roles.where(clinical_center: clinical_centers(:clinical_center)).update_all(sp_admin: true)
    result = SitePayments::PaymentsVisibleToResearcherQuery.run(
      researcher: jack,
      payments: SitePayments::Payment.all,
      role: project_roles(:project_jack_role),
      source: clinical_centers(:clinical_center)
    )
    assert_includes result, @cra_payment
    assert_includes result, site_payments_payments(:processing_payment)
  end

  test "query by Investigator - for project" do
    jack = researchers(:jack)
    @cra_payment.update_columns to_researcher_id: researchers(:jack).id

    result = SitePayments::PaymentsVisibleToResearcherQuery.run(
      researcher: jack,
      payments: SitePayments::Payment.all,
      role: project_roles(:project_jack_role),
      source: projects(:project)
    )
    assert_includes result, @cra_payment
    assert_not_includes result, site_payments_payments(:processing_payment)

    jack.clinical_center_roles.where(clinical_center: clinical_centers(:clinical_center)).update_all(sp_admin: true)
    result = SitePayments::PaymentsVisibleToResearcherQuery.run(
      researcher: jack,
      payments: SitePayments::Payment.all,
      role: project_roles(:project_jack_role),
      source: projects(:project)
    )
    assert_includes result, @cra_payment
    assert_includes result, site_payments_payments(:processing_payment)
  end

  test "query by non-Investigator" do
    result = SitePayments::PaymentsVisibleToResearcherQuery.run(
      researcher: researchers(:researcher),
      payments: SitePayments::Payment.all,
      role: project_roles(:project_role),
      source: projects(:project)
    )

    assert_equal SitePayments::Payment.all, result
  end
end
