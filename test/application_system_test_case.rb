require 'test_helper'
require 'capybara-screenshot/minitest'
require 'capybara/email'

class ApplicationSystemTestCase < ActionDispatch::SystemTestCase
  include Devise::Test::IntegrationHelpers
  include Capybara::Select2
  include Capybara::Email::DSL

  driven_by :selenium, using: :headless_chrome

  def select_mdb_option(text:, parent_el_selector:, index: 0)
    all("#{parent_el_selector} input.select-dropdown")[index].click
    find("#{parent_el_selector} ul.dropdown-content span", text: text).click
  end

  def assert_mdb_option_selected(text:, parent_el_selector:, index: 0)
    dropdown_input = all("#{parent_el_selector} input.select-dropdown")[index]
    selected_text = dropdown_input.value
    
    assert_equal text, selected_text, "Expected '#{text}' to be selected, but found '#{selected_text}'"
  end

  def js_fill_in(selector, value)
    execute_script("$('#{selector}').val('#{value}')")
  end

  def close_date_picker
    find('.picker__button--close').click
  end

  def select_datepicker_date(date:)
    find(".picker__day.picker__day--infocus[aria-label='#{ date.strftime("%d-%m-%Y") }']").click
  end

  def sign_in_researcher(researcher, email: researcher.email, password: 'password')
    visit new_researcher_session_path
    fill_in_researcher_login_form(email: email, password: password)
  end

  def fill_in_researcher_login_form(email:, password:)
    fill_in 'researcher_email', with: email
    fill_in 'researcher_password', with: password
    click_button 'Secure log in'
  end

  # checking the checkbox does not work for some reason
  def check_visit_happened
    page.execute_script('
              var input = document.createElement("input");
              input.setAttribute("type", "hidden");
              input.setAttribute("name", "visit[visit_happened]");
              input.setAttribute("value", "0");
              document.querySelector("form.edit_visit").appendChild(input);
            ')
  end

  def scroll_to_bottom
    page.execute_script('window.scrollTo(0, document.body.scrollHeight)')
  end
end

class NoJsSystemTestCase < ApplicationSystemTestCase
  include Capybara::Screenshot::MiniTestPlugin

  Capybara.save_path = "/tmp/capybara"

  driven_by :rack_test
end
