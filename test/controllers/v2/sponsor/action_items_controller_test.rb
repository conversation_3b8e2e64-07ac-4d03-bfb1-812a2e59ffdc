require 'test_helper'

class V2::Sponsor::ActionItemsControllerTest < ControllerTestCase
  test "CREATE" do
    sign_in researchers(:operator)

    cro = contract_research_organizations(:cro)
    site = cro.clinical_centers.first
    project = site.project

    assert_difference 'ActionItem::Advanced.count' do
      assert_difference 'ActionItem::EscalationResearcher.count' do
        post v2_sponsor_action_items_path(
          action_item_advanced: {
            cro_id: cro.id,
            project_id: project.id,
            resource_type: ActionItem::RESOURCE_TYPES.first,
            resource_id: site.id,
            resource_type: 'ClinicalCenter',
            description: 'description',
            target_researcher_id: researchers(:researcher).id,
            deadline: 1.year.from_now.to_date,
            escalation_researchers_attributes: [
              { researcher_id: researchers(:jack).id }
            ]
          }
        )
      end
    end
  end
end