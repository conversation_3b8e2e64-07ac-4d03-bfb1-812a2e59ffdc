require 'test_helper'

module V2
  module Sponsor
    module ClinicalTransfers
      class ClinicalTransfersAcceptancesControllerTest < ActionDispatch::IntegrationTest
        setup do
          @researcher = researchers(:operator)
          @visit = visits(:success_visit)
          @transfer = clinical_transfers(:visit_transfer)
          @transfer.update_columns state: ClinicalTransfer::WAITING, project_id: @visit.project_id
          @project = @transfer.project
          @km_category = visit_payment_categories(:km_category)
          @transport_category = visit_payment_categories(:transport_category)

          sign_in @researcher
        end

        test 'CREATE - show flash when cost over limit if not manager' do
          @project.update_columns allow_visit_payments_with_costs_above_limits: true
          FactoryBot.create :cost_limit, project_id: @project.id, visit_payment_category_id: @km_category.id, amount: 100
          FactoryBot.create :visit_payment_categorization, visit: @visit, visit_payment_category: @km_category, amount: 1000

          post v2_sponsor_clinical_transfer_clinical_transfers_acceptances_path(@transfer)

          assert_redirected_to root_path
          exp_msg = 'The cost for the "Kilometers" category exceeds the limit. Please contact the manager to approve the payment.'
          assert_equal exp_msg, flash[:alert]
        end

        test 'CREATE - show flash when multiple costs over limit if not manager' do
          @project.update_columns allow_visit_payments_with_costs_above_limits: true
          FactoryBot.create :cost_limit, project_id: @project.id, visit_payment_category_id: @km_category.id, amount: 100
          FactoryBot.create :visit_payment_categorization, visit: @visit, visit_payment_category: @km_category, amount: 1000
          FactoryBot.create :cost_limit, project_id: @project.id, visit_payment_category_id: @transport_category.id, amount: 100
          FactoryBot.create :visit_payment_categorization, visit: @visit, visit_payment_category: @transport_category, amount: 1000

          post v2_sponsor_clinical_transfer_clinical_transfers_acceptances_path(@transfer)

          assert_redirected_to root_path
          exp_msg = 'The cost for the "Kilometers, Transport" categories exceed the limit. Please contact the manager to approve the payment.'
          assert_equal exp_msg, flash[:alert]
        end

        test 'CREATE - allow acceptance when cost over limit if manager' do
          @researcher.project_roles.update_all project_role: 'Manager'
          @project.update_columns allow_visit_payments_with_costs_above_limits: true
          FactoryBot.create :cost_limit, project_id: @project.id, visit_payment_category_id: @km_category.id, amount: 100
          FactoryBot.create :visit_payment_categorization, visit: @visit, visit_payment_category: @km_category, amount: 1000

          ::ClinicalTransfers::AcceptForm.any_instance.stubs(:save).returns(true)

          post v2_sponsor_clinical_transfer_clinical_transfers_acceptances_path(@transfer)

          assert_redirected_to root_path
          assert_equal 'Payment has been approved.', flash[:notice]
        end
      end
    end
  end
end
