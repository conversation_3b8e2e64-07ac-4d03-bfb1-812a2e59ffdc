require 'test_helper'

class ClinicalCenterHelperTest < ActionView::TestCase
  include ApplicationHelper

  setup do
    @project = projects(:project)
    @site = clinical_centers(:clinical_center)
    @researcher = researchers(:researcher)
  end

  test "#site_vip_tag - not noblewell_project" do
    assert_not site_vip_tag(@site, researcher: @researcher).blank?
  end

  test "#site_vip_tag - noblewell_project" do
    @project.update_columns noblewell_project: true

    assert site_vip_tag(@site, researcher: @researcher).blank?
  end

  test "#site_vip_tag - noblewell_project, vip site and researcher is investigator in it" do
    researcher = researchers(:jack)
    @project.update_columns noblewell_project: true

    assert_not site_vip_tag(@site, researcher: researcher).blank?
  end

  test '#clinical_center_no_lead_cra_text_extended - last transfer by CRA' do
    site = clinical_centers(:clinical_center)
    transfer = clinical_transfers(:clinical_transfer)
    researcher = researchers(:jax)
    transfer.update_columns visit_id: visits(:planned_visit).id, researcher_id: researcher.id

    result = clinical_center_no_lead_cra_text_extended(clinical_center: site, name_method: :short_full_name)

    assert_includes result, researcher.short_full_name
  end

  test '#clinical_center_no_lead_cra_text_extended - last transfer not by CRA' do
    site = clinical_centers(:clinical_center)
    transfer = clinical_transfers(:clinical_transfer)
    researcher = researchers(:jack)
    visit = visits(:planned_visit)
    visit.update_columns clinical_user_id: clinical_users(:clinical_user).id
    transfer.update_columns visit_id: visit.id, researcher_id: researcher.id

    result = clinical_center_no_lead_cra_text_extended(clinical_center: site, name_method: :short_full_name)

    assert_not_includes result, researcher.short_full_name
  end
end
