require 'test_helper'

class ZadarmaEventsHelperTest < ActionView::TestCase
  test 'zadarma_event_caller_link' do
    event = zadarma_events(:event_with_project)
    link = zadarma_event_caller_link(event)

    assert_match "#{event.researcher.full_name} (Manager)", link

    cu = clinical_users(:clinical_user)
    event.update_columns(researcher_id: nil, clinical_user_id: cu.id)
    link = zadarma_event_caller_link(event)

    assert_match "Subject #{cu.patient_code}/#{cu.clinical_protocol_code}", link

    event.update_columns(clinical_user_id: nil)
    link = zadarma_event_caller_link(event)

    assert_equal 'Unknown caller', link
  end
end
