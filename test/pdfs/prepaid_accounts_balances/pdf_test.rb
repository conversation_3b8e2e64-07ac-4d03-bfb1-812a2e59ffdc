require 'test_helper'

module PrepaidAccountsBalances
  class PdfTest < ActiveSupport::TestCase
    setup do
      @cs1 = CostSummary.create!(
        project: projects(:project),
        start_date: 1.month.ago.to_date,
        end_date: Date.today,
        end_balance: 1000,
        end_balance_currency: 'EUR'
      )
      @cs2 = CostSummary.create!(
        project: projects(:premium_project),
        start_date: 1.month.ago.to_date,
        end_date: Date.today,
        end_balance: 2000,
        end_balance_currency: 'USD'
      )
      @cost_summaries = [@cs1, @cs2]
      @date = Date.today
    end

    test 'generates PDF with projects grouped by CRO' do
      pdf = PrepaidAccountsBalances::Pdf.new(cost_summaries: @cost_summaries, date: @date)

      text_analysis = PDF::Inspector::Text.analyze(pdf.render).strings
      assert_includes text_analysis, @cs1.project.clinical_protocol_code
      assert_includes text_analysis, @cs2.project.clinical_protocol_code
      assert_equal 3, pdf.page_count # 2 CROs + 1 summary page
    end
  end
end
