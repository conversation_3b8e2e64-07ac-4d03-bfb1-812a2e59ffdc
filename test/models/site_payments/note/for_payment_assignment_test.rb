require 'test_helper'

class SitePayments::Note::ForPaymentAssignmentTest < ActiveSupport::TestCase
  test "not valid if total payment amount higher than note amount" do
    note = site_payments_notes(:project_sp_note).becomes(SitePayments::Note::ForPaymentAssignment)
    note.site_payments_payments.new(amount: 100)

    assert note.valid?

    note.site_payments_payments.new(amount: 1000)

    assert note.invalid?
  end

  test 'at_least_one_payment_required' do
    note = site_payments_notes(:project_sp_note).becomes(SitePayments::Note::ForPaymentAssignment)

    assert note.invalid?

    note.site_payments_payments.new(amount: 100)
    assert note.valid?
  end
end
