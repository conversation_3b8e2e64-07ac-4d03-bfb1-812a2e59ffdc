require 'test_helper'

class InternalTransfer::FromClinicalTransferToCurrencyAccountTest < ActiveSupport::TestCase
  test "create transfers, alior file and update resource balance" do
    source = currency_accounts(:main_lmp_pln)
    destination = currency_accounts(:cro_currency_account)
    transfer = clinical_transfers(:transfer_to_cro_currency_account)
    amount = 50

    assert_difference 'InternalTransfer.count' do
      assert_difference 'ClinicalTransfer.count' do
        assert_difference 'AliorFile.count' do
          assert_difference 'transfer.reload.balance', -amount do
            InternalTransfer::FromClinicalTransferToCurrencyAccount.create!(
              amount: amount,
              destination_currency_account_id: destination.id,
              source_currency_account_id: source.id,
              for_resource: transfer
            )
          end
        end
      end
    end

    ct = ClinicalTransfer.unscoped.last
    assert_equal ct.account_number, destination.account_number
    assert_equal ct.source_acc_nr, source.account_number
  end
end