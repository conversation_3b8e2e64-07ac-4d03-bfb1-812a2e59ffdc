require 'test_helper'

class PatientFormTest < ActiveSupport::TestCase
  setup do
    @patient_form = patient_forms(:patient_form)
  end

  test 'to_int_array' do
    array = @patient_form.to_int_array('clinical_protocol_code_coordinates')
    assert_equal [255, 659], array
  end

  test 'hide available forms after soft delete' do
    cro_form = available_cro_patient_forms(:available_cro_patient_form)
    project_form = available_project_patient_forms(:available_project_patient_form)

    @patient_form.destroy!

    assert_not_nil cro_form.reload.deleted_at
    assert_not_nil project_form.reload.deleted_at
  end
end
