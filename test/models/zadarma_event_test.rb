require 'test_helper'

class ZadarmaEventTest < ActiveSupport::TestCase
  test 'set researcher from caller number' do
    researcher = researchers(:researcher)
    event = ZadarmaEvent.new
    event.caller_number = researcher.phone_number
    event.save!

    assert_equal researchers(:researcher), event.researcher
  end

  test 'set researcher from caller number - number not registered' do
    event = ZadarmaEvent.new
    event.caller_number = '123'
    event.save!

    assert_nil event.researcher
  end

  test '.not_internal' do
    result = ZadarmaEvent.not_internal

    assert_includes result, zadarma_events(:event_with_project)
    assert_not_includes result, zadarma_events(:internal_event)
  end

  test '.call_starts' do
    answer_event = zadarma_events(:event_without_project)
    answer_event.update_columns event: 'NOTIFY_ANSWER'

    result = ZadarmaEvent.call_starts

    assert_includes result, zadarma_events(:event_with_project)
    assert_not_includes result, answer_event
  end

  test 'add + to called number' do
    event = ZadarmaEvent.new
    event.called_number = '48576334818'
    event.save!

    assert_equal '+48576334818', event.called_number

    event = ZadarmaEvent.new
    event.save!

    assert_nil event.called_number
  end

  test 'set clinical user from called number' do
    clinical_user = clinical_users(:clinical_user)
    event = ZadarmaEvent.new
    event.called_number = clinical_user.phone_number
    event.save!

    assert_equal clinical_users(:clinical_user), event.clinical_user
  end
end
