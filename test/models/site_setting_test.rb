require 'test_helper'

class SiteSettingTest < ActiveSupport::TestCase
  test 'validate HE response templates' do
    assert_validation_of(:helpdesk_email_manager_response_email_subject)
    assert_validation_of(:helpdesk_email_manager_response_email_body)
    assert_validation_of(:helpdesk_email_manager_response_sms_body)
    assert_validation_of(:helpdesk_email_cra_response_email_subject)
    assert_validation_of(:helpdesk_email_cra_response_email_body)
    assert_validation_of(:helpdesk_email_cra_response_sms_body)
    assert_validation_of(:helpdesk_email_investigator_response_email_subject)
    assert_validation_of(:helpdesk_email_investigator_response_email_body)
    assert_validation_of(:helpdesk_email_investigator_response_sms_body)
    assert_validation_of(:helpdesk_email_not_registered_response_email_subject)
    assert_validation_of(:helpdesk_email_not_registered_response_email_body)
    assert_validation_of(:helpdesk_email_not_registered_response_sms_body)
  end

  test 'format_helpdesk_message - with nil template' do
    SiteSetting.instance.update! helpdesk_email_manager_response_email_subject: nil

    assert_nil SiteSetting.format_helpdesk_message(HelpdeskEmail.new, :helpdesk_email_manager_response_email_subject)
  end

  test 'format_helpdesk_message - with blank template' do
    SiteSetting.instance.update! helpdesk_email_manager_response_email_subject: ''

    assert_nil SiteSetting.format_helpdesk_message(HelpdeskEmail.new, :helpdesk_email_manager_response_email_subject)
  end

  test 'format_helpdesk_message - with template' do
    SiteSetting.instance.update! helpdesk_email_manager_response_email_subject: "hi {{receipt_id}}"
    receipt_id = 'receipt_id'
    he = HelpdeskEmail.new(receipt_id: receipt_id)

    assert_equal "hi #{receipt_id}", SiteSetting.format_helpdesk_message(he, :helpdesk_email_manager_response_email_subject)
  end

  private

  def assert_validation_of(field)
    assert SiteSetting.new(field => 'hay').valid?
    assert SiteSetting.new(field => '').valid?
    assert SiteSetting.new(field => nil).valid?
    assert SiteSetting.new(field => 'hey {{receipt_id}}').valid?

    ss = SiteSetting.new(field => 'hey {{receipt_id}} on {{wtf}}')
    refute ss.valid?
    assert_includes ss.errors.full_messages.join, 'wtf'
    refute SiteSetting.new(field => 'hey {{wtf}}').valid?
  end
end
