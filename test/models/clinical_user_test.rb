require 'test_helper'

class ClinicalUserTest < ActiveSupport::TestCase
  test "#account_type_setting" do
    assert_equal account_type_settings(:subject_premium_project_setting), clinical_users(:premium_subject_with_visit).account_type_setting
  end

  test 'connected_account_label required for non default account type' do
    parent = clinical_users(:premium_parent_with_visit)

    assert_valid parent

    parent.connected_account_label = nil
    assert_invalid parent

    subject = clinical_users(:premium_subject_with_visit)
    subject .connected_account_label = nil
    assert_valid subject
  end
end
