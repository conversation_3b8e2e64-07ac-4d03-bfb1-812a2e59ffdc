<!doctype html>
<html>
<title>PayClinical.com</title>
<head>
  <meta charset="utf-8">
</head>
<script type="text/javascript">
  document.addEventListener('DOMContentLoaded', function(){
    window.setTimeout(function () {
        location.href = "/";
    }, 5000);
  }, false);

  document.addEventListener('DOMContentLoaded', function(){
    setInterval(oneSecondFunction, 1000);
  }, false);

  function oneSecondFunction() {
    var number = document.getElementById('countdown').innerHTML;
    if (number > 0) {
      document.getElementById('countdown').innerHTML = (parseInt(number) - 1);
    }
  }
</script>
<style>
  body {
    background-image: url("/background_clear.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    background-position-y: inherit;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  }

  html {
    height: 100%;
  }

  img.logo {
    max-width: 100%;
    max-height: 120px;
  }
  span.error_message {
    width: auto;
    color: white;
    font-size: 1.6em;
    font-weight: 100;
    letter-spacing: 1px;
    line-height: 1.3;
    margin-top: 55px;
    text-align: left;
    display: block;
  }

  .container {
    text-align: center;
    position: absolute;
    left: 50%;
    top: 35%;
    transform: translate(-50%, -50%);
    width: 65%;
  }

  span.redirect_message {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    text-align: center;
    color: #e8eefb;
    font-weight: 100;
    font-size: 1.1em;
  }

</style>


<body>
  <div class="container">
    <img src="/payclinical-logo-2.png" class="logo" alt="logo"/>
    <span class="error_message">
      To serve you better, PayClinical.com is currently undergoing maintenance and upgrades. Some functions are temporarily disabled.
      <br>
      <br>
      We apologize for the inconvenience. For any enquiry, please contact our Customer Service at +48 22 376 0000.
    </span>
  </div>
  <span class="redirect_message">Redirect to main page in <span id="countdown">5</span> seconds</span>
</body>
</html>
