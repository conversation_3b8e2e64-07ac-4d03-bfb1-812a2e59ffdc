module Researchers<PERSON><PERSON><PERSON>
  def researcher_project_role_link(researcher, project)
    role = researcher.role_in_project(project)
    link_to 'Edit', v2_sponsor_researcher_project_role_path(researcher, role), class: 'btn btn-primary'
  end

  def researcher_delete_role_link(researcher:, project: nil, project_role: nil)
    project_role = researcher.role_in_project(project) unless project_role
    link_to 'Delete role', v2_sponsor_researcher_project_role_path(researcher, project_role),
    class: 'btn btn-primary', method: :delete, data: { commit: 'Yes', confirm: 'Are you sure you want to delete this role?', original_title: 'Remove role' }
    end

    def send_new_password_to_researcher_link(researcher)
      button_to 'Send new password', v2_sponsor_researcher_password_changes_path(researcher), class: 'btn btn-primary'
    end

    def link_to_project_researchers(project:, researcher:, params: {} )
      v2_sponsor_project_researchers_path(project, params)
    end

    def clinical_user_class(count:, skip_class: false)
      if count > 0 && !skip_class
        return 'reminders_warning'
      end
    end

    def clinical_user_status(clinical_user:, klass: '', cell_class: false, for_pdf: false)
      data_attrs = nil
      link_url = nil
      extra_klass = ""
      if clinical_user.action_required
        cu_status = 'Action required'
        extra_klass = 'protip action_required'
        data_attrs = { 'pt-title' => clinical_user.reason_for_action, 'pt-position' => 'right' }
        link_url = v2_sponsor_clinical_user_visits_path(clinical_user, anchor: "actions_required_table")
      elsif clinical_user.closed
        cu_status = "Closed account"
        extra_klass = "closed_account"
      elsif clinical_user.perm_data == false
        cu_status = "Active account"
      else
        cu_status = "Active account"
      end

      return cu_status if for_pdf

      if cell_class
        return cu_status.downcase.gsub(" ", "_") rescue ''
      else
        if clinical_user.recently_completed_survey?
          cu_status = protip_tooltip(text: 'URGENT', klass: 'premium_tag red', style: 'left: 0; top: 3px;',  tooltip_title: "Subject completed a questionnaire on #{ l(clinical_user.recent_survey_answer.created_at.to_date) }")
        end

        if link_url
          link_to cu_status.html_safe, link_url, class: "#{klass} clinical_user_status row_second_line #{extra_klass}", data: data_attrs
        else
          content_tag :span, class: "#{klass} clinical_user_status row_second_line #{extra_klass}", data: data_attrs do
            cu_status.html_safe
          end
        end
      end
    end

    def link_to_remove_researcher_from(researcher:, clinical_center:, project:, destroying_researcher:)
      ccr = clinical_center.clinical_center_roles.joins(:project_role).where(project_roles: { researcher_id: researcher.id, project_id: project.id }).distinct.last
      return unless ccr
      return unless ClinicalCenterRolePolicy.new(destroying_researcher, ccr).destroy?
      path = v2_sponsor_clinical_center_role_path(ccr)
      link_to 'Remove from site', path, method: :delete, class: 'dropdown-item', id: "delete_ccr_researcher_#{ researcher.id }"
    end

    def link_to_remove_researcher_from_project(researcher:, removing_researcher:, project:)
      if ResearcherPolicy.new(removing_researcher, researcher).edit_invitation?
        link_to 'Remove from study', v2_sponsor_researcher_invitation_removals_path(
          researcher, project_id: project.id), method: :post, class: 'dropdown-item',
        id: "delete_researcher_#{ researcher.id }_project_role_#{ project.id }"
      end
    end

    def value_or_default(value:, default: '-')
      value.present? ? value : default
    end

    def show_managers_link(project:, researcher:)
      if params[:show_managers] == 'true'
        link_to 'Show managers', link_to_project_researchers(project: project, researcher: researcher), class: "default_sm_btn active"
      else
        link_to 'Show managers', link_to_project_researchers(project: project, researcher: researcher, params: { show_managers: true}), class: "default_sm_btn"
      end
    end

    def pdf_save_link(text: 'Save as PDF', path:, btn_class: 'default_sm_btn pdf_btn')
      tooltip_title = "This function will be available soon."

      path = 'javascript:;'
      content_tag :span, class: 'protip', data: { 'pt-title' => tooltip_title, 'pt-position' => 'top' } do
        link_to text, path, class: "#{btn_class} disabled", target: "_blank"
      end
    end

    def confident_manager_info(researcher:, field: 'phone_number', project:)
      target_researcher_pr = researcher.current_role(project)
      current_researcher_pr = current_researcher.current_role(project)

      if ['Investigator', 'Investigator+', 'CTA'].include?(current_researcher_pr) and ['Manager'].include?(target_researcher_pr)
        return "Confidential"
      else
        return researcher.send(field)
      end
    end

    def project_closed_account_tr_title(projects:)
      if projects.any?
        "You have #{ projects.size } closed clinical trial #{ 'account'.pluralize(projects.size) }"
      else
        "You do not have closed clinical trial accounts"
      end
    end

    def manager_name(manager:, researcher_role:)
      if ['Operator', 'CRA', 'CRA+', 'Manager'].include? researcher_role
        manager.full_name
      else
        "#{ manager.first_name.try(:first) }***** #{ manager.last_name.try(:first) }*****"
      end
    end

    def manager_phone(manager:, researcher_role:)
      if ['Operator', 'CRA', 'CRA+', 'Manager'].include? researcher_role
        manager.phone_number
      else
        return unless manager.phone_number
        manager.phone_number.try(:first, 6) + "******"
      end
    end

    def manager_email(manager:, researcher_role:)
      if ['Operator', 'CRA', 'CRA+', 'Manager'].include? researcher_role
        manager.email
      else
        return unless manager.email
        email = manager.email
        split = email.split '@'
        pre_a = split.first
        result = ''
        result = pre_a[0] + ('*' * (pre_a.length - 1))
        result << '@'
        result << split[-1]
        result
      end
    end

    def last_sign_in_ago(researcher:, tooltip: true, custom_class: '')
      if researcher.last_sign_in_at
        if researcher.last_sign_in_at < 1.year.ago
          custom_class = "red"
        end

        content = content_tag :span, class: custom_class, data: { toggle: 'tooltip', title: "Logged in at #{ I18n.l researcher.last_sign_in_at, format: :date_dash_at }"} do
          "#{time_ago_in_words(researcher.last_sign_in_at)} ago"
        end
      else
        content = content_tag :span, "Not activated", class: 'red'
      end

      return content.try(:html_safe)
    end

    def link_to_send_new_password_to_researcher(researcher:, text: 'Send new password by SMS', klass: 'dropdown-item')
      link_to text, v2_sponsor_researcher_password_sendings_path(researcher_id: researcher.id), method: :post, class: klass
    end

    def researcher_link(researcher: nil, placeholder: nil)
      if researcher
        link_to placeholder, [:v2, :sponsor, :payclinical_employees, researcher]
      else
        placeholder
      end
    end
  end
