module ProjectDebitsHelper
  def project_debit_period(project_debit)
    result = ''
    result << I18n.l(project_debit.start_date, format: :only_date_slash).to_s if project_debit.start_date
    result << ' - '
    result << I18n.l(project_debit.completion_date, format: :only_date_slash).to_s if project_debit.completion_date
    result
  end

  def project_debit_amount(project_debit)
    number_to_currency project_debit.clinical_transfers.not_cancelled.sum(:amount).abs, unit: ''
  end

  def project_debit_pdf_download_link(project_debit:)
    pdf_available = project_debit.file_path.present?
    path = pdf_available ? v2_sponsor_project_project_debit_pdfs_path(project_debit.project, project_debit) : 'javascript:;'
    klass = pdf_available ? '' : 'disabled'
    link_to 'Download', path, class: "dropdown-item #{klass}"
  end

  def project_debit_dropdown_links(project_debit:, type: nil)
    researcher_is_manager = current_researcher && current_researcher.has_role_in_project?(project: project_debit.project, role_name: 'Manager')
    links = ''

    if researcher_is_manager && project_debit.started?
      links << link_to('Close immediately', v2_sponsor_project_debit_signatures_path(project_debit_id: project_debit.id, only_close: true), class: 'dropdown-item', method: :post)
    end

    text = if project_debit.zero_balance?
             'Cancel'
           else
             project_debit.started? ? 'Close' : 'Sign electronically'
    end

    unless project_debit.signed_or_paid? || project_debit.cancelled?
      klass = 'dropdown-item'
      disabled = project_debit.started? && (project_debit.free_plan? && !project_debit.vip?)
      disabled = false if (project_debit.completion_date + 1.day).beginning_of_day < Time.current
      disabled = false if researcher_is_manager
      klass << ' disabled' if disabled
      path = disabled ? 'javascript:;' : v2_sponsor_project_debit_signatures_path(
        project_debit_id: project_debit.id,
        only_close: project_debit.started?
      )
      links << link_to(text, path, class: klass, method: :post)
    end

    if project_debit.project_debit_signatures.exists?
      links << link_to('Download', download_pds_pdf_document_v2_sponsor_project_debit_path(project_debit), class: 'dropdown-item')
    end

    unless project_debit.cancelled?
      links << link_to('Show payments', v2_sponsor_project_debit_path(project_debit), class: 'dropdown-item')
    end

    links << link_to('Cancel signature', v2_sponsor_project_debit_signatures_cancelations_path(project_debit), class: 'dropdown-item', method: :post) if project_debit.signed_or_paid? && project_debit.project_debit_summary_id.nil?

    if type == 'other' && project_debit.waiting?
      links << link_to('Send reminder', send_reminder_v2_sponsor_project_debit_path(project_debit), class: 'dropdown-item')
    end
    links << link_to('Cancel all payments', v2_sponsor_project_debit_reset_path(project_debit), class: 'dropdown-item', method: :post) unless project_debit.cancelled? || project_debit.signed_or_paid?
    links.html_safe
  end

  def project_debit_status(project_debit:)
    status = project_debit.status
    status_str = project_debit.status_str
    klass = project_debit.waiting? ? '' : ''
    content_tag :span, status_str, class: klass
  end

  def link_to_project_debits(researcher:)
    path = if !researcher.all_project_debits.exists?
             v2_sponsor_project_debits_path(type: 'other', scope: 'all')
           else
             v2_sponsor_project_debits_path(scope: 'all', type: 'owned')
    end
    link_to 'Batch signatures', path, class: 'waves-effect'
  end

  def pd_signature_date(pd)
    if pd.signed? || pd.paid?
      two_line_date_time(pd.signature_datetime, small_second_line: true)
    else
      content_tag :span, class: ('red' if pd.waiting?).to_s do
        if !pd.vip? && (pd.started? && pd.free_plan? && pd.completion_date)
          "You can sign<br><span class='row_second_line_2'>after #{(pd.completion_date + 1.day).strftime('%d-%m-%Y')}</span>".html_safe
        else
          result = 'Sign anytime'
          if pd.auto_sign_debits && pd.project_debit_signatures.any?
            period = project_debit_signature_auto_sign_period(pd.project_debit_signatures.last)
            result << "<br><span class='row_second_line_2'>Automatically #{period}</span>"
          end
          result.html_safe
        end
      end
    end
  end

  def project_debit_signature_auto_sign_period(project_debit_signature)
    if project_debit_signature.created_at.to_date == project_debit_signature.auto_sign_date
      'today'
    else
      days = (project_debit_signature.auto_sign_date - Date.today).to_i
      "in #{days} #{'day'.pluralize(days)}"
    end
  end
end
