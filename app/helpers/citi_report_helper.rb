module CitiReportHelper
  def transfer_note_number(transfer: t)
    if transfer.get_project.try(:debit_allowed)
      transfer.project_debit.try(:project_debit_summary).try(:note_number) || transfer.project_debit_summary.try(:note_number)
    else
      "pre-paid"
    end
  end

  def pds_paid_date(pds:, end_date: nil)
    if pds.paid?
      latest_date = pds.all_clinical_transfers.max_by(&:created_at).try(:created_at)
      if end_date && latest_date && (latest_date > end_date)
        return 'not_paid'
      else
        return latest_date.try(:strftime, '%d/%m/%y')
      end
    else
      'not paid'
    end
  end
end
