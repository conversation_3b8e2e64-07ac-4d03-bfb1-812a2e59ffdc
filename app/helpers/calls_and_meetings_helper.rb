module CallsAndMeetingsHelper
  def bolden_colons(text)
    lines = []

    text.split("\n").each do |line|
      if line.include?(":") && line.index(':') != 0
        splitted = line.split(":", 2)
        lines << if /\A\s/.match(splitted[0]) || splitted[0].ord == 160 # skip if starts with whitespace
                   line
                 else
                   "<b>#{splitted[0]}:</b>" + splitted[1]
                 end
      else
        lines << line
      end
    end

    lines.join("\n")
  end
end
