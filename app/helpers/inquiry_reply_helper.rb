module Inquiry<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def refund_time_string(inquiry_reply)
    case inquiry_reply.other_info
    when '30'
      'przybliżonym terminie 30 dni'
    when '60'
      'przybliżonym terminie 60 dni'
    else
      'terminie pó<PERSON>niej<PERSON>ym niż 60 dni'
    end
  end

  def refund_time_long_string(inquiry_reply)
    case inquiry_reply.other_info
    when '30'
      'zwrot kosztów nastąpi w  przybliżonym terminie 30 dni'
    when '60'
      'zwrot kosztów nastąpi w przybliżonym terminie 60 dni'
    when '60+'
      'zwrot kosztów nastąpi w terminie późniejszym niż 60 dni'
    else
      'przekażą Państwo odpowiedź badaczowi w ośrodku'
    end
  end

  def notify_user_about_inquiry_reply_sms_text(inquiry_reply)
    clinical_user = inquiry_reply.clinical_user
    if inquiry_reply.reply_to_researcher?
      I18n.t('v2.sms.notify_user_about_inquiry_reply_sms_text.reply_to_researcher',
        locale: :pl,
        clinical_user_title: clinical_user.title_possessive)
    else
      I18n.t('v2.sms.notify_user_about_inquiry_reply_sms_text.reply_to_clinical_user',
        locale: :pl, clinical_user_title: clinical_user.title_possessive,
        clinical_protocol_code: clinical_user.clinical_protocol_code,
        refund_time: refund_time_string(inquiry_reply))
    end
  end

  def researcher_info_string(researcher)
    string = ''
    string << researcher.full_name
    string << ", telefon: #{ researcher.phone_number }" if researcher.phone_number.present?
    string << ", email: #{ researcher.email }"
    string
  end

  def refund_time_long_string_for_researcher(inquiry_reply)
    clinical_user = inquiry_reply.inquiry.clinical_user
    case inquiry_reply.other_info
    when '30'
      "zwrot kosztów dla #{ clinical_user.genderize('pacjenta') } #{ clinical_user.patient_code } nastąpi w  przybliżonym terminie 30 dni"
    when '60'
      "zwrot kosztów dla #{ clinical_user.genderize('pacjenta') } #{ clinical_user.patient_code } nastąpi w przybliżonym terminie 60 dni"
    when '60+'
      "zwrot kosztów dla #{ clinical_user.genderize('pacjenta') } #{ clinical_user.patient_code } nastąpi w terminie późniejszym niż 60 dni"
    else
      'przekażą Państwo odpowiedź badaczowi w ośrodku'
    end
  end
end
