class KnfCreditsPage
  include ActionView::Helpers::Url<PERSON>elper
  include ActionView::Helpers::DateHelper
  include ActionView::Helpers::NumberHelper

  attr_reader :params
  attr_accessor :sort_by_cro

  def initialize(params:, sort_by_cro: false)
    @params = params
    @sort_by_cro = sort_by_cro
  end

  def render_html?
    @render_html ||= params[:render_html] == true
  end

  def date_from
    @date_from ||= begin
      if params[:date_from].present?
        vars = params[:date_from].split(' ').first.split("/")
        date_to_parse = [vars.last, vars.first, vars.second].join("-")
        Date.parse(date_to_parse).beginning_of_day
      else
        1.month.ago.beginning_of_month.beginning_of_day
      end
    end
  end

  def date_to
    @date_to ||= begin
      if params[:date_to].present?
        vars = params[:date_to].split(' ').first.split("/")
        date_to_parse = [vars.last, vars.first, vars.second].join("-")
        Date.parse(date_to_parse).end_of_day
      else
        1.month.ago.end_of_month.end_of_day
      end
    end
  end

  def type
    params[:type]
  end

  def cros
    ContractResearchOrganization.all
  end

  def cro_id
    params[:cro_id]
  end

  def credit_pds
    @credit_pds ||= begin
      result = ProjectDebitSummary.credit_note
      if cro_id.present?
        result = result.where(contract_research_organization_id: cro_id)
      end

      if type == 'unpaid'
        result = result.credit_note_unpaid
      elsif type == 'paid'
        result = result.credit_note_paid
      end

      result = result
        .where(
          "
          (
            credit_paid_at IS NOT NULL
            AND
            (
              credit_paid_at >= :start_date
              AND
              credit_paid_at <= :end_date
            )
          )
          OR
          (
            credited_at IS NOT NULL
            AND
            (
              credited_at >= :start_date
              AND
              credited_at <= :end_date
            )
          )
          ", { start_date: date_from, end_date: date_to }
        ).order('created_at desc')

      if sort_by_cro
        result = result.sort_by { |p| [p.contract_research_organization.try(:name), p.credited_at]}
      end

      result
    end
  end

  def paid_notes
    @paid_notes ||= credit_pds.select { |s| s.credit_paid_at.present? && (date_from..date_to).cover?(s.credit_paid_at) }
  end

  def report_type
    params[:report_type] || 'basic'
  end

  def table_headers
    [
      'Data noty',
      'Numer noty',
      'CRO',
      'Kwota noty',
      'Data skredytowania',
      'Kwota kredytu',
      'Termin płatnosci',
      'Data spłaty',
      'Kwota spłaty kredytu',
      'Żródło',
      'Spłata',
      'Kredyt->spłata'
    ]
  end

  def table_data
    credit_pds.map do |pds|
      [
        link_to_or_text(pds.created_at.to_date, Rails.application.routes.url_helpers.admin_project_debit_summary_path(pds)),
        link_to_or_text(pds.note_number, Rails.application.routes.url_helpers.admin_project_debit_summary_path(pds)),
        (if pds.contract_research_organization
          link_to_or_text(pds.contract_research_organization.name, Rails.application.routes.url_helpers.admin_contract_research_organization_path(pds.contract_research_organization))
        else
          'Brak'
        end),
        link_to_or_text(pds.original_amount, Rails.application.routes.url_helpers.admin_project_debit_summary_path(pds)),
        link_to_or_text(pds.credited_at.try(:to_date) || 'Brak', Rails.application.routes.url_helpers.admin_project_debit_summary_path(pds)),
        pds.original_amount,
        pds.credited_to,
        pds.credit_paid_at,
        pds.credit_paid_at ? pds.original_amount : 0,
        (
          if pds.credit_source_main_acc?
            'G'
          elsif pds.credit_source_credit_capital?
            'K'
          end
        ),
        (
          if pds.credit_payment_type_main_acc?
            'G'
          elsif pds.credit_payment_type_credit_capital?
            'K'
          end
        ),
        (
          if pds.credited_at && pds.credit_paid_at
            distance_of_time_in_words(pds.credited_at, pds.credit_paid_at)
          end
        )
      ]
    end
  end

  def link_to_or_text(text, path)
    if render_html?
      link_to text, path
    else
      text
    end
  end

  def summary_notes
    result = []
    # result << "Kwota kredytow niesplaconych: #{ number_to_currency credit_pds.select { |s| s.credit_paid_at.nil? }.map(&:original_amount).sum }"
    result << "Kwota kredytow splaconych: #{ number_to_currency paid_notes.map(&:original_amount).sum }"
    result << "Kwota lacznie wszystkich kredytow: #{ number_to_currency credit_pds.map(&:original_amount).sum }"

    if report_type == 'detailed'
      result << "Ilość kredytów z konto glowne: #{ credit_pds.select { |s| s.credit_paid_at.nil? && s.credit_source_main_acc? }.size }"
      result << "Kwota kredytów z konto glowne: #{ credit_pds.select { |s| s.credit_paid_at.nil? && s.credit_source_main_acc? }.map(&:original_amount).sum }"
      result << "Ilość kredytów z Wydzielony kapital kredytowy: #{ credit_pds.select { |s| s.credit_paid_at.nil? && s.credit_source_credit_capital? }.size }"
      result << "Kwota kredytów z Wydzielony kapital kredytowy: #{ credit_pds.select { |s| s.credit_paid_at.nil? && s.credit_source_credit_capital? }.map(&:original_amount).sum }"

      result << "Ilość kredytów spłaconych z konto glowne: #{ paid_notes.select { |s| s.credit_payment_type_main_acc? }.size }"
      result << "Kwota kredytów spłaconych z konto glowne: #{ paid_notes.select { |s| s.credit_payment_type_main_acc? }.map(&:original_amount).sum }"
      result << "Ilość kredytów spłaconych z Wydzielony kapital kredytowy: #{ paid_notes.select { |s| s.credit_payment_type_credit_capital? }.size }"
      result << "Kwota kredytów spłaconych z Wydzielony kapital kredytowy: #{ paid_notes.select { |s| s.credit_payment_type_credit_capital? }.map(&:original_amount).sum }"
    end
    result
  end
end