class ResourceStatisticsPage < BaseDataPage
  def type
    params[:type]
  end

  def proportional?
    type == 'proportional'
  end

  def fees_for(resource)
    resource.fees.where(created_at: start_date.beginning_of_month..end_date)
  end

  def new_patients_fees_for(resource)
    fees_for(resource).for_new_patients
  end

  def new_patients_fees_count_for(resource)
    new_patients_fees_for(resource).size
  end

  def new_patients_fees_total_for(resource)
    new_patients_fees_for(resource).sum(:amount)
  end

  def active_patients_fees_for(resource)
    fees_for(resource).for_active_patients
  end

  def active_patients_fees_count_for(resource)
    active_patients_fees_for(resource).size
  end

  def active_patients_fees_total_for(resource)
    active_patients_fees_for(resource).sum(:amount)
  end

  def not_closed_patients_count_for(resource)
    resource.clinical_users.not_closed.where('created_at <= ?', end_date).count
  end

  def closed_patients_count_for(resource)
    resource.clinical_users.closed.where(closed_at: date_range).count
  end

  def paid_visits_for(resource)
    result = resource.visits.paid.transfer_paid_or_grouped.includes(:visit_payment_categorizations)

    if proportional?
      result = result.where(visit_date: date_range)
    else
      result = result.where('clinical_transfers.status_change_date >= ? AND clinical_transfers.status_change_date <= ?', start_date, end_date)
    end

    result
  end

  def paid_visits_count_for(resource)
    paid_visits_for(resource).size
  end

  def paid_visits_total_for(resource)
    paid_visits_for(resource).inject(0) { |sum, v| sum += v.amount }
  end

  def not_closed_patients_count
    resources.inject(0) { |sum, r| sum += not_closed_patients_count_for(r) }
  end

  def closed_patients_count
    resources.inject(0) { |sum, r| sum += closed_patients_count_for(r) }
  end

  def new_patients_fees_size
    resources.inject(0) { |sum, r| sum += new_patients_fees_count_for(r) }
  end

  def new_patients_fees_total
    resources.inject(0) { |sum, r| sum += new_patients_fees_total_for(r) }
  end

  def active_patients_fees_size
    resources.inject(0) { |sum, r| sum += active_patients_fees_count_for(r) }
  end

  def active_patients_fees_total
    resources.inject(0) { |sum, r| sum += active_patients_fees_total_for(r) }
  end

  def paid_visits_count
    resources.inject(0) { |sum, r| sum += paid_visits_count_for(r) }
  end

  def paid_visits_total
    resources.inject(0) { |sum, r| sum += paid_visits_total_for(r) }
  end

  def default_table_headers
    [
      'Utworzenie konta',
      'Utworzenie konta suma',
      'Utrzymanie konta',
      'Utrzymanie konta suma',
      'Liczba wizyt opłaconych',
      'Kwota płatności'
    ]
  end

  def default_table_data_methods
    %i[
      new_patients_fees_count_for
      new_patients_fees_total_for
      active_patients_fees_count_for
      active_patients_fees_total_for
      paid_visits_count_for
      paid_visits_total_for
    ]
  end

  def table_data_for(resource)
    table_data_methods.map do |method|
      send(method, resource)
    end
  end

  def default_table_summary
    [
      'Łącznie',
      new_patients_fees_size,
      new_patients_fees_total,
      active_patients_fees_size,
      active_patients_fees_total,
      paid_visits_count,
      paid_visits_total
    ]
  end
end
