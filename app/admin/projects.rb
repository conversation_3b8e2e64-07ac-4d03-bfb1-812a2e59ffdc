ActiveAdmin.register Project do
  menu priority: 2, label: 'Projekty'
  belongs_to :contract_research_organization, optional: true

  filter :contract_research_organization
  filter :clinical_protocol_code_or_project_code, as: :string

  scope 'Aktywne', :not_closed
  scope 'Bez harmonogramu', :no_template
  scope 'Pre-paid', :not_debit_allowed
  scope 'Debetowe', :debit_allowed
  scope 'Wszystkie', :all

  before_action :redirect_to_invoices, if: proc { |_o| current_admin_user.role == 'accountant' }
  before_action :left_sidebar!

  controller do
    def redirect_to_invoices
      redirect_to admin_project_invoices_path
    end
  end

  Project.class_eval do
    def display_name
      "#{clinical_protocol_code} - #{currency}"
    end
  end

  form do |f|
    if f.object.new_record?
      f.object.set_transfer_limits_from_cro
      f.object.set_debit_allowed_based_on_cro
      f.object.set_fees_from_cro
      f.object.notify_investigator = false
      f.object.notify_cra = false
      f.object.notify_manager = false
      f.object.send_email_after_confirm_cu = false
      f.object.send_app_req = f.object.cro.try(:send_app_req)
      f.object.patient_reactivation_fee = f.object.cro.try(:patient_reactivation_fee)
      f.object.country_name = 'Poland'
      f.object.fee_plan = f.object.cro&.fee_plan
    end

    f.inputs 'Opłaty' do
      f.input :fee_plan
      f.input :fee_plan_cost
      f.input :monthly_project_fee
      f.input :premium_free_to
      f.input :fee_patient_add
      f.input :fee_active_patient
      f.input :tier_1_transfer_fee
      f.input :tier_2_transfer_fee
      f.input :fee_post_transfer
      f.input :patient_reactivation_fee
      f.input :premium_plan_fee
      f.input :super_premium_plan_fee
      f.input :platinum_plan_fee
      f.input :site_fee
      f.input :one_min_rate
      f.input :plan_fee_type
      f.input :citi_transfer_fee
    end

    f.inputs 'Projekt' do
      f.semantic_errors
      f.input :contract_research_organization
      f.input :account_number
      f.input :pir_account_number
      f.input :active_account_number, collection: Project::ACTIVE_ACCOUNT_NUMBER
      f.input :test_project
      f.input :clinical_protocol_code, input_html: { maxlength: 200 }
      f.input :clinical_trial_title
      f.input :name
      f.input :administrator_id, collection: Researcher.all, as: :select
      f.input :accounting_type, as: :select, collection: Project::ACCOUNTING_TYPE_DICT,
                                selected: Project::VISITS
      f.input :saldo, as: :hidden
      f.input :cra_transfer_limit
      f.input :investigator_transfer_limit
      f.input :manager_transfer_limit
      f.input :operator_transfer_limit
      f.input :debit_allowed, as: :select, collection: [['Projekt debetowy', true],
                                                        ['Projekt klasyczny', false]]
      f.input :auto_visits_planning
      f.input :out_of_window_validations
      f.input :amount_treshold_notfication
      f.input :amount_treshold_notification_sent_at
      f.input :amount_treshold_notification_email
      f.input :include_main_payment_categories
      f.input :send_login_report, collection: Project::SEND_LOGIN_REPORT_VALUES
      f.input :visit_amount_difference_alert_limit
      f.input :auto_accept_over_limit_transfer
      f.input :notify_investigator
      f.input :notify_cra
      f.input :notify_manager
      f.input :send_email_after_confirm_cu
      f.input :transfer_limit
      f.input :summary_per_cc
      f.input :po
      f.input :note_po
      f.input :reimbursement_start_date, as: :datepicker
      f.input :country_name, collection: Country::LIST_HASH.keys
      f.input :project_code
      f.input :avatar
      f.input :send_app_req
      f.input :transfer_accept_roles, collection: ContractResearchOrganization::TRANSFER_ACCEPT_ROLES
      f.input :auth_approver_cc
      f.input :online_patient_forms
      f.input :currency, collection: Currency::AVAILABLE
      f.input :site_reimbursement_currency
      f.input :default_ai_owner, collection: ActionItem.owner.values
      f.input :limit_per_patient_for_all_payments
      f.input :low_balance_notification_limit
      f.input :auto_generate_notes_when_balance_low
      f.input :noblewell_project
      f.input :allow_visit_payments_with_costs_above_limits
      f.input :site_cost_rec
      f.input :cra_cost_rec
      f.input :manager_cost_rec
      f.input :auto_credit_debit_summaries
      f.input :future_visits_allowed
      f.input :investigator_info
      f.input :cra_managers_info
    end
    f.actions
  end

  index do
    column 'id' do |p|
      link_to p.id, admin_project_path(p)
    end
    column 'Start' do |p|
      p.created_at
    end
    column 'CRO' do |p|
      if p.contract_research_organization
        link_to p.contract_research_organization.name, admin_contract_research_organization_path(p.contract_research_organization)
      else
        'brak'
      end
    end
    column :name
    column :clinical_protocol_code
    column :saldo do |x|
      format('%.2f', x.real_balance)
    rescue StandardError
      '-'
    end
    column :site_reimbursement_currency
    column :accounting_type do |x|
      Project::ACCOUNTING_TYPE_DICT.select { |_k, v| v == x.accounting_type }.to_a.flatten.first
    end
    column :account_number
    column :pir_account_number
    column :active_account_number
    column 'L. pacjentow / osrodkow / badaczy' do |x|
      "#{x.clinical_users.count} / #{x.clinical_centers.count} / #{x.researchers.count}"
    end
    column 'Typ projektu' do |p|
      p.debit_allowed ? 'post-paid' : 'pre-paid'
    end
    column :invoice_type
    column :test_project
    column :include_main_payment_categories
    column :send_login_report
    column :visit_amount_difference_alert_limit
    column :auto_accept_over_limit_transfer
    column :transfer_limit
    column :summary_per_cc

    actions
  end

  show do |project|
    attributes_table do
      row :project_code
      row :fee_plan do |p|
        link_to p.fee_plan.title, [:admin, p.fee_plan], style: p.premium_plan? ? 'color: red;' : ''
      end
      row :fee_plan_cost
      row :monthly_project_fee
      row :premium_free_to
      row :id
      row :site_reimbursement_currency
      row 'CRO' do |p|
        if p.contract_research_organization
          link_to p.contract_research_organization.name, admin_contract_research_organization_path(p.contract_research_organization)
        else
          'brak'
        end
      end
      row :closed
      row :closed_at
      row :closed_by
      row :account_number
      row :pir_account_number
      row :active_account_number
      row :test_project
      row :name
      row :administrator_id do |project|
        if project.administrator
          link_to project.administrator.full_name, admin_researcher_path(project.administrator.id)
        else
          'brak'
        end
      end
      row :clinical_protocol_code
      row :cra_transfer_limit
      row :investigator_transfer_limit
      row :manager_transfer_limit
      row :operator_transfer_limit
      row 'Typ projektu' do |p|
        p.debit_allowed ? 'post-paid' : 'pre-paid'
      end

      row :saldo do |x|
        format('%.2f', x.real_balance)
      rescue StandardError
        '-'
      end
      row 'Rozliczanie na podstawie' do |p|
        (p.accounting_type == 'a' ? 'Kwot' : 'Wizyt').to_s
      end

      row :company_name
      row :company_street
      row :company_zip_code
      row :company_city
      row :company_nip

      row :auto_visits_planning
      row :out_of_window_validations

      row :amount_treshold_notfication
      row :amount_treshold_notification_sent_at
      row :amount_treshold_notification_email

      row :processing_type
      row :include_main_payment_categories
      row :send_login_report
      row :visit_amount_difference_alert_limit
      row :auto_accept_over_limit_transfer
      row :transfer_limit
      row :send_email_after_confirm_cu
      row :summary_per_cc
      row :po
      row :note_po
      row :reimbursement_start_date
      row :fee_patient_add
      row :fee_active_patient
      row :tier_1_transfer_fee
      row :tier_2_transfer_fee
      row :fee_post_transfer
      row :country_name
      row :patient_reactivation_fee
      row :avatar do |r|
        link_to r.avatar_url, r.avatar_url
      end
      row :send_app_req
      row :transfer_accept_roles
      row :auth_approver_cc
      row :online_patient_forms
      row :created_at
      row :currency
      row :plan_fee_type
      row :default_ai_owner
      row :low_balance_notification_limit
      row :auto_generate_notes_when_balance_low
      row :noblewell_project, as: :checkbox
      row :allow_visit_payments_with_costs_above_limits
      row :site_cost_rec
      row :cra_cost_rec
      row :manager_cost_rec
      row :closed_at
      row :future_visits_allowed
      row :investigator_info
      row :cra_managers_info
    end

    render 'admin/shared/currency_accounts'

    panel 'Formularze online' do
      table_for resource.available_project_patient_forms.order(id: :desc) do |t|
        t.column(:name) { |pf| link_to pf.name, [:admin, pf.patient_form] }
        t.column(:version)
        t.column(:dated)
        t.column('Download form') { |pf| pf.form ? link_to(pf.name, pf.form.url) : '-' }
      end
    end

    panel 'Osoby badające' do
      table_for project.researchers do |t|
        t.column('Imie Nazwisko') { |r| link_to "#{r.first_name} #{r.last_name} (#{r.email})", admin_researcher_path(r.id) }
        t.column('Rola') { |r| link_to r.role_in_project(resource).project_role, admin_project_role_path(r.role_in_project(resource)) }
        t.column('Usuń rolę') { |r| link_to 'Usuń rolę', admin_project_role_path(r.role_in_project(resource), redirect_to: 'project'), method: :delete }
        t.column('Telefon') { |r| r.phone_number.to_s }
        t.column('Email') { |r| r.email.to_s }
      end
    end

    panel 'Ośrodki badawcze' do
      render 'cc_and_cc_roles', project: project
    end

    panel 'Kategorie kosztów' do
      render 'visit_payment_categories', source: resource
    end

    render 'shared/admin_changes_table', resource: resource
    active_admin_comments

    panel 'Noty do oplacenia' do
      table_for resource.project_debit_summaries.not_paid do |t|
        t.column('ID') { |r| link_to r.id, [:admin, r] }
        t.column('Numer noty') { |r| link_to r.note_number, [:admin, r] }
        t.column('Data utworzenia') { |r| link_to r.created_at, [:admin, r] }
        t.column('Kwota') { |r| link_to r.original_amount, [:admin, r] }
      end
    end

    panel 'Ustawienia kont polaczonych' do
      div do
        link_to 'Dodaj ustawienie', new_admin_account_type_setting_path(account_type_setting: { resource_id: resource.id, resource_type: resource.class.name })
      end

      table_for resource.account_type_settings do |t|
        t.column('Typ konta') { |r| link_to r.account_type.name, [:admin, r.account_type] }
        t.column('Tytul przelewu') { |r| link_to r.transfer_title, [:admin, r] }
        t.column('Usuń') { |r| link_to 'Usuń', admin_account_type_setting_path(r), method: :delete }
      end
    end
  end

  collection_action :bookkeeping_reports do
  end

  collection_action :get_bookkeeping_report, method: :post do
    pp = params['project']['time']['period']
    period = Time.new(pp['(1i)'].to_f, pp['(2i)'].to_i, pp['(3i)'].to_i).all_month

    path = BookkeepingReports.gen_reports_for_each_month(period)
    send_file path
  end

  action_item only: :index do
    link_to 'Raporty finansowe', all_projects_financial_reports_admin_projects_path
  end
  collection_action :all_projects_financial_reports do
    @first_project = Project.unscoped.order('created_at ASC').first
    r = @first_project.created_at.to_date..Time.new.to_date
    @moths_range = r.map { |d| Date.new(d.year, d.month, 1) }.uniq
  end
  collection_action :get_all_financial_report, method: :post do
    start_date = Date.parse(params[:project][:created_at])
    f = ProjectReport.get_all_projects_report(start_date, start_date + 1.month - 1.day)
    p f
    send_file f
  end

  collection_action :get_transfers_confirmation, method: :post do
    p params
    locale = params[:project][:locale] || 'pl'
    time_from = Date.parse(params[:project][:time][:time_from].values.join('-')).beginning_of_day
    time_to = Date.parse(params[:project][:time][:time_to].values.join('-')).end_of_day
    if params[:project][:id].eql?('0')
      ct = ClinicalTransfer.outgoing
                           .where('status_change_date between (?) and (?)', time_from, time_to)
    else
      ct = ClinicalTransfer.outgoing
                           .where('status_change_date between (?) and (?)', time_from, time_to)
                           .where(project_id: params[:project][:id])

    end

    r = begin
        Researcher.find(params[:project][:researcher_id])
        rescue StandardError
          nil
      end
    ct = ct.where(researcher_id: r.id) if r

    if ct.empty?
      flash[:warning] = 'Brak przelewów zleconych w tym okresie'
      redirect_to(all_projects_financial_reports_admin_projects_path) && return
    end

    ctr = ClinicalTransfersReport.new(ct)
    f = ctr.gen_transfer_confirmation(current_researcher: r, locale: locale)

    send_file("#{Rails.root}/public/#{f}")
  end

  action_item :action, only: :show do
    link_to 'Typy wizyt', admin_project_visit_types_path(params[:id], order: 'position_asc', scope: 'position>0')
  end

  action_item :action, only: :show do
    link_to 'Szablony wizyt', admin_project_project_visit_templates_path(resource)
  end

  action_item :action, only: :show do
    link_to 'Zestawienia', admin_project_debits_path('q[project_id_eq]' => params[:id])
  end

  action_item :action, only: :show do
    link_to 'Niezatwierdzeni pacjenci',
            admin_project_clinical_users_path(params[:id], 'order' => 'patient_code_asc',
                                                           'q[perm_data_eq]' => '0')
  end

  action_item :action, only: :show do
    link_to 'Dodaj osrodek', controller: 'projects', action: :new_clinical_center
  end
  member_action :new_clinical_center, method: :get do
    @project = Project.find(params[:id])
    @clinical_center = ClinicalCenter.new
    render 'new_clinical_center'
  end
  member_action :create_clinical_center, method: :post do
    ClinicalCenter.transaction do
      @project = Project.find(params[:id])
      @clinical_center = ClinicalCenter.new(params[:clinical_center])
      @clinical_center.country_name = 'Poland'
      if @clinical_center.save
        flash[:notice] = 'Dodano ośrodek do projektu'
        redirect_to admin_project_path(@project)
      else
        render :new_clinical_center
      end
    end
  end

  action_item :action, only: :show do
    link_to 'Raporty finansowe', financial_reports_admin_project_path(params[:id])
  end
  member_action :financial_reports do
    @project = Project.find(params[:id])
    r = @project.created_at.to_date..Time.new.to_date
    @moths_range = r.map { |d| Date.new(d.year, d.month, 1) }.uniq
  end
  member_action :get_financial_report, method: :post do
    p params
    @project = Project.find(params[:id])
    start_date = Date.parse(params[:project][:created_at])
    f = ProjectReport.gen_project_report(@project, start_date, start_date + 1.month - 1.day)
    p f
    send_file f
  end

  action_item :action, only: :show do
    link_to 'Zwróc środki', new_return_funds_admin_project_path(resource)
  end

  member_action :new_return_funds do
    @form = ReturnFundsForm.new(amount: resource.saldo, account_number: resource.cro&.account_number_for_returns)
  end

  member_action :return_funds, method: :post do
    form = ReturnFundsForm.new(params[:return_funds_form])
    if form.save
      redirect_to admin_project_path(resource), notice: 'Środki zostały zwrócone.'
    else
      redirect_to admin_project_path(resource), alert: "Operacja nie powiodła się, błędy: #{form.errors.full_messages}."
    end
  end

  action_item :action, only: :show do
    link_to 'Dodaj pacjenta', new_admin_project_clinical_user_path(params[:id])
  end

  action_item :action, only: :show do
    link_to 'Ośrodki', [:admin, resource, :clinical_centers]
  end

  action_item :action, only: :show do
    link_to 'Pacjenci', admin_project_clinical_users_path(resource, order: 'patient_code_asc')
  end

  action_item :action, only: :show do
    link_to 'Osoby badające', :controller => 'project_roles',
                              :action => 'index', 'q[project_id_eq]' => params[:id].to_s.html_safe
  end

  action_item :action, only: :show do
    link_to 'Transakcje', :controller => 'clinical_transfers',
                          :action => 'index', 'q[project_id_eq]' => params[:id].to_s.html_safe
  end

  action_item :action, only: :show do
    link_to 'Wysłij raport miesięczny >>', new_send_monthly_report_admin_project_path(resource)
  end

  member_action :new_send_monthly_report do
    @mailer = ResearcherMailer.with(project: resource, report_pdfs: [], send_to_admin: false)
    @link_path = [:send_monthly_report, :admin, resource]
    render 'admin/mailer_preview'
  end

  member_action :send_monthly_report do
    GenerateClinicalCentersReportsJob.perform_async(send_to_admin: false, project: resource)
    redirect_to admin_project_path(resource), notice: 'Raport wysłany!'
  end

  # member_action :create_project_visit_template, :method => :post do
  #   begin
  #     ClinicalCenter.transaction do
  #       @project = Project.find(params[:id])
  #       @clinical_center = ClinicalCenter.new(params[:clinical_center])
  #       @clinical_center.save!
  #       flash[:notice] = "Dodano ośrodek do projektu"
  #       redirect_to admin_project_path(@project)
  #     end
  #   rescue Exception => e
  #     flash[:alert] = "Wystąpiły błedy, operacja nie powiodła się."
  #     render :new_clinical_center
  #     p e.backtrace
  #   end
  # end
  action_item only: :index do
    link_to 'Raport ze zmiany ilości badaczy/pacjentów', new_project_personnel_report_admin_projects_path
  end

  collection_action :new_project_personnel_report do
    @form = ProjectPersonnelReportForm.new
  end

  collection_action :create_project_personnel_report, method: :post do
    @report = ProjectPersonnelReportForm.new(params[:project_personnel_report_form]).generate_report
    render xlsx: 'project_personnel_report', filename: "project_personnel_report_#{@report.start_date.strftime('%d-%m-%Y')}_#{@report.end_date.strftime('%d-%m-%Y')}.xlsx"
  end

  action_item :action, only: :show do
    link_to 'Zaproś badacza', new_invite_researcher_admin_project_path(resource)
  end

  member_action :new_invite_researcher do
    @form = InviteResearcherForm.new(project_id: resource.id)
  end

  member_action :create_invite_researcher, method: :post do
    @form = InviteResearcherForm.new(params[:invite_researcher_form])
    if @form.save
      redirect_to admin_researcher_path(@form.researcher), notice: 'Badacz został dodany. Email NIE został wysłany.'
    else
      render :new_invite_researcher, alert: @form.errors.full_messages
    end
  rescue Exception => e
    redirect_back_with_default alert: e.to_s
  end

  action_item :action, only: :show do
    link_to 'Wyślij raporty z osrodkow do admina >>', new_send_clinical_center_reports_admin_project_path(resource)
  end

  member_action :new_send_clinical_center_reports do
    @mailer = ResearcherMailer.with(project: resource, report_pdfs: [], send_to_admin: true).clinical_centers_reports
    @link_path = [:send_clinical_center_reports, :admin, resource]
    render 'admin/mailer_preview'
  end

  member_action :send_clinical_center_reports do
    GenerateClinicalCentersReportsJob.perform_async(project: resource, send_to_admin: true)
    redirect_back_with_default notice: 'Raporty dotra za kilka minut!'
  end

  action_item only: :index do
    link_to 'Wyślij raporty ze wszystkich projektow do admina >>', new_send_all_projects_clinical_center_reports_admin_projects_path
  end

  collection_action :new_send_all_projects_clinical_center_reports do
    @mailer = ResearcherMailer.with(project: Project.first, report_pdfs: []).clinical_centers_reports
    @link_path = %i[send_all_projects_clinical_center_reports admin projects]
    render 'admin/mailer_preview'
  end

  collection_action :send_all_projects_clinical_center_reports do
    GenerateProjectReportsJob.perform_async
    redirect_back_with_default notice: 'Raporty dotra za kilka minut!'
  end

  action_item :action, only: :show do
    link_to 'Faktury', admin_invoices_path(q: { project_id_eq: resource.id })
  end

  action_item :action, only: :show do
    link_to 'Wygeneruj faktury', new_invoice_admin_project_path(resource)
  end

  action_item :action, only: :show do
    link_to 'Wygeneruj faktury(od/do)', new_invoice_from_to_admin_project_path(resource)
  end

  member_action :new_invoice do
    @form = GenerateInvoiceForm.new
  end

  member_action :new_invoice_from_to do
    @form = GenerateInvoiceForm.new
  end

  member_action :generate_invoice, method: :post do
    date = params[:generate_invoice_form].select { |key, _value| key.to_s.match(/^date/) }.map { |_k, v| v }.join('-').to_date
    @form = GenerateInvoiceForm.new(project_id: resource.id, date: date)
    if @form.save
      redirect_to admin_project_invoices_path(q: { project_id_eq: resource.id }), notice: 'Wygenerowano faktury.'
    else
      redirect_back_with_default alert: @form.errors.full_messages.to_sentence
    end
  end

  member_action :generate_invoice_from_to, method: :post do
    user_start_date = params[:generate_invoice_form].select { |key, _value| key.to_s.match(/^user_start_date/) }.map { |_k, v| v }.join('-').to_date
    user_end_date = params[:generate_invoice_form].select { |key, _value| key.to_s.match(/^user_end_date/) }.map { |_k, v| v }.join('-').to_date
    @form = GenerateInvoiceForm.new(project_id: resource.id, user_start_date: user_start_date, user_end_date: user_end_date)
    if @form.save
      redirect_to admin_project_invoices_path(q: { project_id_eq: resource.id }), notice: 'Wygenerowano faktury.'
    else
      redirect_back_with_default alert: @form.errors.full_messages.to_sentence
    end
  end

  action_item :action, only: :show do
    unless resource.debit_allowed
      link_to 'Generuj notę', new_pds_admin_project_path(resource)
    end
  end

  member_action :new_pds do
    @form = ManualPdsForm.new(currency: resource.cro.currency)
  end

  member_action :create_pds, method: :post do
    form_params = params[:manual_pds_form]
    date = form_params.select { |key, _value| key.to_s.match(/^date/) }.map { |_k, v| v }.join('-').to_date
    form_params.delete_if { |k, _v| k.to_s.match(/^date/) }
    form_params[:date] = date
    form = ManualPdsForm.new(form_params)
    if form.save
      redirect_to admin_project_debit_summary_path(form.pds), notice: 'Nota została wygenerowana.'
    else
      redirect_back_with_default alert: form.errors.full_messages.to_sentence
    end
  rescue Exception => e
    puts e
    puts e.backtrace
    ExceptionNotifier.notify_exception(e)
    redirect_back_with_default alert: e.to_s
  end

  member_action :new_cu_for_user do
  end

  action_item :action, only: :show do
    link_to 'Dodaj kategorię płatności', add_vpc_admin_project_path(resource)
  end

  member_action :add_vpc do
    @visit_payment_category = VisitPaymentCategory.new
    render partial: 'admin/contract_research_organizations/add_vpc'
  end

  action_item :action, only: :show do
    if resource.closed
      link_to 'Reaktywuj projekt', reactivate_admin_project_path(resource)
    else
      link_to 'Deaktywuj projekt', new_deactivate_admin_project_path(resource)
    end
  end

  member_action :new_deactivate do
    resource.delete_fees_created_after_closed_at = true
    @clinical_users = resource.clinical_users.with_internal_transfers_positive_amount.order('clinical_users.id desc')
  end

  member_action :deactivate, method: :patch do
    if resource.update(params[:project])
      redirect_to admin_project_path(resource), notice: 'Projekt zostal zamknięty.'
    else
      @clinical_users = resource.clinical_users.with_internal_transfers_positive_amount.order('clinical_users.id desc')
      render :new_deactivate
    end
  end

  member_action :reactivate do
    resource.update closed: false
    redirect_to admin_project_path(resource), notice: 'Projekt zostal reaktywowany.'
  end

  member_action :create_default_visit_template do
    Project.transaction do
      resource.visit_types.where(exceptional: false).each do |vt|
        new_postition = begin
                        (resource.project_visit_templates.last.position + 100)
                        rescue StandardError
                          100
                      end
        pvt = ProjectVisitTemplate.create!(name: vt.name, position: new_postition, project_id: resource.id)
        VisitTemplate.create!(visit_type_id: vt.id, state: 'u', project_visit_template_id: pvt.id)
      end
    end
    redirect_to admin_project_project_visit_templates_path(resource), notice: 'Dodano szablony.'
  rescue Exception => e
    redirect_back_with_default alert: "Wystąpił błąd: #{e}"
  end

  member_action :stretch_position do
    new_postition = 100
    Project.transaction do
      resource.visit_types.where(exceptional: false).each do |vt|
        vt.position = new_postition
        vt.save!
        new_postition += 100
      end
    end
    redirect_back_with_default notice: 'Typy wizyt zostały rozciągnięte co 100.'
  rescue Exception => e
    redirect_back_with_default alert: "Wystąpił błąd: #{e}"
  end

  action_item :action, only: :show do
    link_to 'Pokaz zasilenia', show_incoming_transfers_admin_project_path(resource)
  end

  member_action :show_incoming_transfers do
    @clinical_transfers = ClinicalTransfer
      .incoming
      .where('(project_debit_summary_id IN (?) OR project_debit_id IN (?)) OR project_id = ?', resource.project_debit_summaries.pluck(:id), resource.project_debits.pluck(:id), resource.id)
  end

  action_item :action, only: :show do
    link_to 'Generuj historię transakcji', new_transaction_history_admin_project_path(resource)
  end

  member_action :new_transaction_history do
    @form = ProjectTransactionHistoryPdfForm.new(project_id: resource.id)
  end

  member_action :generate_transaction_history, method: :post do
    start_date = Date.parse(params[:start_date])
    end_date = Date.parse(params[:end_date])
    form = ProjectTransactionHistoryPdfForm.new(project_id: resource.id, start_date: start_date, end_date: end_date)

    if form.save
      if params[:xls].present?
        render xlsx: 'transaction_history', filename: 'bilans.xlsx', disposition: 'inline', locals: { project: resource, transactions: form.transactions }
      else
        send_data form.pdf.render, filename: form.pdf_name, type: 'application/pdf'
      end
    else
      redirect_back_with_default alert: form.errors.full_messages.to_sentence
    end
  end

  action_item :action, only: :show do
    link_to 'Pokaż opłaty', admin_project_fees_path(resource)
  end

  action_item :action, only: :show do
    link_to 'Noty obciążeniowe', admin_project_debit_summaries_path(q: { project_id_eq: resource.id })
  end

  action_item :action, only: :show do
    link_to 'Wyciągi', admin_project_statements_path(q: { project_id_eq: resource.id })
  end

  action_item :action, only: :show do
    link_to 'Formularze pacjentów', admin_project_available_project_patient_forms_path(resource)
  end

  action_item :action, only: :show do
    link_to 'Włącz SMS wszystkim', enable_cu_sms_admin_project_path(resource)
  end

  member_action :enable_cu_sms do
    resource.clinical_users.update_all(sms_notifications: true)
    redirect_back_with_default notice: 'Sms zostały włączone.'
  end

  action_item :action, only: :show do
    link_to 'Wyłącz SMS wszystkim', disable_cu_sms_admin_project_path(resource)
  end

  member_action :disable_cu_sms do
    resource.clinical_users.update_all(sms_notifications: false)
    redirect_back_with_default notice: 'Sms zostały wyłączone.'
  end

  action_item :action, only: :show do
    link_to 'Wyslij powiadomienie o przelewach do akceptacji', [:new_send_waiting_ct_email, :admin, resource]
  end

  member_action :new_send_waiting_ct_email do
    service = SendWaitingTransfersEmailPerProject.new(force_all_transfers: true)
    clinical_centers = service.clinical_centers_with_waiting_transfers.where(id: resource.clinical_centers).distinct
    @service = SendWaitingTransfersEmailPerProject.new(clinical_centers: clinical_centers)
  end

  member_action :send_waiting_ct_email, method: :post do
    service = SendWaitingTransfersEmailPerProject.new(force_all_transfers: true)
    clinical_centers = service.clinical_centers_with_waiting_transfers.where(id: resource.clinical_centers).distinct
    service = SendWaitingTransfersEmailPerProject.new(clinical_centers: clinical_centers, centers_cc: params[:centers_cc])
    service.call
    redirect_to [:admin, resource], notice: "Wysłano #{service.emails_sent} powiadomień."
  end

  action_item :action, only: :show do
    link_to 'Generuj potwierdzenia przelewow', [:new_ct_conf, :admin, resource]
  end

  member_action :new_ct_conf do
    @form = ClinicalCenterPaymentConfirmationsForm.new(date_from: resource.created_at.to_date,
                                                       date_to: Date.today)
  end

  member_action :ct_conf, method: :post do
    @form = ClinicalCenterPaymentConfirmationsForm.new(params[:clinical_center_payment_confirmations_form])
    @form.clinical_user_ids = resource.clinical_users.pluck(:id).map(&:to_s)
    if @form.valid?
      pdf = @form.pdf
      File.open(pdf, 'r') do |f|
        send_data f.read, filename: 'transfer_confirmations.pdf', type: 'application/pdf', disposition: 'inline'
      end
    else
      flash.now[:alert] = @form.errors.full_messages.to_sentence
      render :new_ct_conf
    end
  end

  action_item :action, only: :show do
    link_to 'Report study team >>', [:new_report_study_team, :admin, resource]
  end

  member_action :new_report_study_team do
    @mailer = MatrixClinicalMailer.with(project: resource).report_study_team
    @link_path = [:report_study_team, :admin, resource]
    render 'admin/mailer_preview'
  end

  member_action :report_study_team do
    MatrixClinicalMailer.with(project: resource).report_study_team.deliver
    redirect_to [:admin, resource], notice: 'Raport został wysłany.'
  end

  action_item :action, only: :show do
    link_to 'Pacjenci bez harmonogramu', admin_project_clinical_users_path(resource, scope: 'bezharmonogramu', order: 'patient_code_asc')
  end

  action_item :action, only: :show do
    link_to 'Szkolenia', [:researcher_training, :admin, resource]
  end

  member_action :researcher_training do
  end

  action_item :action, only: :show do
    link_to 'Badacz performance', [:researcher_perf, :admin, resource]
  end

  member_action :researcher_perf do
    researcher_ids = resource.project_roles.except_managers.pluck(:researcher_id)
    @researchers = Researcher.where(id: researcher_ids).page(params[:page]).per(10)
  end

  action_item :action, only: :show do
    link_to 'Miesieczne oplaty', admin_project_fees_path(resource, q: { created_at_gte: Time.new.beginning_of_month, created_at_lte: Time.new.end_of_month })
  end

  action_item :action, only: :show do
    link_to 'Pokaz liste pacjenetow ze sodkami na ich kontach LMP', [:patients_with_lmp_money, :admin, resource]
  end

  member_action :patients_with_lmp_money do
  end

  action_item :action, only: :show do
    link_to 'Statystyki', [:statistics, :admin, resource]
  end

  member_action :statistics do
    @page = ProjectStatisticsPage.new(params: params.merge(project: resource, use_html: !params[:xls].present?))

    if params[:xls].present?
      render xlsx: 'admin/contract_research_organizations/statistics', filename: 'statistics.xlsx', disposition: 'inline', locals: { page: @page }
    end
  end

  action_item :action, only: :show do
    link_to 'Klonuj na inna walute', [:new_clone_diff_currency, :admin, resource]
  end

  member_action :new_clone_diff_currency do
  end

  member_action :clone_diff_currency, method: :patch do
    if params[:project][:currency].blank?
      redirect_back_with_default(alert: 'Wybierz walutę') && return
    end

    if params[:project][:currency] == resource.currency
      redirect_back_with_default(alert: 'Wybierz inną walutę niz obecna') && return
    end

    service = Projects::CloneCurrency.new(project: resource, currency: params[:project][:currency])
    service.call

    redirect_to admin_project_path(service.new_project), notice: 'Projekt zostal sklonowany'
  end

  action_item :action, only: :show do
    link_to 'Premium Study Report >>', [:new_premium_study_report, :admin, resource]
  end

  member_action :new_premium_study_report do
  end

  member_action :premium_study_report, method: :post do
    date = Date.new(params[:project_report]['date(1i)'].to_i, params[:project_report]['date(2i)'].to_i, params[:project_report]['date(3i)'].to_i)
    pdf = Projects::PremiumStudyReportPdf.new(project: resource, start_date: date.beginning_of_month, end_date: date.end_of_month)

    send_data pdf.render, filename: "#{resource.clinical_protocol_code} Report.pdf", type: 'application/pdf', disposition: 'inline'
  end

  action_item :action, only: :show do
    link_to 'Srodki pieniezne w projekcie', [:money, :admin, resource]
  end

  member_action :money do
    @page = SystemBalancePage.new(params: params)

    @project_balance = if resource.debit_allowed
                         @page.normal_note_transfers_total(resource)
                       else
                         @page.project_balance(resource)
    end

    @patients_money = if resource.debit_allowed
                        @page.debit_transfers_without_note_total(resource)
                      else
                        @page.prepaid_transfers_total(resource)
    end
  end

  action_item :action, only: :show do
    link_to "Wyslij XLS z wizytami do #{ClinicalMailer::DJ}", [:generate_xls_with_visits, :admin, resource]
  end

  member_action :generate_xls_with_visits do
    Projects::SendVisitsXlsJob.perform_async(project: resource, context: self)
    redirect_to admin_project_path(resource), notice: 'Email zostanie wkrótce wysłany.'
  end

  action_item :action, only: :show do
    link_to "Zregeneruj opłaty", [:new_regenerate_fees, :admin, resource]
  end

  member_action :new_regenerate_fees do
    @date = 1.month.ago.to_date.strftime('%Y-%m')
  end

  member_action :regenerate_fees, method: :post do
    date = Date.parse("#{ params[:date] }-01")
    Projects::RegenerateFeesJob.perform_later(project: resource, date: date.to_time)

    redirect_back_with_default notice: 'Opłaty zostaną zregenerowane w tle.'
  end

  action_item :action, only: :show do
    if resource.debit_allowed
      link_to 'Zmień na pre-paid', admin_project_path(resource, project: { debit_allowed: false }), method: :put
    else
      link_to 'Zmień na post-paid', admin_project_path(resource, project: { debit_allowed: true }), method: :put
    end
  end

  action_item :action, only: :show do
    link_to 'Raport przelewow do pacjentow', new_patient_transfers_report_admin_project_path(resource)
  end

  member_action :new_patient_transfers_report do
    @start_date = resource.created_at.to_date
    @end_date = Date.today
  end

  member_action :create_patient_transfers_report, method: :post do
    @start_date = Date.parse(params[:start_date])
    @end_date = Date.parse(params[:end_date])

    data = Project::PatientTransfersReportData.new(
      project: resource,
      start_date: @start_date,
      end_date: @end_date
    )
    pdf = Projects::PatientTransfersReportPdf.new(data: data)

    send_data pdf.render, filename: 'report.pdf', type: 'application/pdf', disposition: 'attachment'
  end

  action_item :action, only: :show do
    link_to 'Raporty zarządcze', new_manage_report_admin_project_path(resource)
  end

  member_action :new_manage_report do
  end

  action_item :action, only: :show do
    link_to 'Wygeneruj Zestawienie kosztów', new_cost_summary_admin_project_path(resource)
  end

  member_action :new_cost_summary do
    @cost_summary = CostSummary.new(project: resource)
  end

  member_action :create_cost_summary, method: :post do
    @cost_summary = CostSummary.new(params[:cost_summary])
    @cost_summary.project = resource

    if @cost_summary.save
      redirect_to admin_cost_summary_path(@cost_summary), notice: 'Zestawienie kosztów zostało dodane.'
    else
      render :new_cost_summary
    end
  end
end
