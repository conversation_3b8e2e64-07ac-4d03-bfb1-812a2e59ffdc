ActiveAdmin.register ClinicalUserForm do
  scope :all, default: true
  scope :only_deleted

  belongs_to :researcher, optional: true
  belongs_to :clinical_user, optional: true

  controller do
    def find_resource
      ClinicalUserForm.with_deleted.where(id: params[:id]).first!
    end
  end

  filter :clinical_user
  filter :researcher
  filter :state
  filter :created_at

  index do
    id_column
    column :clinical_user
    column :researcher
    column :file do |f|
      if f.file.present?
        link_to f.file.filename, direct_storage_path(f.file), { target: '_blank' }
      end
    end
    column :state
    column :created_at
  end

  show do
    attributes_table do
      row :id
      row :clinical_user
      row :researcher
      row :state
      row :file do |f|
        f.file.present? ? link_to(f.file.filename, direct_storage_path(f.file)) : '-'
      end
      row :created_at
      row :deleted_at
    end
  end

  form do |f|
    f.semantic_errors
    f.input :clinical_user
    f.input :researcher
    f.input :state
    f.input :file, as: :file
    f.actions
  end

  action_item :action, only: :show do
    if resource.deleted_at
      link_to 'Od<PERSON>ryj', restore_admin_clinical_user_form_path(resource), method: :patch
    else
      link_to '<PERSON><PERSON>ryj', admin_clinical_user_form_path(resource), method: :delete
    end
  end

  member_action :restore, method: :patch do
    resource.restore

    redirect_to [:admin, resource], notice: 'Formularz został odkryty.'
  end
end
