ActiveAdmin.register HelpdeskEmails::TaskCategory do
  menu parent: 'Helpdesk', label: 'Task Categories'

  form do |f|
    f.semantic_errors
    h3 'Dla email_subject, email_body, sms_body moż<PERSON><PERSON> uż<PERSON> {{manual_response}} aby w<PERSON><PERSON><PERSON> reczny tekst do odpowiedzi, np. "Dziękujemy za kontakt. {{manual_response}}".'
    f.inputs
    f.actions
  end

  show do
    attributes_table_for(resource) do
      resource.class.column_names.each do |cn|
        row cn
      end
    end

    panel "Reply templates" do
      table_for HelpdeskReplyTemplate.all do
        column :subject
        column :body
        column :linked do |r|
          resource.reply_templates.include?(r)
        end
        column '' do |r|
          if resource.reply_templates.include?(r)
            link_to 'Unlink', admin_helpdesk_task_category_reply_template_path(resource.helpdesk_task_category_reply_templates.find_by(reply_template: r)), method: :delete
          else
            link_to 'Link', admin_helpdesk_task_category_reply_templates_path(helpdesk_task_category_reply_template: { task_category_id: resource.id, reply_template_id: r.id }), method: :post
          end
        end
      end
    end

    active_admin_comments_for(resource)
  end
end
