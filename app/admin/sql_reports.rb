ActiveAdmin.register SqlReport do
  controller do
    def preview
      @sql = params[:sql_report][:sql]
      @params_text = params[:sql_report][:sql_params] || ''
      sql_executor = SqlExecutor.new(@sql, @params_text)
      @missing_params = sql_executor.missing_params
      @resource = SqlReport.new(params[:sql_report])

      begin
        @results = sql_executor.execute unless @missing_params.any?
      rescue => e
        @error = e.message
        ActiveRecord::Base.connection.execute("ROLLBACK") if Rails.env.test?
      end

      render 'form', layout: "active_admin"
    end

    def create
      if params[:preview]
        preview
      else
        super
      end
    end

    def update
      if params[:preview]
        preview
      else
        super
      end
    end
  end

  form do |f|
    render 'form'
  end
end
