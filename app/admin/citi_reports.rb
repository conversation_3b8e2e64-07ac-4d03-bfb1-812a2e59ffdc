ActiveAdmin.register_page 'Raport Citibank' do
  menu parent: 'Reports'
  content do
    render 'form'
  end

  page_action :generate_report, method: :post do
    year = params["citi_form"]["date(1i)"].to_i
    month = params["citi_form"]["date(2i)"].to_i
    day = params["citi_form"]["date(3i)"].to_i
    @date = Date.new(year, month, day)
    date_all_month = @date.to_time.all_month
    @end_date = @date.to_time.end_of_month
    @start_date = @date.to_time.beginning_of_month.to_date
    @start_datetime = @start_date.to_time.beginning_of_month
    @balance = params[:citi_form][:balance].to_d
    @citi_files = CitiFile.where(created_at: date_all_month)
    @transfers = ClinicalTransfer.where(citi_file_id: @citi_files)
    @pds_transfers = ClinicalTransfer.where('clinical_transfers.project_debit_summary_id IS NOT NULL').where(status_change_date: date_all_month)
    @return_transfers = ClinicalTransfer.where(transfer_type: :return_transfer, status_change_date: date_all_month)
    @all_transfers = @transfers + @pds_transfers + @return_transfers
    @all_transfers = @all_transfers.sort_by(&:status_change_date)

    @prepaid_projects = Project.not_debit_allowed.not_closed.not_test.order('clinical_protocol_code asc')
    @lmp_patients = ClinicalUser.joins(:clinical_transfers).with_internal_transfers_positive_amount.where('clinical_transfers.status_change_date <= ? AND clinical_transfers.state in (?)', @end_date, [ClinicalTransfer::DEBIT_WAITING, ClinicalTransfer::PROCESSING, ClinicalTransfer::SENT_TO_BANK, ClinicalTransfer::BOOKED]).order('patient_code asc').select { |cu| cu.internal_balance_on_day(end_date: @end_date).try(:abs) > 0 }
    @pds = ProjectDebitSummary.where(
      '(created_at BETWEEN :first_day AND :last_day) OR
      (state_changed_at BETWEEN :first_day AND :last_day) OR
      (saldo < 0 AND created_at < :first_day)', {first_day: date_all_month.first, last_day: date_all_month.last}).order('id asc')

    @cros = @all_transfers.map { |t| t.try(:cro) }.flatten.compact.uniq

    date_range = "#{date_all_month.first.strftime("%d/%m/%y")}-#{date_all_month.last.strftime("%d/%m/%y")}"
    render :xlsx => "citi_report", :filename => "raport_citibank_#{ date_range }.xlsx", :disposition => "inline"
  end
end
