ActiveAdmin.register ChangeLog do
  menu label: "Log zmian"

  form do |f|
    f.inputs "Changelog" do
      f.object.date = f.object.date || Date.today
      f.input :date
      f.input :version
      f.input :description
      f.input :sent
    end
    f.actions
  end

  action_item :action, only: :index do
    link_to "Wyslij nowe (#{ ChangeLog.not_sent.count }) do supermanagerow >>", new_send_new_admin_change_logs_path
  end

  collection_action :new_send_new do
    @mailer = ChangeLogMailer.with(change_logs: ChangeLog.not_sent, researcher: CroRole.first.researcher).send_new_to_super_managers
    @link_path = send_new_admin_change_logs_path
    render 'admin/mailer_preview'
  end

  collection_action :send_new do
    logs = ChangeLog.not_sent

    CroRole.find_each do |cr|
      ChangeLogMailer.with(change_logs: logs, researcher: cr.researcher).send_new_to_super_managers.deliver_now
    end
    logs.update_all(sent: true)

    redirect_to admin_change_logs_path, notice: 'Wpisy zostaly wyslane.'
  end

  action_item :action, only: :index do
    link_to "Wyslij <NAME_EMAIL>", send_to_admin_admin_change_logs_path
  end

  collection_action :send_to_admin do
    researcher = Researcher.find_by email: '<EMAIL>'
    ChangeLogMailer.with(change_logs: ChangeLog.not_sent, researcher: researcher).send_new_to_super_managers.deliver_now

    redirect_to admin_change_logs_path, notice: 'Wpisy zostaly wylsane do admina.'
  end
end
