ActiveAdmin.register ProjectStatement do
  menu false

  index do
    id_column
    column :project
    column :file_path do |x|
      link_to File.basename(x.file_path), "/#{x.file_path}" if x.file_path
    end
    column :month
    column :year
  end

  show do
    attributes_table do
      row :id
      row :project
      row :file_path do |x|
        link_to File.basename(x.file_path), "/#{x.file_path}" if x.file_path
      end
      row :month
      row :year
    end
  end
end