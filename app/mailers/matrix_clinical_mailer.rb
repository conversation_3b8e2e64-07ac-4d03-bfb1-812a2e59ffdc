# -*- encoding : utf-8 -*-
class MatrixClinicalMailer < ActionMailer::Base
  include ClinicalMailerHelper
  default from: "<EMAIL>"
  helper(ClinicalMailerHelper)

#<EMAIL>

  def successfully_credited_pir_files
    data_tab = params.fetch :data_tab
    @transfers_count = data_tab[0]
    @transfers_sum = data_tab[1]
    @transfers_data = data_tab[2]

    mail(
         to: ClinicalMailer::DREAM_TEAM,
         bcc: ['<EMAIL>'],
         subject: 'ZwrotKosztow: Podsumowanie PIR.')
  end

  def pir_funding_failure
    @simp_file = params.fetch :simp_file
    mail(to: '<EMAIL>',
         subject: 'ZwrotKosztow: Nieudane zasilenie konta poprzez PIR.')

  end


  def financial_summary
    @waiting_clinical = params.fetch :waiting_clinical
    if @waiting_clinical.size > 0
      mail(:to => ["<EMAIL>", "<EMAIL>"],
           :subject => "[Clinical] Liczba przelewow gotowych do wyciagniecia do citi: #{@waiting_clinical.size}")
    else
      mail(:to => ["<EMAIL>"],
           :subject => "[Clinical] Liczba przelewow gotowych do wyciagniecia do citi: #{@waiting_clinical.size}")
    end
  end

  def notify_ct_with_unverified_patients
    @unconfirmed_patient_transfers = params.fetch :transfers, ClinicalTransfer.unconfirmed_patient
    mail_to = params[:mail_to]
    if @unconfirmed_patient_transfers.size > 0
      mail(:to => mail_to || ["<EMAIL>", "<EMAIL>"],
           :subject => "[Clinical] Liczba przelewow oczekujacych na potwierdzenie pacjentow: #{@unconfirmed_patient_transfers.size}")
    else
      mail(:to => mail_to || ["<EMAIL>"],
           :subject => "[Clinical] Liczba przelewow oczekujacych na potwierdzenie pacjentow: #{@unconfirmed_patient_transfers.size}")
    end
  end

  def waiting_for_acceptance
    @hash = params.fetch :hash
    @to = params.fetch :to
    mail(:to => ClinicalMailer::DREAM_TEAM, :subject => "[Clinical] Przelewy oczekujące na akceptacje.")
  end

  def bank_code_violation
    @clinical_transfer = params.fetch :clinical_transfer
    @clincial_user = @clinical_transfer.clinical_user
    @researcher = @clinical_transfer.researcher
    mail(:to => "<EMAIL>",
      :subject => "[ZwrotKosztów] Próba zlecenia przelewu (przez Matrix) z niewłaściwym kodem bezpieczeństwa.")
  end

  def empty_account_notification
    @project = params.fetch :project
    @cost = params.fetch :cost
    @to = params.fetch :to
    mail(:to => "<EMAIL>",  :subject => "[Clinical] Informacja o niskim saldzie projektu.")
  end

  def send_postal_transfers_info_to_accounting
    today = Date.today
    @transfers = params.fetch :transfers
    @transfers.group_by { |t| t.clinical_user }.each do |cu, transfers|
      pdf = PostalTransfersSummaryPdf.new(transfers: transfers, clinical_user: cu)
      attachments["#{ cu.first_name }_#{ cu.last_name }_#{ today.strftime('%d_%m_%Y') }.pdf"] =  { :mime_type => 'application/pdf', :content => pdf.render }
    end
    mail(to: "<EMAIL>", subject: "[ZwrotKosztów] Oczekujące przekazy pocztowe")
  end

  def invalid_simp_amount
    simp_file_id = params[:simp_file_id]
    @simp_file = simp_file_id ? SimpFile.find(simp_file_id) : nil
    @project_debit = params.fetch :project_debit
    mail(to: '<EMAIL>',
         subject: "[ZwrotKosztów] Nieprawidłowa kwota w SIMP zasilająca zestawienie")
  end

  def invalid_pds_simp_amount
    simp_file_id = params.fetch :simp_file_id
    @simp_file = simp_file_id ? SimpFile.find(simp_file_id) : nil
    @pds = params.fetch :pds
    mail(to: '<EMAIL>',
         subject: "[ZwrotKosztów] Nieprawidłowa kwota w SIMP zasilająca notę")
  end

  def unresolved_transfers_report
    @transfers = params.fetch :transfers
    mail to: '<EMAIL>', subject: 'Nierozstrzygniete przelewy w ZK'
  end

  def pir_transactions_not_matching_db_acc_nr
    @account_numbers = params.fetch :unfound_acc_nr
    mail to: ClinicalMailer::DREAM_TEAM, subject: 'Numery kont z PIR nie znajdują się w bazie danych ZK'
  end

  def send_ordered_post_transfers_list
    @post_transfers = PostTransfer.ordered
    return if @post_transfers.empty?
    mail to: PRIORITY, subject: 'Przekazy pocztowe oczekujące na wysłanie'
  end

  def send_no_login_researchers_report
    @researchers = params.fetch :researchers, []
    mail to: params.fetch(:send_to, ClinicalMailer::ADMIN),
         subject: "PC: lista badaczy ktorzy nie logowali sie przez co najmniej 6 miesiecy"
  end

  def report_study_team
    @project = params.fetch :project
    mail to: ClinicalMailer::DJ,
         subject: "STUDY TEAM: #{ @project.clinical_protocol_code } clinical trial"
  end

  def exception_researcher_notification
    @researcher = params.fetch :researcher
    @error_text = params.fetch :error_text, ''
    mail to: ClinicalMailer::ADMIN,
         subject: "PC #{ Rails.env } - wystąpił błąd wywołany przez badacza #{@researcher.id}"
  end
end
