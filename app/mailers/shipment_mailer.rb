class ShipmentMailer < ClinicalMailer
  layout 'researcher_mailer_layout'

  def cro_shipment_report
    @cro = params.fetch :cro
    @shipments = params.fetch :shipments
    @researcher = Researcher.find_by_email(@cro.shipment_report_email)
    @stop_date = params.fetch :stop_date
    @start_date = params.fetch :start_date
    mail to: @cro.shipment_report_email,
    cc: @cro.shipment_report_email_cc,
    subject: "SHIPMENTS: Received for #{@cro.name} clinical trials from #{l @start_date, format: :date_year_first} to #{l @stop_date, format: :date_year_first}"
  end
end
