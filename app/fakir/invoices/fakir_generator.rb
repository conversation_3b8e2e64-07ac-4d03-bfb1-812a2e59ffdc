module Invoices::<PERSON><PERSON>rGenerator
  extend ActiveSupport::<PERSON><PERSON><PERSON><PERSON>

  def self.generate_nag(invoices)
    invoices.map do |inv|
      "#{ inv.id };#{ inv.fakir_kod_kontrahenta };#{ inv.number };#{ inv.created_for_to.strftime('%d-%m-%Y') };#{ inv.created_for_to.strftime('%d-%m-%Y') };;#{ inv.payment_till.strftime('%d-%m-%Y') };#{ inv.fakir_invoice_description };;0;FVS;FVS;;0;"
    end.join("\n")
  end

  def self.generate_poz(invoices)
    invoices.select { |i| i.gross_total_amount > 0 }.map do |inv|
      Invoices::GeneratePoz.call(invoice: inv)
    end.join("\n")
  end

  def self.generate_kontrah(invoices)
    invoices.group_by(&:contract_research_organization_id).map do |cro_id, invoices|
      inv = invoices.first
      "#{ inv.cro_country_code };#{ inv.cro_company_name };#{ inv.cro_company_name };1;1;#{ inv.cro_company_nip };;1;#{ inv.fakir_kod_kontrahenta };#{ inv.cro_company_zip_code };#{ inv.cro_company_city };#{ inv.cro_company_street };;;"
    end.join("\n")
  end

  def self.generate_vat(invoices)
    invoices.map do |inv|
      foreign_cro = inv.contract_research_organization.foreign?
      date_for_currency_rate = inv.created_for_to - 1.day

      total_for_transfer_fees = inv.projects.map { |p| inv.report.converted_total_transfer_fees_for_project(p) }.sum || 0
      total_net = inv.projects.map { |p| inv.report.total_vattable_fees_for_project_converted_to_pln(p).round(2) }.sum || 0
      total_vat = inv.projects.map { |p| inv.report.total_vat_for_project(p).round(2) }.sum || 0
      total = inv.gross_total_amount - total_for_transfer_fees

      if foreign_cro && inv.invoice_currency != 'PLN'
        converter_total_net = inv.report.convert_to_pln(amount: total_net, currency_rate_date: date_for_currency_rate, currency: 'PLN', base_currency: inv.invoice_currency)
        converter_total_vat = inv.report.convert_to_pln(amount: total_vat, currency_rate_date: date_for_currency_rate, currency: 'PLN', base_currency: inv.invoice_currency)
        converter_total = inv.report.convert_to_pln(amount: total, currency_rate_date: date_for_currency_rate, currency: 'PLN', base_currency: inv.invoice_currency)

        result = "NP;#{ inv.id };S;#{ inv.fakir_vat_category };#{ inv.fakir_kod_kontrahenta };#{ inv.cro_company_name };#{ inv.cro_company_zip_code } #{ inv.cro_company_city } #{ inv.cro_company_street };#{ inv.cro_company_nip };#{ formatted_number(converter_total_net) };#{ formatted_number(converter_total) };#{ formatted_number(converter_total_vat) };#{ inv.created_for_from.month };#{ inv.created_for_from.year };0;0;0;;;;;#{ formatted_number(total_net) };#{ formatted_number(total) };#{ formatted_number(total_vat)};#{inv.invoice_currency};0;0;0;;"
      else
        result = "#{ inv.fakir_vat_amount };#{ inv.id };S;#{ inv.fakir_vat_category };#{ inv.fakir_kod_kontrahenta };#{ inv.cro_company_name };#{ inv.cro_company_zip_code } #{ inv.cro_company_city } #{ inv.cro_company_street };#{ inv.cro_company_nip };#{ formatted_number(total_net) };#{ formatted_number(total) };#{ formatted_number(total_vat) };#{ inv.created_for_from.month };#{ inv.created_for_from.year };0;0;0;;;;;0;0;0;;0;0;0;;"

        if !foreign_cro
          vat_amount = inv.all_fees.for_transfers.sum(:amount)
          report = inv.report

          converted_vat = if inv.is_a? BulkInvoice
            report.projects_with_fees.map { |p| report.converted_total_transfer_fees_for_project(p) }.compact.sum
          else
            report.convert_to_pln(vat_amount, currency_rate_date: date_for_currency_rate)
          end

          if converted_vat > 0
            result << "\nZW;#{ inv.id };S;#{ inv.fakir_vat_category };#{ inv.fakir_kod_kontrahenta };#{ inv.cro_company_name };#{ inv.cro_company_zip_code } #{ inv.cro_company_city } #{ inv.cro_company_street };#{ inv.cro_company_nip };#{ formatted_number(converted_vat) };#{ formatted_number(converted_vat) };0;#{ inv.created_for_from.month };#{ inv.created_for_from.year };0;0;0;;;;;0;0;0;;0;0;0;;"
          end
        end
      end

      result
    end.join("\n")
  end

  def self.formatted_number(number)
    number_to_currency number, strip_insignificant_zeros: true, format: '%n', delimiter: ''
  end
end
