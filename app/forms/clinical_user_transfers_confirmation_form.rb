class ClinicalUserTransfersConfirmationForm
  extend ActiveModel::Naming
  include ActiveModel::Conversion
  include ActiveModel::Validations

  def initialize(params = {})
    params.each do |k, v|
      instance_variable_set("@#{ k }", v)
    end
  end

  attr_accessor :start_date, :end_date, :clinical_user_id, :pdf, :locale

  validates :start_date, :end_date, :clinical_user_id, presence: true
  validate :transfers_present

  def persisted?
    false
  end

  def save
    if valid?
      persist!
      true
    else
      false
    end
  end

  private

  def persist!
    file = ClinicalTransfersReport.new(transfers).gen_transfer_confirmation(locale: locale)
    ClinicalMailer.with(file: file, clinical_user_id: clinical_user_id).send_clinical_user_transfers.deliver
  end

  def transfers
    ClinicalTransfer.where(state: ClinicalTransfer::SENT_TO_BANK, clinical_user_id: clinical_user_id).
    where('clinical_transfers.status_change_date between (?) and (?)', start_date, end_date).reorder('status_change_date ASC')
  end

  def transfers_present
    unless transfers.any?
      errors.add(:base, 'brak przelewów w danym terminie')
    end
  end
end
