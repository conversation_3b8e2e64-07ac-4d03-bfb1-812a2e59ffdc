class ProjectTransactionHistoryPdfForm < BaseFormObject
  attr_accessor :start_date, :end_date, :project_id, :pdf

  validates :start_date, :end_date, :project_id, presence: true

  def default_start_date
    project.created_at.to_date
  end

  def default_end_date
    Date.today
  end

  def project
    Project.find(project_id)
  end

  def file_name
    "#{project.clinical_protocol_code} - historia transakcji [#{start_date.strftime('%d.%m.%Y')}-#{end_date.strftime('%d.%m.%Y')}]"
  end

  def pdf_name
    "#{file_name}.pdf"
  end

  def xls_name
    "#{file_name}.xlsx"
  end

  def transactions
    ProjectTransfers.call(project_id: project_id, date_range: start_date..end_date)
  end

  private

  def persist!
    @pdf = ProjectTransactionHistoryPdf.new(start_date: start_date, end_date: end_date, project_id: project_id)
  end
end
