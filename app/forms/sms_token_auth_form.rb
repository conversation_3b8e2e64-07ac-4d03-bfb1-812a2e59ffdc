class SmsTokenAuthForm < BaseFormObject
  attr_accessor :sms_token, :researcher, :confirmation_path

  def check
    researcher.authenticate_otp(sms_token, drift: 120)
  end

  def send_token_via_sms(force: false)
    if force
      msg = "PAYCLINICAL authorization code: #{researcher.otp_code}"
      phone_number = researcher.phone_number
      SmsApi.new(phone_number: phone_number, msg: msg).call
    else
      researcher.send_sms("PAYCLINICAL authorization code: #{researcher.otp_code}", priority: true)
    end
  end

end
