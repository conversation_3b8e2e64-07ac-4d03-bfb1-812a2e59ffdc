class ManualPdsForm < BaseFormObject::V2
  attr_accessor :pds

  attribute :currency, :string, default: 'PLN'
  attribute :amount, :decimal
  attribute :date, :date
  attribute :project_id, :integer
  attribute :currency_account_id, :integer

  private

  def persist!
    @pds = ProjectDebitSummaries::Manual::Create.call(
      project: project,
      amount: amount,
      currency: currency,
      date: date,
      currency_account_id: currency_account_id
    )
  end

  def project
    Project.find project_id
  end
end
