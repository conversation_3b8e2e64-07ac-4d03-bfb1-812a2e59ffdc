class CancelProjectDebitSignatureForm < BaseFormObject
  attr_accessor :project_debit

  validate :project_debit_must_have_signed_signatures
  validate :project_debit_must_be_signed
  validate :project_debit_summary_not_generated

  private

  def persist!
    ProjectDebit.transaction do
      cancel_signatures
      open_project_debit
    end
  end

  def cancel_signatures
    signatures.each(&:unsign)
  end

  def open_project_debit
    project_debit.update!(status: ProjectDebit::STARTED)
  end

  def project_debit_must_have_signed_signatures
    unless signatures.exists?
      errors.add(:base, 'signatures not found')
    end
  end

  def signatures
    project_debit.project_debit_signatures.signed
  end

  def project_debit_must_be_signed
    unless project_debit.signed_or_paid?
      errors.add(:project_debit, 'is not signed')
    end
  end

  def project_debit_summary_not_generated
    if project_debit.project_debit_summary_id
      errors.add(:project_debit, 'summary note has been generated')
    end
  end
end
