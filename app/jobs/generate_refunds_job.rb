class GenerateRefundsJob
  include SuckerPunch::Job

  def perform(admin_user_id:, transfers: ClinicalTransfer.returns_ready_for_processing)
    ClinicalTransfer.transaction do
      result = CitiFile.send_funds(CitiFile::FULL_FILE_PATH, admin_user_id, 0, transfers)
      if result && result.is_a?(Hash)
        if !result[:records].nil?
          msg_part = result[:obj] ? result[:obj].report_message : nil

          message = if result[:bad_sec_code_ids].size > 0
                      "UWAGA! ID przelewów z błednym kodem bezpieczeństwa: #{result[:bad_sec_code_ids]}. " + msg_part
                    else
                      msg_part
                    end
        else
          message = "Brak przelewów gotowych do wygenerowania."
        end

        if result[:obj]
          result[:obj].update_column :report, message
        end

        AdminMailer.with(message: message, citi_file: result[:obj]).citi_generated.deliver
      end
    end
  end
end
