
<!DOCTYPE html>
<!--[if lt IE 7]><html class="no-js lt-ie9 lt-ie8 lt-ie7" lang="en"> <![endif]-->
<!--[if (IE 7)&!(IEMobile)]><html class="no-js lt-ie9 lt-ie8" lang="en"><![endif]-->
<!--[if (IE 8)&!(IEMobile)]><html class="no-js lt-ie9" lang="en"><![endif]-->
<!--[if (IE 9)]><html class="no-js ie9" lang="en"><![endif]-->
<!--[if gt IE 8]><!--> <html lang="en-US"> <!--<![endif]-->
<head>

<!-- Meta Tags -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />



<meta name="description" content="" />

<title><%= t('app_title') %></title>
<link rel="icon" type="image/png" href="/assets/favicon.ico"/>
<%= stylesheet_link_tag    :chakra, :media => "all" %>
<%= javascript_include_tag :jqueries %>
<%= javascript_include_tag :chakra %>

  <%= csrf_meta_tags %>


  <%= yield :head %>

<%= yield :script %>

<%= yield :style %>



<!-- Mobile Specifics -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="HandheldFriendly" content="true"/>
<meta name="MobileOptimized" content="320"/>

<!-- Mobile Internet Explorer ClearType Technology -->
<!--[if IEMobile]>  <meta http-equiv="cleartype" content="on">  <![endif]-->




<!-- Fav Icon -->
<link rel="shortcut icon" href="#">

<link rel="apple-touch-icon" href="#">
<link rel="apple-touch-icon" sizes="114x114" href="#">
<link rel="apple-touch-icon" sizes="72x72" href="#">
<link rel="apple-touch-icon" sizes="144x144" href="#">

  <style>
    @import url(https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,800,700,600,300&subset=latin,latin-ext);
    #home-slider h2{
        display: inline-block;
        margin: 0 0 0px 0;
        padding: 16px;
        background: #000000;
        background: rgba(0, 0, 0, 0.6);
        line-height: 1em;
        color: #f3f3f3;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
        font-size: 50px;
        font-weight: 300;
        border-bottom: solid 1px #0085c8;
    }


    .login-form{
        width: 450px;
        position: absolute;
        right: 50px;
        top: 60px;
    }

    .login-form.left{
        width: 450px;
        position: absolute;
        left: -100px;
        right: auto;
        top: 60px;
    }


    .login-form.form-horizontal .control-group, .login-form.form-horizontal .form-actions{
        margin-top: 5px;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
  </style>
</head>


<body>



<!-- Homepage Slider -->
<div id="home-slider">


    	<div class="row">
           <span class="12">
                 <%= render :partial => "shared/flash", :locals => { :flash => flash } %>

             <%= yield 'login-wrap' %>

           </span>
        </div>


</div>


<!-- End Homepage Slider -->

<!-- Header -->

<%= yield :header %>

<!-- End Header -->
<%= yield :content %>


<!-- Contact Section -->
<div id="contact" class="page">
<div class="container">
    <!-- Title Page -->
    <div class="row">
        <div class="span12">
            <div class="title-page">
                <h2 class="title"><%= t('.contact') %></h2>
              <h3></h3>

                <h3 class="title-description"><%= t('.contact_text1') %></h3>

                <div class="page-description">
                  <p>
                    <%= t('.contact_text2') %>
                  </p>
                </div>
            </div>
        </div>
    </div>
    <!-- End Title Page -->

    <!-- Contact Form -->
    <div class="row">
    	<div class="span6">

        <%= form_for Feedback.new, :url => feedback_path, html:{id: "contact-form", class: "contact-form"}, remote: true do |form| %>

            <p class="contact-name">
                <input id="contact_name" type="text" placeholder="<%= t('.first_last_name') %>" value="" name="feedback[name]" />
            </p>
            <p class="contact-email">
                <input id="contact_email" type="text" placeholder="<%= t('.email_addr') %>" value="" name="feedback[email]" />
            </p>
            <p class="contact-message">
                <textarea id="contact_message" placeholder="<%= t('.msg') %>" name="feedback[msg]" rows="5" cols="40"></textarea>
            </p>
            <p class="contact-submit">
                <%= form.submit t('.question_send'), id: "contact-submit", class: "submit" %>
            </p>

            <div id="response" style="color:#FF7B3A">
            </div>
        <% end %>

        </div>

        <div class="span3">
        	<div class="contact-details">

        		<h3><%= t('.address_data') %></h3>
                <ul>


                  <li><strong>Let Me Pay Sp. z o.o.</strong> <br>

                    Wiśniowy Business Park  <br>
                  ul. Iłżecka 26             <br>
                  02-135 Warszawa           </li>
                  <li>Telefon: +48 22 376 0000<br>
                    Fax: +48 22 376 0001</li>
                  <li><a href="mailto:<EMAIL>"><EMAIL></a></li>



                </ul>
            </div>
        </div>
    </div>
    <!-- End Services Form -->
</div>
</div>
<!-- End Contact Section -->


<!-- Footer -->
<footer>
  <p style="margin: 0; padding-top: 10px; margin-left: 20px; text-align: left;">
    <%= yield :linkToOther %>
    <a class="coockies-policy-btn zone-anchor" href='#' ><%= t(".cookies_policy") %></a>

  </p>
  <p  class="credits" style="margin-top: 0px;">
    <%= t('.cookies_info') %>
  </p>

  <p class="credits" style="padding-top: 0;">
    <%= t('.spzoo_credits') %>
  </p>

</footer>

<div id="cookies-modal" style="display:none" class="modal hide">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>Polityka Cookies</h3>
  </div>
  <div class="modal-body">

    <h4>Cookies</h4>
    <p style="">Pliki cookies, "ciasteczka" są to niewielkie informacje tekstowe,
      wysyłane przez serwer WWW i zapisywane po stronie użytkownika (zazwyczaj
      na twardym dysku). Domyślne parametry ciasteczek pozwalają na odczytanie
      informacji w nich zawartych jedynie serwerowi, który je utworzył.
      Ciasteczka są stosowane najczęściej w przypadku stron wymagających
      logowania, reklam i do monitorowania aktywności odwiedzających,
      liczników, sond oraz sklepów internetowych.  </p>

    <p style="">Strona www.letmepay.com do poprawnego działania wykorzystuje pliki cookie.</p>

    <h4 style="">Jakiego rodzaju plików cookies używamy?</h4>

    <ul style="">
      <li>Ciasteczka sesji, które w serwisie www.letmepay.com są wymagane do
        utrzymania poprawnej wymiany informacji pomiędzy serwerem i przeglądarką
        a tym samym umożliwiają poprawne wyświetlenie zawartości odwiedzanej
        strony i korzystania z funkcjonalności w niej zawartych. Dzięki nim,
        możliwe jest logowanie się do serwisu.</li>

      <li>Ciasteczka trwałe to pliki, które zostają zapisane na urządzeniu, z
        którego korzysta Użytkownik, nawet po opuszczeniu przeglądanej strony.
        Dzięki temu możliwe jest zapamiętanie wcześniej wybranych ustawień i
        preferencji.</li>

      <li>Ciasteczka podmiotów zewnętrznych, z którymi operator serwisu
        www.letmepay.com współpracuje, umożliwiają firmom zewnętrznym
        przechowywanie informacji dotyczących liczby odwiedzin oraz zachowania
        użytkowników na stronach internetowych. W zawartości tych ciasteczek nie
        są zbierane dane osobowe. Partnerami serwisu są Facebook (okienko z
        Likes) oraz Google (usługa Google Analytics).</li>
    </ul>

    <p style="">Informacje na temat zmiany ustawień dotyczących ciasteczek w
      poszczególnych przeglądarkach dostępne są na poniższych stronach:</p>

    <ul style="">
      <li><a href="http://support.google.com/chrome/bin/answer.py?hl=pl&answer=95647" style="text-decoration: underline;"> w przeglądarce Chrome</a>
      </li>

      <li><a href="http://support.mozilla.org/pl/kb/W%C5%82%C4%85czanie%20i%20wy%C5%82%C4%85czanie%20obs%C5%82ugi%20ciasteczek"  style="text-decoration: underline;">w przeglądarce Firefox</a></li>

      <li><a href="http://support.apple.com/kb/PH5042"  style="text-decoration: underline;">w przeglądarce Safari</a></li>

      <li><a href="http://windows.microsoft.com/pl-pl/windows7/block-enable-or-allow-cookies" style="text-decoration: underline;">w przęglądarce Internet Explorer</a></li>

      <li><a href="http://help.opera.com/Windows/12.10/pl/cookies.html" style="text-decoration: underline;">w przeglądarce Opera</a></li>
    </ul>



  </div>
  <div class="modal-footer">
    <a href="#" class="btn" data-dismiss="modal">Zamknij</a>
  </div>
</div>

<script>
    $(function(){
        $('.coockies-policy-btn').click(function(){
            $('#cookies-modal').modal('show');
            return false;
        });
    });

</script>


</body>
</html>
