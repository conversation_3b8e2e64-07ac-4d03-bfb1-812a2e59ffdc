<!DOCTYPE html>
<!--[if lt IE 7]><html class="no-js lt-ie9 lt-ie8 lt-ie7" lang="en"> <![endif]-->
<!--[if (IE 7)&!(IEMobile)]><html class="no-js lt-ie9 lt-ie8" lang="en"><![endif]-->
<!--[if (IE 8)&!(IEMobile)]><html class="no-js lt-ie9" lang="en"><![endif]-->
<!--[if (IE 9)]><html class="no-js ie9" lang="en"><![endif]-->
<!--[if gt IE 8]><!--> <html lang="en-US"> <!--<![endif]-->

<head>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'UA-108011033-1');
    gtag('set', {'content_group1': 'Subject'});
  </script>

  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <title><%= t('app_title') %></title>
  <link rel="icon" type="image/png" href="/assets/favicon.ico"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="//cdn.jsdelivr.net/npm/mutationobserver-shim/dist/mutationobserver.min.js"></script>

  <%= stylesheet_link_tag    :subject, :media => "all" %>
  <%= javascript_include_tag 'subject' %>
  <%= csrf_meta_tags %>
  <%= yield :script %>

  <!-- Le HTML5 shim, for IE6-8 support of HTML5 elements -->
  <!--[if lt IE 9]>
  <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->

<%= yield :head %>
<link rel="stylesheet" href="//gitcdn.link/repo/DoclerLabs/Protip/master/protip.min.css">
</head>

<body class='<%= "authenticated subject patient_#{controller_name} #{action_name} #{'ie' if browser.ie? or browser.edge? } sign_in_active"%>' >
  <div id="clinical_user_id" data-clinical-user-id="<%= current_clinical_user.try(:id) %>"></div>
  <%= render 'layouts/subject/navigation' %>
  <div class="alert-messages" style="padding-top: 60px;">
    <%= render 'layouts/messages' %>
  </div>
  <div class="container" style="">
    <main>
      <%= content_for?(:content) ? yield(:content) : yield %>
    </main>
  </div>
  <div class="mt-4"></div>
  <footer class="page-footer user-section-footer">
    <%= render 'layouts/footer_copyright_section_subject', link_url: v2_subject_visits_path %>
  </footer>
  <%= yield :body_end %>

  <%= javascript_include_tag 'mdb' %>
  <script src="//gitcdn.link/repo/DoclerLabs/Protip/master/protip.min.js"></script>
</body>
</html>
