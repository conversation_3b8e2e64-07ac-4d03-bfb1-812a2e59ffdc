- pds = @signature
- transfers = pds.clinical_transfers
- if pds.project_debit.start_date and pds.project_debit.completion_date
  - period = "#{I18n.l pds.project_debit.start_date, format: :only_date_slash} - #{I18n.l pds.project_debit.completion_date, format: :only_date_slash}"
- else
  - period = ""
- transfers = transfers || []
- transfers_amount = "%.2f" % transfers.inject(0) { |sum, x| sum + x.amount }
- if @remind
  = raw t('emails.to_researchers.notify_quintiles_cra_seperate.remind')
- else
  = raw t('emails.to_researchers.notify_quintiles_cra_seperate.not_remind')
= raw t('emails.to_researchers.notify_quintiles_cra_seperate.body',
        clinical_protocol_code: pds.project_debit.project.clinical_protocol_code,
        clinical_trial_title: pds.project_debit.project.clinical_trial_title,
        sponsor: pds.project_debit.project.name,
        site_info: "#{pds.clinical_center.clinical_center_code} (#{pds.clinical_center.city})",
        period: period,
        transfers_count: transfers.count,
        full_note_number: pds.project_debit.full_note_number,
        transfers_amount: transfers_amount,)
br
= render partial: "shared/mailers/mailer_button", locals: { btn_text: t('emails.to_researchers.notify_quintiles_cra_seperate.link'), btn_url: edit_v2_sponsor_project_debit_signature_url(pds.signature_hash) }
