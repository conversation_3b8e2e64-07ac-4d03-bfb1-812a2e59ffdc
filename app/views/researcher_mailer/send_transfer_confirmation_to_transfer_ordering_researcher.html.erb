<%= I18n.t('emails.to_researchers.send_transfer_confirmation_to_transfer_ordering_researcher.body', date: I18n.l(Time.new, format: :only_date_dash), cc_code: @cc_code, protocol_code: @protocol_code, locale: @researcher.locale ) %></br>
<br>
Number of payments: <%= @transfers.size %><br>
Total amount: <%= number_to_currency @transfers.sum(&:amount), unit: @transfers.first.try(:currency) %><br>
<br>
<%= I18n.t('emails.to_researchers.send_transfer_confirmation_to_transfer_ordering_researcher.disable_info') %><br>
<%= link_to  I18n.t('emails.to_researchers.send_transfer_confirmation_to_transfer_ordering_researcher.disable_link'), v2_sponsor_disable_transfer_sent_confirmation_email_url %>
<br>
