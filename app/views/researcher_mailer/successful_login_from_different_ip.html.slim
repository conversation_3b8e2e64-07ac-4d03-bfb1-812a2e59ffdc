= raw I18n.t('emails.to_researchers.successful_login_from_different_ip.body_html', location: @location, day: @day, hour: @hour, change_password_link_html: link_to(I18n.t('emails.to_researchers.successful_login_from_different_ip.change_password_link_html'), new_v2_sponsor_account_lock_and_password_change_url(lock_token: @researcher.lock_token)),
block_account_link_html: link_to(I18n.t('emails.to_researchers.successful_login_from_different_ip.block_account_link_html'), new_v2_sponsor_account_lock_url(lock_token: @researcher.lock_token)),
disable_notifications_link_html: link_to(I18n.t('emails.to_researchers.successful_login_from_different_ip.disable_notifications_link_html'), v2_sponsor_email_notifications_settings_url))
