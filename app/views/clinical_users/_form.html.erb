<%= form_for(@clinical_user) do |f| %>
  <% if @clinical_user.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(@clinical_user.errors.count, "error") %> prohibited this clinical_user from being saved:</h2>

      <ul>
      <% @clinical_user.errors.full_messages.each do |msg| %>
        <li><%= msg %></li>
      <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= f.label :clinical_center_id %><br />
    <%= f.number_field :clinical_center_id %>
  </div>
  <div class="field">
    <%= f.label :project_id %><br />
    <%= f.number_field :project_id %>
  </div>
  <div class="field">
    <%= f.label :account_number %><br />
    <%= f.text_field :account_number %>
  </div>
  <div class="field">
    <%= f.label :city %><br />
    <%= f.text_field :city %>
  </div>
  <div class="field">
    <%= f.label :first_name %><br />
    <%= f.text_field :first_name %>
  </div>
  <div class="field">
    <%= f.label :last_name %><br />
    <%= f.text_field :last_name %>
  </div>
  <div class="field">
    <%= f.label :phone_number %><br />
    <%= f.text_field :phone_number %>
  </div>
  <div class="field">
    <%= f.label :patient_code %><br />
    <%= f.text_field :patient_code %>
  </div>
  <div class="field">
    <%= f.label :street %><br />
    <%= f.text_field :street %>
  </div>
  <div class="field">
    <%= f.label :zip_code %><br />
    <%= f.text_field :zip_code %>
  </div>
  <div class="field">
    <%= f.label :sex %><br />
    <%= f.text_field :sex %>
  </div>
  <div class="actions">
    <%= f.submit %>
  </div>
<% end %>
