- attendees_and_receivers_only = local_assigns[:attendees_and_receivers_only] || false
- attendees_from_previous_cm = local_assigns[:attendees_from_previous_cm] || ''


= simple_form_for call_meeting, url: url, method: method do |f|
  - unless attendees_and_receivers_only
    - if !policy(:calls_and_meetings).supermanager_access?
      = f.input :contract_research_organization_id, collection: ContractResearchOrganization.all.sort_by(&:name), input_html: { class: 'mdb-select md-12' }, include_blank: "Select", label: 'CRO'
      = f.input :cme_type, collection: CallsAndMeeting::CME_TYPES, input_html: { class: 'mdb-select md-12' }, include_blank: "Select", label: 'Type'
      = f.input :title
    = f.input :date_part, as: :string, input_html: { class: 'datepicker', value: call_meeting.date_and_time ? call_meeting.date_and_time.strftime("%d-%m-%Y") : nil }, label: 'Date'
    = f.input :time_part, as: :string, input_html: { class: 'timepicker', value: call_meeting.date_and_time ? call_meeting.date_and_time.strftime("%H:%M") : nil }, label: 'Time'

    h3.table-header.mb-0.mt-3 style=("font-size: 1.2em")
      | Agenda
    = f.input :agenda, as: :trix_editor, label: false, input_html: { class: "trix-200 trix-hide-file-buttons trix-hide-heading-button" }

    h3.table-header.mb-0.mt-3 style=("font-size: 1.2em")
      | Meeting minutes
    = f.input :meeting_minutes, as: :trix_editor, label: false, input_html: { class: "trix-200 trix-hide-file-buttons trix-hide-heading-button" }

    .mt-3

  - if !policy(:calls_and_meetings).supermanager_access?
    - if call_meeting.persisted?
      h3#attendees.table-header.mb-0 style="font-size: 1.2em; text-transform: none !important;"
        | Participants
        = link_to "Copy from previous #{call_meeting.cme_type.downcase}", '#', onclick: 'event.preventDefault(); copyAttendeesFromPreviousCM()', class: "default_sm_btn ml-1", style: "font-size: 0.5em; margin-bottom: 10px;"
      = f.input :attendees, as: :string, input_html: { data: { receivers: @receivers.map(&:full_name) } }, label: false
      h3.table-header.mb-0.mt-2 style="font-size: 1.2em; text-transform: none !important;" Mail to
      = f.input :main_receiver, as: :select, collection: @receivers, label_method: :name_and_email, value_method: :email, label: false
      h3.table-header.mb-0.mt-1 style="font-size: 1.2em; text-transform: none !important;" CC
      = f.input :receivers, as: :string, input_html: { data: { receivers: @receivers.map(&:name_and_email) } }, label: false

  .mt-3
  - if call_meeting.persisted?
    = f.submit "Save", class: 'submit_btn mt-right-btn'
  - else call_meeting.persisted?
    = f.submit "Next", class: 'submit_btn mt-right-btn'
  = link_to_back(text: "Back")



javascript:
  $('.timepicker').on('mousedown', event => event.preventDefault() )

  const attendees = $('#calls_and_meeting_attendees')
  attendees.select2({
    width: '100%',
    tags: attendees.data('receivers'),
  })

  const main_receiver = $('#calls_and_meeting_main_receiver')
  main_receiver.select2({
    width: '100%',
  })

  const receivers = $('#calls_and_meeting_receivers')
  receivers.select2({
    width: '100%',
    tags: receivers.data('receivers'),
  })

  function copyAttendeesFromPreviousCM() {
    $('#calls_and_meeting_attendees').val( "#{ raw attendees_from_previous_cm }" )
    $('#calls_and_meeting_attendees').trigger('change')
  }
