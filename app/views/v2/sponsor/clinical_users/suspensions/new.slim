.row
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Close account for subject# #{@clinical_user.patient_code}", subtitle: "#{ link_to_cc(clinical_center: @clinical_user.clinical_center) } Protocol# #{ red_project_link(project: @clinical_user.project) }")

.tab-content.vertical_1.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      - if @clinical_user.has_processing_transfers?
        .summary_section.default.mt-0
          p There is #{ number_to_currency @clinical_user.processing_transfers.sum(:amount) } on this custody account.
      - elsif @clinical_user.clinical_transfers.not_done.exists?
        .summary_section.default.mt-0
          - ct_size = @clinical_user.clinical_transfers.not_done.size
          - is_are = ct_size > 1 ? 'are' : 'is'
          - payments_inf = ct_size > 1 ? 'payments' : 'payment'
          p There #{ is_are } #{ ct_size } #{ payments_inf } in process.
      
      = simple_form_for @clinical_user, url: v2_sponsor_clinical_user_suspension_path(@clinical_user), method: :put do |f|
        = f.input :closed_reason, label: 'Reason'
        = f.submit 'Close account', class: 'submit_btn'
        = link_to_back(text: 'Back', link_class: 'ml-1 btn btn-warning')
