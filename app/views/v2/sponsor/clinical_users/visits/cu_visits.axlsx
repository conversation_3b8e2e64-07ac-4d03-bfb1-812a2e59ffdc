
wb = xlsx_package.workbook

wb.add_worksheet(name: "Visits") do |sheet|
  sheet.add_row ['Date', 'Visit name', 'Status', 'Amount', '', 'Recent activity']
  @visits.each do |v|
    sheet.add_row [v.visit_date ? I18n.l(v.visit_date, format: :only_date_dash) : 'Not specified', v.name_formatted, actual_visit_state_date(visit: v, include_date: false, use_html: false), number_to_currency(v.amount, unit: ''), v.currency, visit_clinical_user_account_activities_date_decorator(visit: v, html: false)]
  end
end
