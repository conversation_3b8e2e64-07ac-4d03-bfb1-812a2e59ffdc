scss:
  .select2-container {
    width: 300px !important;
  }
  .container {
    margin-left: 0 !important;
  }

.row.section_header
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Call center")
    .mt-0
    = link_to 'New & pending', { scope: 'new_or_pending' }, class: "default_sm_btn #{'active' if ((@scope == 'new_or_pending') || @scope.nil?)}"
    = link_to 'Resolved', { scope: 'resolved' }, class: "default_sm_btn #{'active' if @scope == 'processed'}"
    = link_to "Search by phone number", new_v2_sponsor_payclinical_employees_search_path, class: 'default_sm_btn'
    = link_to "Add a new request", new_v2_sponsor_operator_helpdesk_email_path, class: 'default_sm_btn'

.tab-content.vertical_1.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container style='width: auto;'
      - if @events.any?
        table.table
          thead
            th Time
            th From
            th To
            th.text-xs-center Sponsor/Study
            th.text-xs-center Status
            th.text-xs-center Owner
            th.fit_to_content
          tbody
            - @events.each do |ev|
              tr id="zadarma_event_#{ev.id}"
                td
                  = with_he_colors(ev, two_line_date_time(ev.call_start, small_second_line: true))
                td
                  = link_to ev.caller_number, v2_sponsor_payclinical_employees_search_path(search_term: ev.caller_number)
                  br
                  span.row_second_line_2 = zadarma_event_caller_link(ev)
                td
                  = ev.called_number
                td
                  = render SponsorStudyComponent.new(cro: ev.contract_research_organization, project: ev.project)
                td.text-xs-center
                  = ev.status
                td.text-xs-center
                  = ev.operator&.full_name || 'None yet'
                td.fit_to_content
                  = render DropdownBtnComponent.new do |component|
                    - component.with_links do
                      = link_to ev.new? ? 'Check-in' : 'Edit', edit_v2_sponsor_zadarma_event_path(ev), class: 'dropdown-item'
                      = link_to 'Callback completed', callback_completed_v2_sponsor_zadarma_event_path(ev), class: 'dropdown-item', method: :put unless ev.called_back_at
                      = link_to 'Close', close_v2_sponsor_zadarma_event_path(ev), class: 'dropdown-item', method: :put if ev.pending?
                      = link_to 'Re-open', reopen_v2_sponsor_zadarma_event_path(ev), class: 'dropdown-item', method: :put if ev.closed?
                      = link_to 'Convert to request', new_v2_sponsor_operator_helpdesk_email_path(zadarma_event_id: ev.id), class: 'dropdown-item'
      - else
        h3 No events have been entered yet.

        = paginate @events
