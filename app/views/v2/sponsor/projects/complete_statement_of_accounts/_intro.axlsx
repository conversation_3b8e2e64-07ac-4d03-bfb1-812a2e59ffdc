sheet.add_row ['Let Me Pay Sp. z o.o.']
sheet.add_row ['Iłżecka 26, 02-135 Warszawa']
sheet.add_row []
sheet.add_row ['HISTORIA TRANSAKCJI PŁATNICZYCH']
sheet.add_row ['KONTO ROZLICZENIOWE BADANIA KLINICZNEGO']
sheet.add_row ["Numer protokołu: #{ @project.clinical_protocol_code }"]
sheet.add_row ["Numer rachunku: #{ ClinicalTransfer.format_account_number @project.get_active_account_number }"]
sheet.add_row ["Okres rozliczeniowy: #{ I18n.l(@start_date, format: :only_date_dash) } - #{ I18n.l(@end_date, format: :only_date_dash) }"]
sheet.add_row ["Konto prowadzone dla:"]
sheet.add_row ["#{@project.company_name}"]
sheet.add_row ["#{@project.company_street}"]
sheet.add_row ["#{@project.company_zip_code} #{@project.company_city}"]
sheet.add_row ["NIP: #{ProjectDebit.format_nip_number(@project.company_nip)}"]
