.section_header
  = section_header(title: "Cost categories", subtitle: "Protocol# #{ red_project_link(project: @project) }")
  = link_to 'Add a new category', new_v2_sponsor_project_visit_payment_category_path(@project), class: 'default_sm_btn'
.mt-1

.tab-content.vertical_1.card
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      .row
        .col-xs-12
          = form_tag update_multiple_v2_sponsor_project_visit_payment_categories_path(@project), method: :put do |f|
            table.table.table-striped
              thead
                tr
                  th.fit_to_content style=('padding-right: 30px') = md_check_box(checked: false, label_text: nil, disabled: !policy(@new_vpc).update?,
                  input_data: { behavior: 'set_checkbox', target: '.vpc_checkbox', set_to: 'checked' })

                  th.text-xs-left.th_two_line
                    | Category
                    br
                    | in English

                  th.text-xs-left.th_two_line
                    | Category
                    br
                    | in Polish

                  th.text-xs-left.th_two_line style='width: 1%'
                    | Category
                    br
                    | Abbreviation
                  th.text-xs-left.th_two_line
                    | Cost
                    br
                    | limit

                  th.text-xs-right.fit_to_content
              tbody
                - @vpc.each do |vpc|
                  tr
                    td = md_check_box(checked: vpc.visible, input_name: "visit_payment_categories[#{ vpc.id }][visible]",
                    input_value: vpc.visible ? 1 : 0, input_id: "vpc_#{ vpc.id }", label_text: nil, input_class: 'vpc_checkbox filled-in', tooltip_title: 'Visibility', fieldset_class: 'protip form-group md-form',
                    disabled: !policy(vpc).update?)
                    td.text-xs-left = vpc.name_en
                    td.text-xs-left = vpc.name_pl
                    td.text-xs-center style='width: 50px' = vpc.abbr
                    td.text-xs-left = vpc_limit(vpc: vpc, project: @project)
                    td.text-xs-right
                      = dropdown_btn do
                        = link_to 'Edit', edit_v2_sponsor_project_visit_payment_category_path(@project, vpc.id), class: 'dropdown-item'
                        = toggle_project_vpc_visibility_link(project: @project, vpc: vpc)
            .mt-2
            = submit_tag 'Save', class: 'submit_btn mt-right-btn', disabled: !policy(@new_vpc).update?
            = link_to_back(text: 'Back', link_class: 'btn btn-warning')
