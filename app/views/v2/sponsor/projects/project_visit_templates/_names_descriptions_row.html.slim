scss:
  td {
    border: none !important;
  }

  table {
    .md-form {
      input, label {
        font-family: inherit;
      }
    }
  }

  a.remove_pvt {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .names_and_descriptions_row {
    position: relative;

    a.remove_pvt {
      opacity: 0;
      transition: .4s;
    }

    &:hover {
      .remove_pvt {
        opacity: 1;
      }
    }
  }

  .form-group.string.required.project_visit_template_form_visit_names {
    padding-right: 30px;
  }

.names_and_descriptions_row data-index=i
  table.w-100
    tbody
      tr
        td style=("width: 250px")
          = f.input :visit_names, label: 'Visit name', input_html: { name: "project_visit_template_form[visit_names][#{ i }]", value: visit_name, id: "project_visit_template_form_visit_names_#{ i }" }, error: error, label_html: { for: "project_visit_template_form_visit_names_#{ i }" }

        td style="position: relative"
          = f.input :descriptions, label: 'Description', input_html: { name: "project_visit_template_form[descriptions][#{ i }]", value: description, id: "project_visit_template_form_descriptions_#{ i }" }, error: error, label_html: { for: "project_visit_template_form_descriptions_#{ i }" }
          a.default_sm_btn.remove_pvt data-behavior='remove_pvt_name_description_row' data-index=i Remove
