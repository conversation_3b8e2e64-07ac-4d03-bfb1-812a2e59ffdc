scss:
  div.relative {
    position: relative;
  }

  span.currency_label {
    position: absolute;
    right: 0;
    bottom: 0;
  }

  .project_send_app_req {
    label {
      font-size: 1.1rem;
    }
  }

.row.section_header
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Study overview", subtitle: sponsor_protocol(project: @project) )
    .mt-0
    = link_to "Subjects' accounts", v2_sponsor_project_clinical_users_path(@project), class: 'default_sm_btn'
    - if policy(@project).add_center?
      = link_to 'Add a new site', new_v2_sponsor_project_clinical_center_path(@project), class: 'default_sm_btn'
    = link_to 'Study team', link_to_project_researchers(project: @project, researcher: current_researcher), class: 'default_sm_btn'
    - if current_researcher.payclinical_employee
      = link_to 'Edit Project Code', edit_v2_sponsor_payclinical_employees_project_code_path(@project), class: 'default_sm_btn'
    = dropdown_btn btn_text: 'Activities', div_class: 'dropdown sm-dropdown' do
      = link_to 'Activity log', v2_sponsor_project_account_activity_logs_path(@project), class: 'dropdown-item'
      - if policy(@project).add_shipment?
        = link_to 'Shipments', v2_sponsor_project_shipments_path(@project), class: 'dropdown-item'
    - if @project.clinical_transfers.waiting.exists?
      = link_to 'Payments for approval', v2_sponsor_account_history_path(resource_id: @project.id, resource_type: @project.class.name, scope: 'waiting'), class: 'default_sm_btn btn-danger'

    = link_to 'Actions required', v2_sponsor_project_user_action_requirements_path(@project), class: "default_sm_btn #{user_action_requirements_link_class(@user_action_requirements)}" if @user_action_requirements.any?

  = render 'shared/two_line_text_section', first_row_text: @project.clinical_centers.distinct.count , second_row_text: 'Study site'.pluralize(@project.clinical_centers.distinct.count), klass: "red"

  = render 'shared/vip_researcher_info', researcher: current_researcher
  - info = Researchers::InfoBoxText.new(current_researcher).for_project(@project)
  - if info.present?
    .col-xs-12
      .summary_section.warning.mt-1
        = info

.row
  .col-md-12
    - if ProjectPolicy.new(current_researcher, @project).can_see_balance?
      = project_balance_section(project: @project).html_safe

.mt-0

/! Nav tabs
ul.nav.nav-tabs.vertical_1 role="tablist"
  li.nav-item
    a.nav-link data-toggle="tab" href="#panel1" role="tab" data-activate="summary" Summary
  li.nav-item
    a.nav-link data-toggle="tab" href="#panel2" role="tab" data-activate="settings" Settings


.tab-content.vertical_1.card
  #panel1.tab-pane role="tabpanel"
    .container
      .row
        .col-xs-12
          = render 'overview_table', project: @project, clinical_centers: @clinical_centers
          = project_anticitpated_visits_cost(project: @project)

          .mt-3
          = link_to_back(text: 'Back', link_class: 'margin0 btn btn-warning')


  #panel2.tab-pane.fade role="tabpanel"
    .container
      .row
        .col-xs-12
          - is_cro = current_researcher.is_super_manager_in_project?(@project) || current_researcher.is_manager_in_project?(@project)
          - is_cro_or_operator = is_cro || current_researcher.payclinical_employee
          - path = is_cro_or_operator ? v2_sponsor_project_path(@project) : '#'

          = simple_form_for @project, url: path do |f|
            h3.mb-1.mt-1 Properties
            - if is_cro
              = f.input :clinical_protocol_code
            = f.input :name, label: 'Sponsor', disabled: !is_cro
            = md_text_input(disabled: !is_cro, label_text: 'Account owner', input_value: @project.cro.try(:name), input_class: 'string disabled')
            = f.input :debit_allowed, label: 'Account type', input_html: { value: @project.debit_allowed ? 'Just-in-time' : 'Pre-paid' }, as: :string, disabled: true
            = f.input :closed, label: 'Status', input_html: { value: @project.closed ? 'Closed' : 'Current account' }, as: :string, disabled: true
            = f.input :reimbursement_start_date, input_html: { class: 'datepicker' }, disabled: !is_cro, as: :string, placeholder: 'Any date'
            = f.input :send_app_req, label: 'Email notifications on new payments awaiting approval'

            h3.mb-1.mt-3 Transaction limits
            div.relative
              = f.input :investigator_transfer_limit, label: 'Investigator’s limit for single payment', disabled: !is_cro, as: :string
              span.currency_label = currency_symbol(project: @project)

            div.relative
              = f.input :cra_transfer_limit, label: 'CRA’s limit for single payment', disabled: !is_cro, as: :string
              span.currency_label = currency_symbol(project: @project)

            div.relative
              = f.input :manager_transfer_limit, label: 'Manager’s limit for single payment', disabled: !is_cro, as: :string
              span.currency_label = currency_symbol(project: @project)

            div.relative
              = f.input :transfer_limit, label: 'Maximum payment for a visit', disabled: !is_cro, as: :string
              span.currency_label = currency_symbol(project: @project)

            h3.mb-1.mt-3 Service level
            = f.input :fee_plan, label: "SLA", disabled: true, as: :string

            h3.mb-1.mt-3 Purchase orders
            = f.input :po, label: "Invoices", disabled: !is_cro_or_operator, as: :string
            = f.input :note_po, label: "Debit notes", disabled: !is_cro_or_operator, as: :string

            .mt-3
            - if is_cro_or_operator
              = f.submit 'Save', class: 'default_btn margin0 mt-right-btn'
            = link_to_back(text: 'Back', link_class: 'margin0 btn btn-warning')
