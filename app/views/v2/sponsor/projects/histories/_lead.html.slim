css:
  h3.style1 {
    font-family: news cycle;
    color: #373a3c;
    text-transform: initial;
  }

  h3.style2 {
    font-family: news cycle;
    color: #373a3c;
    text-transform: initial;
    font-size: 1.3em;
  }

#financial_account_lead.section_header style=("margin-bottom: 10px;")
  = section_header(title: "Transaction history", subtitle: "Protocol# #{ red_project_link(project: @project) }")
  = pdf_save_link(text: "Save as XLS", path: v2_sponsor_project_complete_statement_of_account_path(@project, doc_type: 'xlsx'))
  = pdf_save_link(path: v2_sponsor_project_complete_statement_of_account_path(@project, doc_type: 'pdf'))
  .mt-1
  / = render 'shared/iban_swift', project: @project, swift: Bank.lmp_bank_swift_code
  - if ProjectPolicy.new(current_researcher, @project).can_see_balance?
    = project_balance_section(project: @project)
.mt-2
