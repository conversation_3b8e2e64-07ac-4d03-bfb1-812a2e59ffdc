scss:
  .wd_210 {
    width: 210px;
  }

  .wd_120 {
    width: 120px;
  }

  .wd_160 {
    width: 160px;
  }

= section_header(title: "Study team", subtitle: "Protocol# #{ @clinical_protocol_code }")

.mt-3
/! Nav tabs
= form_tag v2_sponsor_multi_currency_projects_researchers_path, method: :get, remote: false do
  = select_tag :selected_project_id, options_from_collection_for_select(@projects, :id, proc { |p| "#{p.country_name} - #{p.currency}"}, @project.try(:id)), prompt: 'Select country', class: 'mdb-select md-12'
  = hidden_field_tag :code, @clinical_protocol_code

.mt-2

- if @project.present?
  - if ProjectPolicy.new(current_researcher, @project).invite_researcher?
    = link_to 'Add a new user', new_v2_sponsor_project_researcher_path(@project), class: 'default_sm_btn'
  - else
    = link_to 'Add a new user', 'javascript:;', class: 'default_sm_btn', data: { toggle: 'tooltip', placement: 'bottom', title: 'You are not authorized to add a new user.' }


#content
  - if @project.present?
    = render "content"
  - else
    = render "multi_project_summary"

coffee:
  $ ->
    $('#selected_project_id').on 'change', (e) ->
      $('form').submit();
