
.tab-content.vertical_1.mt-2
  #panel1.tab-pane.active role="tabpanel"
    .container
      .row
        .col-xs-12
          table.table.table-stripped
            thead
              tr
                th.text-xs-left Site#
                th.text-xs-center style=("width: 175px") # of Investigators
                th.text-xs-center style=("width: 175px") # of CRAs
                / th.text-xs-center.fit_to_content

            tbody
              - @projects.each do |project|
                tr.clickable data-target=".#{project.id}_accordion" data-toggle="collapse"
                  td.td_style1 colspan='99'
                    i.fa.fa-angle-down.rotate-icon style='margin-right: 10px'
                    = "Sites in #{project.clinical_centers.map(&:country_name).uniq.to_sentence} for payments in #{currency_symbol(project: project)}"


                - project.clinical_centers.order('clinical_center_code asc').each do |cc|
                  - has_access = current_researcher.has_access_to_site?(cc)

                  tr class="#{project.id}_accordion"
                    td.text-xs-left
                      - if has_access
                        = link_to cc.code_with_city, v2_sponsor_clinical_center_clinical_users_path(cc), class: "#{ 'text_crossed' if cc.closed }"
                      - else
                        = link_to cc.code_with_city, 'javascript:;', class: "protip hover_color_red #{ 'text_crossed' if cc.closed }", data: { 'pt-title' => 'You are not authorized to access this information.', 'pt-position' => 'bottom' }

                    td.text-xs-center
                      = cc.project_roles.investigators.count

                    td.text-xs-center
                      = cc.project_roles.cras.count

                    / td.text-xs-center
                    /   = dropdown_btn do

