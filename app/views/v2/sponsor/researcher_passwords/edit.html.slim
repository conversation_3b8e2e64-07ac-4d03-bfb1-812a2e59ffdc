= section_header(title: "Change password", subtitle: "User: #{red_link(text: current_researcher.full_name, path: edit_v2_sponsor_researchers_path)}")

.tab-content.vertical_1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      = simple_form_for current_researcher, url: v2_sponsor_researcher_passwords_path do |f|
        .row.mt-2
          .col-xs-12.col-md-7.col-md-offset-5
            - unless current_researcher.temporary_password_was
              = f.input :current_password, autofocus: true
            = f.input :password, label: 'New password', input_html: { class: '', data: { behavior: 'validate_researcher_password', user_type: 'researcher' } }, wrapper_html: { style: 'margin-bottom: -39px;' }
            div.text-xs-right#first_password_valid_indicator.password_valid_indicator style='visibility: hidden; font-size: 12pt; margin-bottom: 10px' *
            = f.input :password_confirmation, input_html: { class: '', data: { behavior: 'validate_password_confirmation', password_source: '#researcher_password', user_type: 'researcher' } }
            div.text-xs-right#password_confirmation_valid_indicator.password_valid_indicator style='visibility: hidden; font-size: 12pt; margin-top: -39px; margin-bottom: 10px' *
            p style='font-size: 11px; margin-top: -8px;'
              = link_to 'Show password', 'javascript:;', data: { behavior: 'show_password', targets: ['#researcher_password', '#researcher_password_confirmation'] }, class: 'default_sm_btn'

            - unless current_researcher.temporary_password_was
              = render 'password_change_info', researcher: current_researcher
            .mt-1
            = render 'shared/researcher_password_requirements'
        .text-xs-left.mt-3
          = f.submit 'Confirm', class: 'submit_btn mt-right-btn'
          = link_to_back(text: 'Back', link_class: 'btn btn-warning')
