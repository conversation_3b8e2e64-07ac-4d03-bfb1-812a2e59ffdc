.row
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Site review")
.mt-1

.tab-content.vertical_1.card
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      table.table.table-stripped.text-xs-center
        thead
          tr
            th.text-xs-center Sponsor
            th.text-xs-center Limited Projects#
            th.text-xs-center Premium Projects#
            th.text-xs-center Sites for review
            th.text-xs-center Last review
            th.text-xs-center.fit_to_content
        tbody
          - @cros.each do |cro|
            tr id="cro_#{ cro.id }"
              td = cro.name
              td = cro.projects.free.count
              td = cro.projects.with_premium_plans.count
              - premium_sites = cro.clinical_centers.where(project: cro.projects.with_premium_plans).distinct
              td
                = "#{ premium_sites.where.not(id: cro.site_reviews.where(created_at: Time.current.all_week).map(&:resource_id)).distinct.count }/#{ premium_sites.count }"
                span.row_second_line_2 = cro.premium_plan? ? 'Required' : 'Optional'
              - last_review_date = cro.site_reviews.order_by_id.first&.created_at
              th = last_review_date ? l(last_review_date) : ''
              th = render DropdownBtnComponent.new do |component|
                - component.with_links do
                  = link_to 'Start review', new_v2_sponsor_site_reviews_sponsor_review_path(cro), class: 'dropdown-item'