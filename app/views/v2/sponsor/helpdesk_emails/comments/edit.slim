.row
  .col-md-8.col-sm-10.col-xs-12
    = render HelpdeskEmails::HeaderLinkComponent.new(helpdesk_email: @email, text: 'Edit Comment')
.tab-content.vertical_1.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      = simple_form_for [:v2, :sponsor, @email, @comment] do |f|
        = render HelpdeskEmails::SummaryComponent.new(helpdesk_email: @email)
        h5.mt-3 Comment
        = f.input :body, label: false
        = f.submit 'Save', class: 'submit_btn'
