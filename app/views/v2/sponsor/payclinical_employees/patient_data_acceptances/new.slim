.row
  .col-xs-12
    = section_header(title: "Approve data for #{red_subject_link(cu: @clinical_user)}", klass: 'no_bottom_margin', subtitle: "#{ link_to_cc(clinical_center: @clinical_user.clinical_center) } Protocol# #{ red_project_link(project: @clinical_user.clinical_center.project) }")
.mt-1

.tab-content.vertical_1.card
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      .row
        .col-sm-12
          = simple_form_for @clinical_user, url: v2_sponsor_payclinical_employees_patient_data_acceptances_path(clinical_user_id: @clinical_user.id), method: :post do |f|
            = f.input :first_name, disabled: true
            = f.input :last_name, disabled: true
            = f.input :sex, disabled: true, collection: ClinicalUser::SEX, input_html: { class: 'md-12 mdb-select'}, include_blank: "Select"
            = f.input :provided_data_form_type, disabled: true, collection: ClinicalUser::PROVIDED_DATA_FORM_TYPE, input_html: { class: 'md-12 mdb-select'}, include_blank: false
            = f.input :street, disabled: true
            = f.input :city, disabled: true
            = f.input :zip_code, disabled: true
            = f.input :phone_number, disabled: true, input_html: { data: { phone_number_input: true }}
            = f.input :second_phone_number, disabled: true, input_html: { data: { phone_number_input: true }}
            = f.input :account_number, disabled: true, input_html: { value: masked_acccount_number(@clinical_user.account_number) }
            = f.input :juvenile, disabled: true
            - if @clinical_user.juvenile
              = f.input :parent_first_name, disabled: true
              = f.input :parent_last_name, disabled: true
              = f.input :parent_phone_number, disabled: true
            - if @clinical_user.account_number.present?
              = f.input :bank_account_sec_code, disabled: true
            = f.input :transfer_destination, collection: ClinicalUser::ForOperatorVerification::TRANSFER_DESTINATIONS, input_html: { class: 'md-12 mdb-select'}, include_blank: 'Select destination', disabled: true
            = form_save_and_cancel_btn(submit_text: 'Approve', cancel_text: 'Back', data: { 'behavior' => 'spinner_loader' })