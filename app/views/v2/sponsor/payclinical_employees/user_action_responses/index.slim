.row
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Responses to action required", subtitle: "Reported for: #{ red_link(text: 'ALL TRIALS', path: v2_sponsor_projects_path)}")

.tab-content.vertical_1.card.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      = form_tag '', method: :get do
        = select_tag 'q[project_id_eq]', options_from_collection_for_select(@projects, :id, :clinical_protocol_code, params[:q].present? ? params[:q][:project_id_eq] : nil), class: 'select2', prompt: 'Select Protocol'
        br
        = select_tag 'q[clinical_user_id_eq]', options_from_collection_for_select(@clinical_users, :first, :second, params[:q].present? ? params[:q][:clinical_user_id_eq] : nil), class: 'select2 mt-1', prompt: 'Select Subject'
        br
        = select_tag 'q[researcher_id_eq]', options_from_collection_for_select(@researchers, :id, :full_name, params[:q].present? ? params[:q][:researcher_id_eq] : nil), class: 'select2 mt-1', prompt: 'Select Researcher'
        br
        .row
          .mt-1.col-xs-12
            = text_field_tag 'q[user_action_requirement_reason_cont]', params.dig(:q, :user_action_requirement_reason_cont), placeholder: 'Enter Issue'
        br
        = submit_tag 'Filter', class: 'submit_btn mt-1 mb-3'

      - if @user_action_responses
        h3.mb-1 Responses

        table.table.mb-3
          thead
              tr
                th Date
                th Researcher
                th Body
                th Subject#
                th Protocol#
                th Site#
                th CRO#
                th.fit_to_content.text-xs-center style=("min-width: 100px;")
          tbody
            - @user_action_responses.each do |r|
              tr
                td style=("width: 150px") #{r.created_at.strftime('%d-%m-%y')}
                td style=("width: 180px")
                  = researcher_link(researcher: r.researcher, placeholder: r.responder_name)
                td #{r.body}
                td = r.patient_code
                td = r.clinical_protocol_code
                td = r.clinical_center_code
                td = r.cro_name
                td
                  - if r.clinical_user
                    = link_to 'Read more', v2_sponsor_clinical_user_visits_path(r.clinical_user, anchor: 'actions_required_table'), class: 'default_btn'

        = paginate @user_action_responses
