scss:
  #s2id_helpdesk_email_from {
    width: 40% !important;
  }

.row
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Helpdesk Request")

.tab-content.vertical_1.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      = simple_form_for @email, url: v2_sponsor_operator_helpdesk_emails_path do |f|
        .form-group
          = f.label :from
          = f.input :from, input_html: { class: 'researcher-select' }, as: :hidden
        = f.input :cc, as: :string
        = f.input :subject
        h5.mb-0.mt-3 Body
        = f.input :body, label: false
        = f.input :request_type, collection: HelpdeskEmail.request_type.values, input_html: { class: 'mdb-select md-12' }, include_blank: false
        = f.input :message_date, as: :string, input_html: { class: 'datepicker' }
        h5.mb-0.mt-3 Comment
        = f.input :comment, label: false
        = f.hidden_field :zadarma_event_id
        = f.submit 'Create', class: 'submit_btn mt-1'

javascript:
  $(document).ready(function() {
    $('.researcher-select').select2({
      placeholder: 'Search by email, last name or phone number',
      minimumInputLength: 2,
      initSelection: function (element, callback) {
        var id = $(element).val();
        if (id !== "") {
          $.ajax({
            url: '/v2/sponsor/operator_helpdesk_emails/researcher_details',
            dataType: 'json',
            data: {
              id: id
            },
            success: function (data) {
              callback(data);
            }
          });
        }
      },
      ajax: {
        url: '/v2/sponsor/operator_helpdesk_emails/researcher_search',
        dataType: 'json',
        delay: 250,
        data: function(term) {
          return {
            q: term
          };
        },
        results: function(data) {
          var opts = $.map(data, function (item) {
						return {
							id: item.id,
							text: item.text
						};
					})
          return {
            results: opts
          };
        },
        cache: true
      }
    });
  });