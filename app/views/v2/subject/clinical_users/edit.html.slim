scss:
  input.string:not(.disabled) {
    color: #00afff !important;
  }

= section_header(title: "Ustawienia konta", subtitle: default_subject_subtitle)
.mt-1
.tab-content.vertical_1
  .container
    = simple_form_for current_clinical_user, url: v2_subject_clinical_users_path do |f|
      .row.mt-2
        .col-xs-12

          h3.mt-0.mb-1 <PERSON>
          = f.input :patient_code, disabled: true
          = f.input :first_name
          = f.input :last_name
          .mt-3

          h3.mt-0.mb-1 Adres zamieszkania
          = f.input :street, disabled: !current_clinical_user.online_patient_forms
          = f.input :zip_code, disabled: !current_clinical_user.online_patient_forms
          = f.input :city, disabled: !current_clinical_user.online_patient_forms
          .mt-3

          h3.mt-0.mb-1 <PERSON> k<PERSON>
          = f.input :phone_number, disabled: !current_clinical_user.online_patient_forms
          = f.input :email
          .mt-3

          h3.mt-0.mb-1 Rachunek bankowy
          = f.input :account_number, disabled: !current_clinical_user.online_patient_forms, input_html: { value: current_clinical_user.formatted_account_number(force_unmask: true) }
          = f.input :bank, input_html: { value: current_clinical_user.bank.try(:name) }, disabled: true, label: "Bank"

          = f.input :payment_form_preference, disabled: !current_clinical_user.online_patient_forms, include_blank: false, input_html: { class: 'mdb-select md-12' }, label: 'Preferowana forma zwrotu kosztów'
          / = f.input :data_proc_agree, disabled: !current_clinical_user.online_patient_forms, input_html: { class: 'filled-in' }

          .mt-3
          .text-xs-left.mt-3.mb-4
            = f.submit 'Zapisz', class: 'submit_btn'
            = link_to 'Powrót', :back, class: 'btn btn-warning cancel-left-margin'
