= javascript_include_tag 'jquery.min'

javascript:
  function textAreaAdjust(o) {
    o.style.minHeight = "1px";
    o.style.minHeight = (o.scrollHeight - 10)+"px";
  }

  function initMap() {
    var styledMapType = new google.maps.StyledMapType(
      [{"featureType":"all","elementType":"labels.text.fill","stylers":[{"saturation":36},{"color":"#000000"},{"lightness":40}]},{"featureType":"all","elementType":"labels.text.stroke","stylers":[{"visibility":"on"},{"color":"#000000"},{"lightness":16}]},{"featureType":"all","elementType":"labels.icon","stylers":[{"visibility":"off"}]},{"featureType":"administrative","elementType":"geometry.fill","stylers":[{"color":"#000000"},{"lightness":20}]},{"featureType":"administrative","elementType":"geometry.stroke","stylers":[{"color":"#000000"},{"lightness":17},{"weight":1.2}]},{"featureType":"landscape","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":20}]},{"featureType":"poi","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":21}]},{"featureType":"road.highway","elementType":"geometry.fill","stylers":[{"color":"#000000"},{"lightness":17}]},{"featureType":"road.highway","elementType":"geometry.stroke","stylers":[{"color":"#000000"},{"lightness":29},{"weight":0.2}]},{"featureType":"road.arterial","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":18}]},{"featureType":"road.local","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":16}]},{"featureType":"transit","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":19}]},{"featureType":"water","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":17}]}]
    )

    var a, e, i, n, o, r, s, t, u;

    e = new google.maps.LatLng(52.190789, 20.985582);

    t = {
      zoom: 17,
      scrollwheel: !1,
      center: e,
      mapTypeControl: !0,
      mapTypeControlOptions: {
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU
      },
      navigationControl: !0,
      navigationControlOptions: {
        style: google.maps.NavigationControlStyle.SMALL
      },
      mapTypeId: google.maps.MapTypeId.ROADMAP
    };

    n = new google.maps.Map(document.getElementById('map'), t);
    var img_url = "#{ URI.join(root_url, image_path('PC-plus-small.png')) }";
    r = '<div style="color: black; background-color: #FFF;"><img src="'+img_url+'" id="logo-on-map" alt="PayClinical.com" height="43" width="150"><p id="marker_map_p"><span id="marker_map_p_el1">Let Me Pay Sp. z o.o.</span><span id="marker_map_p_el2">Wiśniowy Business Park </span><span id="marker_map_p_el3">Iłżecka 26, 02-135 Warsaw, Poland</span><span id="marker_map_p_el4">Phone: +48 22 376 0000</span><span id="marker_map_p_el5">Email: <EMAIL></span></p></div>';

    i = new google.maps.InfoWindow({
      content: r
    });

    s = new google.maps.MarkerImage('http://maps.google.com/mapfiles/marker_orange.png', new google.maps.Size(20, 34), new google.maps.Point(0, 0), new google.maps.Point(50, 50));

    o = new google.maps.MarkerImage('http://maps.google.com/mapfiles/shadow50.png', new google.maps.Size(40, 34), new google.maps.Point(0, 0), new google.maps.Point(50, 50));

    u = new google.maps.LatLng(52.190029, 20.985582);

    a = new google.maps.Marker({
      position: u,
      map: n,
      icon: s,
      shadow: o,
      title: 'Let Me Pay',
      zIndex: 3
    });

    google.maps.event.addListener(a, 'click', function() {
      i.open(n, a);
    });

    i.open(n, a);

    n.mapTypes.set('styled_map', styledMapType);
  }

/ #image_path data-url="#{asset_path('PC-plus-small-white.png')}"

/ section#contact-section.section.section-xl.py-lg.pb-300
/   /! SVG separator
/   .separator.separator-bottom.separator-skew.zindex-100
/     svg preserveaspectratio="none" version="1.1" viewbox=("0 0 2560 100") x="0" xmlns="http://www.w3.org/2000/svg" y="0"
/       polygon.fill-white points=("2560 0 2560 100 0 100")

section#contact_section.section.section-lg.pb-4.py-lg.d-flex.align-items-center
  .container.d-flex.align-items-center
    .row.row-grid.align-items-center
      .col-md-12.order-md-1
        .pr-md-5
          h2.display-4 Contact
          p.text-justify.mb-5
            | Do you have any questions about our references and success stories? Or would you like to become a customer?
            br
            | Our team of experts is ready and waiting to assist you. Please contact us and use us as a resource towards your business success.

          div
            = simple_form_for @form, url: contact_emails_url, method: :post, defaults: {wrapper: :vertical_md_form} do |f|
              = f.input :name
              = f.input :email
              = f.input :company
              = f.input :phone_number
              = f.input :message, as: :text, input_html: { class: 'md-textarea', rows: '2' }

              div.mt-sm
                = f.submit 'Send message', class: 'btn btn-default btn-lg'



section.section.section-lg.pb-0
  .row.justify-content-center
    .col-lg-12
      #wisniowy_images.row.custom_border style="margin:0;"
        #contact_wisniowy_1.col-md-9.col-xs-12.float-left
        .col-md-3.col-xs-12.float-left style=("padding: 0;")
          #contact_wisniowy_2.col-md-12.col-sm-6
          #contact_wisniowy_3.col-md-12.col-sm-6
      div
        #map style="width: 100%; height: 500px;"
          script async="" defer="defer" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDcQjBbl6TZ9tm8WWG-ANutdW5kDl0H48c&callback=initMap"
