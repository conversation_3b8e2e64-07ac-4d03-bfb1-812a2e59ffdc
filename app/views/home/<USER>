.position-relative
  /! shape Hero
  #index-main-section.section.section.section-xl.section-dark.mh-100vh
    .container.title-container.d-flex.py-1.ml-xl-xl.mt-4
      .col.px-0
        .row
          .col-lg-6.col-md-8.col-sm-12.text-center
            .main-title-container.text-left
              h1.main-title
                | REIMBURSEMENT
                span.plus +
                span.second  IN CLINICAL TRIALS

              h3.main-title-desc
                | The journey towards your patient's recovery.

    = render 'layouts/v1/scroll_to_continue'

#sevices-section.section.section.section-lg
  .container
    .row.justify-content-center
      .col-lg-12
        h4.display-3.mb-3.text-center.text-uppercase.text-dark
          | Subject compensation and reimbursement

        div.text-center
          p We have developed technology that gives financial transparency to Sponsors and CROs, shifting the time-consuming burden from investigators and speeding up the subject reimbursement process. PAYCLINICAL.com is an online payment platform for Sponsors, CROs, investigators and patients participating in clinical trials. It allows Sponsors to reimburse patients for travel costs and to send payments directly to their bank accounts, MasterCard prepaid cards or by postal orders.


        / .row.row-grid.pt-5
        /   .col-lg-4
        /     .py-0.py-md-1.py-lg-5.text-center
        /       .div
        /         = embedded_svg 'full-range.svg', class: 'animated_svg svg_120'

        /       h6.text-uppercase.my-4 Full range CRO
        /       p.description.my-4.text-justify Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.

        /       / = link_to t('layout.home.learn-more'), 'full_range_cro_home_path', class: 'btn btn-custom-blue mt-4'

        /   .col-lg-4
        /     .py-0.py-md-1.py-lg-5.text-center
        /       = embedded_svg 'meddev.svg', class: 'animated_svg svg_120'

        /       h6.text-uppercase.my-4 Medical device clinical studies
        /       p.description.my-4.text-justify Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.

        /       / = link_to t('layout.home.learn-more'), 'medical_devices_home_path', class: 'btn btn-success mt-4'

        /   .col-lg-4
        /     .py-0.py-md-1.py-lg-5.text-center
        /       = embedded_svg 'outsourcing_01.svg', class: 'animated_svg svg_120'

        /       h6.text-uppercase.my-4 Trial execution & performance
        /       p.description.my-4.text-justify Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam

        /       / = link_to t('layout.home.learn-more'), 'trial_execution_home_path', class: 'btn btn-custom-red mt-4'


#big_section.section.section.section-lg
  .container
    .row.row-grid.align-items-center
      .col-lg-12
        .px-3
          h4.display-3.text-center.text-uppercase.text-white Accelerating the reimbursement process
          p.text-white.text-center Our services are focused around three principles: trust, privacy, and high performance. We maximize the security of financial and personal data of subjects and research teams in every transaction. We can design, build, test and implement the right payment solutions for your clinical study at a very competitive cost. We are the company that can help you get ahead.

          div.text-center.mt-lg
            = embedded_svg 'study_management_2.svg', class: 'animated_svg svg_320 img-fluid'


section.section.section-lg.bg-gradient-lighter
  .container
    .row.row-grid.align-items-center
      .col-lg-5
        h4.display-3.text-center.text-left.text-uppercase.text-dark.mb-3 style="line-height: 1.15em;" Simplicity, security, and global reach

        p.text-left With our seamless payment solution you can drive greater efficiency in reimbursement process, reduce your transaction costs and automate payments in your clinical study.

        ul.list-unstyled.mt-2
          li.py-2
            .d-flex.align-items-center
              div
                .badge.badge-circle.badge-success.mr-3.bg-custom-blue.text-custom-blue.badge-custom-size
                  i.ni.ni-settings-gear-65
              div
                h6.mb-0.lh-130 = "Pre-paid and post-paid accounts"

          li.py-2
            .d-flex.align-items-center
              div
                .badge.badge-circle.badge-success.mr-3.bg-custom-blue.text-custom-blue.badge-custom-size
                  i.ni.ni-settings-gear-65
              div
                h6.mb-0.lh-130 = "Subject and site payments"

          li.py-2
            .d-flex.align-items-center
              div
                .badge.badge-circle.badge-success.mr-3.bg-custom-blue.text-custom-blue.badge-custom-size
                  i.ni.ni-settings-gear-65
              div
                h6.mb-0.lh-130 = "SLAs to match your needs"

      .col-lg-7
        .px-md
          div.text-center
            = image_tag 'pc_macbook.png', class: 'mw-100'


section.section.section-lg
  .container
    .row.row-grid.align-items-center
      .col-lg-7
        .px-md
            div.text-center
              = image_tag 'nobletrack_imac.png', class: 'mw-100'

      .col-lg-5
        h4.display-3.text-center.text-left.text-uppercase.text-dark.mb-3 style="line-height: 1.15em;" Subject reimbursement made awesome
        p.text-left PayClinical collects personal data forms completed by patients from their study sites. An individual, visit driven custody bank account identified by patient’s study number is set up for each patient. Clinical Research Associates or other Sponsor representatives enter the reimbursable costs per visit for each registered patient and send immediate payments. PayClinical provides Sponsors with monthly account statements, bank confirmations, statistical reports, cost projections and more. Payment services are performed by Let Me Pay in a cooperation with Citibank and Bank BNP Paribas.


section.section.section-lg.py-5.bg-gradient-lighter
  .container
    .row.flex-column.align-items-center.justify-content-center
      h4.display-4.text-left.text-uppercase.text-dark Let us know how we can help
      p.text-left
        | Do you have any questions about our references and success stories? Or would you like to become a customer?  Our team of experts is ready and waiting to assist you.
      div.mt-3
        a href="mailto:<EMAIL>?subject=Quote request" class="btn btn-danger btn-lg m-2 radius-0" Get in touch



coffee:
  study_management_2_animation = () ->
    svg = document.getElementById('study_management_2')
    s = Snap(svg)
    # step1_from = Snap.select('#path6')
    step1_from = Snap.select('#path4223')
    step1_to = Snap.select('#path4223')
    step_1_from_points = step1_from.node.getAttribute('d')
    step_1_to_points = step1_to.node.getAttribute('d')

    step_1_to_points = 'm 159.1548,176.29242 0,-54.29874 0.42286,-50.353445 48.84197,-0.01533 49.26483,0 0,52.559871 -0.40746,53.86401 -61.48863,-0.4347 c -36.9478,-0.26125 -34.58087,0.8694 -36.63357,-1.32058 z'

    step2_from = Snap.select('#path4223')
    step3_from = Snap.select('#path4225')

    step1Start = ->
      step1_from.animate { d: step_1_to_points }, 1000
      return

    step_2_to_points = 'm 209.68446,165.80087 0,-48.85611 0.206,-45.306282 23.794,-0.01333 24,0 0,47.291543 -0.1985,48.46497 -29.95498,-0.39114 c -17.9996,-0.23503 -16.84652,0.78228 -17.84652,-1.1882 z'
    step_3_to_points = 'm 157.68446,119.48563 0.007,-47.450415 23.9925,-0.41081 24,0 0,47.861225 0,47.86123 -24,0 -24,0 z'

    step2Start = ->
      step2_from.animate { d: step_2_to_points }, 1000, mina.backout, step3Start
      return

    step3Start = ->
      step3_from.animate { d: step_3_to_points }, 1000, mina.backout
      return

    step1Start()

  $(window).on 'load', ->
    $(window).scroll ->

      if $(window).scrollTop() >= $("#full-range-animate1").offset().top - 350
        $("#full-range-animate1").addClass("animate")
        $("#full-range-animate2").addClass("animate")


      if $(window).scrollTop() >= $("#meddev_animation1").offset().top - 350
        $("#meddev_animation1").addClass("animate")

      if $(window).scrollTop() >= $("#outsourcing_animate").offset().top - 350
        $("#outsourcing_animate").addClass("animate")

      if $(window).scrollTop() >= $("#study_management_2").offset().top - 350
        study_management_2_animation()

      return

  $ ->
    $('.show_more').on 'click', (e) ->
      e.preventDefault()
      target = $(@).data('target')
      $(target).toggleClass('show')
      $(@).addClass('fadeOutBtn')
