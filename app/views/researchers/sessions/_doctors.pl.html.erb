<p>Portal umożliwia Państwu proste zarządzanie procesem zwrotu kosztów podróży pacjentów uczestniczących w badaniach klinicznych.  Hasło do konta otrzymają Państwo w wiadomości wysłanej na Państwa adres email. Jeż<PERSON> jeszcze nie otrzymali Państwo hasła, prosimy o kontakt z monitorem odpowiedzialnym za Państwa ośrodek.
</p>
<p>Po zalogowaniu do portalu będą Państwo mogli zarządzać  procesem rozliczania kosztów podróży pacjentów w oparciu o harmonogram wizyt w Państwa ośrodku. Będą Państwo mieli także możliwość ustawiania dodatkowych przypomnień dla pacjentów (np. o badaniach lub o zadaniach pacjentów, które zostały przewidziane w protokole).
</p>
<p>  Poniżej ilustrujemy 4 kroki potrzebne do zlecenia płatności dla pacjenta.
</p>

<p><strong>Krok 1.</strong> Po zalogowaniu i wskazaniu pacjenta zobaczą Państwo listę jego wizyt przewidzianych w protokole badania. Dla wybranej wizyty ustalają Państwo jej termin klikając na przycisk "Ustal termin" poniżej.
</p>
    <%= image_tag 'rtut1.png', :style => 'width: 100%;', :class => 'img-polaroid' %>

<p>Przypomnienie SMS o zaplanowanej wizycie zostanie automatycznie wysłane do pacjenta na 7 dni oraz ponownie na 1 dzień przed wizytą.
</p>

<p><strong>Krok 2.</strong> Odbytą wizytę potwierdzają Państwo klikając na zielony przycisk obok daty. System jest gotowy do rozliczenia kosztów podróży pacjenta za tę wizytę.
</p>
  <%= image_tag 'rtut2.png', :style => 'width: 100%;', :class => 'img-polaroid'  %>

<p><strong>Krok 3.</strong> Kwotę zwrotu wpisują Państwo po lewej stronie przycisku "Zapłać". Tę czynność może także wykonać monitor podczas wizyty w ośrodku.
</p>
  <%= image_tag 'rtut3.png', :style => 'width: 100%;', :class => 'img-polaroid'  %>

<p><strong>Krok 4.</strong> Zlecają Państwo przelew bezpośrednio na konto bankowe pacjenta klikając na przycisk "Zapłać". Pacjent otrzymuje powiadomienie SMS o wysłanym przelewie.
</p>
  <%= image_tag 'rtut4.png', :style => 'width: 100%;', :class => 'img-polaroid'  %>
<p>Zależnie od preferencji Sponsora przelew może być także zlecany przez monitora lub może wymagać dodatkowej autoryzacji. Po wykonaniu przelewu wizyta pacjenta uzyskuje status "Rozliczona" jak na przykładzie poniżej.
</p>
  <%= image_tag 'rtut5.png', :style => 'width: 100%;', :class => 'img-polaroid'  %>
<p>System udostępni Państwu raporty dotyczące oczekujących i wykonanych płatności. Szczegółową instrukcję otrzymają Państwo po zalogowaniu.
</p>
