<h2>W celu uzyskania dostępu do systemu, proszę uzu<PERSON>ł<PERSON>ć poniższe dane.</h2>

<%= simple_form_for(resource, :as => resource_name, :url => password_path(resource_name),
    :html => { :method => :put }) do |f| %>
  <%#= f.error_notification %>

  <%= f.input :email, :label => t('.email') %>

  <%= f.input :first_name, :label => t('.first_name') %>
  <%= f.input :last_name, :label => t('.last_name') %>
  <%= f.input :phone_number, :label => t('.phone_number') %>
  <%= f.input :reset_password_token %> <%#, input_html: {value: params[:reset_password_token] } %> <%#, :as => :hidden %>
  <%= f.full_error :reset_password_token %>

  <div class="form-inputs">
    <%= f.input :password, :label => t('.new_password'), :required => true, :autofocus => true %>
    <%= f.input :password_confirmation, :label => t('.confirm_new_password'), :required => true %>
  </div>

  <%= f.input :data_confirmed, input_html: { checked: true }, as: :hidden %>

  <div class="form-actions">
    <%= f.button :submit, t('.change_password') %>
  </div>
<% end %>


<p style="margin-top: 20px;">
<%= render "researchers/shared/links" %>
</p>