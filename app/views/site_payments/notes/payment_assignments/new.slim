.row
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Assign Payments", subtitle: "Note# #{ @note.id }")

.tab-content.vertical_1.card.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container
      = simple_form_for @note, url: site_payments_note_payment_assignments_path(@note), method: :post, html: { data: { behavior: 'site_payments_note_payment_assignments_form', amount_available: @note.amount } } do |f|
        - @note.errors.any?
          .form-group.has-danger
            .md-form
              = f.error :base
        table.table
          thead
            th Researcher
            th Amount
            th
          tbody
            = f.simple_fields_for :site_payments_payments do |p|
              tr
                td = p.object.to_researcher.name_and_email
                td = p.input :amount, label: false, input_html: { id: "payment_amount_for_researcher_#{ p.object.to_researcher_id }" }
                td = p.input :_destroy, as: :boolean, label: 'Remove'
                = p.hidden_field :to_researcher_id
                = p.hidden_field :added_by_researcher_id
        p
          | Amount remaining:&nbsp;
          span#amount_remaining #{ number_to_currency @note.amount, unit: '' }
          span &nbsp;#{ @note.currency }
        p
          | Amount used:&nbsp;
          span#amount_used 0
          span &nbsp;#{ @note.currency }
        = f.submit 'Confirm', class: 'submit_btn'