table.table
  thead
    tr
      th.wd_210 Name
      th.wd_120 Role
      th.wd_160 Phone
      th style=("width: auto") Email
      th.fit_to_content
  tbody
    - if researchers_and_roles.try(:any?)
      - researchers_and_roles.each do |r, pr|
        - next unless r
        tr
          td = link_to manager_name(manager: r, researcher_role: researcher_role), v2_sponsor_researcher_path(r, project_id: project)
          td = pr
          td = manager_phone(manager: r, researcher_role: researcher_role)
          td = manager_email(manager: r, researcher_role: researcher_role)
          td.text-xs-center
            = dropdown_btn do
              = link_to 'View', v2_sponsor_researcher_path(r, project_id: project), class: 'dropdown-item'
              = link_to 'Last logins', v2_sponsor_researcher_session_histories_path(r), class: 'dropdown-item'
              / = link_to 'Remove', v2_sponsor_researcher_invitation_removals_path(r), method: :post, class: 'dropdown-item'
              = link_to 'Remind', v2_sponsor_researcher_invitation_reminders_path(r), method: :post, class: 'dropdown-item' unless r.data_confirmed
              = link_to_remove_researcher_from_project(researcher: r, removing_researcher: current_researcher, project: project)
    - else
      tr
        td colspan="99"
          | User not found.
