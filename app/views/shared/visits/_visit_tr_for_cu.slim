tr data-id="#{ visit.id }"
  td.text-xs-left.relative.clinical_user_status.visit_high_priority class="visit_id_#{visit.try(:id)}"
    = visit_high_priority_icon(visit: visit, link: false)
    - if visit.visit_date
      = l(visit.visit_date, format: :only_date_dash)
      = visit_self_added_icon(visit: visit)

    - else
      span data-toggle='tooltip' data-title="<PERSON>e maj<PERSON>stwo uprawnień."
        | Podaj datę
      i.fa.fa-clock-o style=("color: #737373;margin-left: 6px;margin-right: 0;margin-top: -5px;display: inline-block")

  td.text-xs-left.visit_name
    = link_to visit.name, v2_subject_visit_path(visit)
  td.text-xs-left.status = actual_visit_state_date(visit: visit)
  td.text-xs-right = visit.amount ? number_to_currency(visit.amount, unit: '') : '-'
  td.text-xs-left.fit_to_content = currency_symbol(project: visit.project)
  td.text-xs-center.visit_high_priority class="visit_id_#{visit.try(:id)}"
    = visit_high_priority_icon(visit: visit, link: false)
  td.text-xs-center
    = dropdown_btn(btn_text: "Wybierz") do
      = link_to 'Informacje o kosztach', v2_subject_visit_path(visit), class: 'dropdown-item'
      = visit_transfer_confirmation_link(visit: visit, link_class: 'dropdown-item') if visit.transfer_sent_to_bank?
