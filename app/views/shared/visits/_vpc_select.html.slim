.form-group.select.optional.visit_visit_payment_categorizations_visit_payment_category_id
  .md-form
    select#visit_visit_payment_categorizations_attributes_0_visit_payment_category_id.select.optional.md-12.mdb-select name="visit[visit_payment_categorizations_attributes][0][visit_payment_category_id]"
      optgroup label=""
        - @visit.available_cost_categories_sorted_by_use_frequency.each do |vpc|
          option value=vpc.id selected="#{f.object.visit_payment_category_id == vpc.id}" = vpc.name
      - unused_vpc = @visit.unused_vpc
      - if unused_vpc.any?
        optgroup label=""
        - unused_vpc.each do |vpc|
          option value=vpc.id selected="#{f.object.visit_payment_category_id == vpc.id}" = vpc.name
      / option value="367"  Accommodation
      / option value="172"  Bus
      / option selected="selected" value="484"  Cleaning
      / option value="445"  Clothes
      / option value="328"  Food
      / option value="289"  Gasoline
      / option value="250"  Kilometers
      / option value="19"  Medical transport (ambulance)
      / option value="994"  Motorway toll
      / option value="15"  Parking
      / option value="406"  Phone
      / option value="94"  Taxi
      / option value="133"  Train
