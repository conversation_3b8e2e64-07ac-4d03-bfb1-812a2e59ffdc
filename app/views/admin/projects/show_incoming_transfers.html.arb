panel "Zasilenia" do
  table_for clinical_transfers do
    column :id do |x|
    link_to x.id, admin_clinical_transfer_path(x)
    end
    column :clinical_user_id, :sortable => :clinical_user_id do |x|
    link_to x.clinical_user.display_name.truncate(32), admin_clinical_user_path(x.clinical_user_id) if x.clinical_user
    end
    column "Numer pacjenta" do |x|
    link_to x.clinical_user.patient_code, admin_clinical_user_path(x.clinical_user_id) if x.clinical_user
    end
    column :researcher_id, :sortable => :researcher_id do |x|
    link_to x.researcher.display_name.truncate(32), admin_researcher_path(x.researcher_id) if x.researcher
    end
    column :visit_id, :sortable => :visit_id do |x|
    link_to x.visit.name.truncate(32), admin_visit_path(x.visit_id) if x.visit
    end
    column :project_id, :sortable => :project_id do |x|
    link_to x.project.name.truncate(32), admin_project_path(x.project_id) if x.project
    end
    column :amount do |x|
    "%.2f" % x.amount
    end
    column "SIMP", :simp_file_id do |x|
    link_to x.simp_file_id, admin_simp_file_path(x.simp_file_id) if x.simp_file_id
    end
    column "Zasilający Citi", :in_citi_file_id do |x|
    link_to x.in_citi_file_id, admin_citi_file_path(x.in_citi_file_id) if x.in_citi_file_id
    end
    column "Citi wychodzący", :citi_file_id do |x|
    link_to x.citi_file_id, admin_citi_file_path(x.citi_file_id) if x.citi_file_id
    end
    column "Zestawienie", :project_debit_id do |x|
    link_to x.project_debit_id, admin_project_debit_path(x.project_debit_id) if x.project_debit_id
    end
    column "Nota", :project_debit_summary_id do |x|
    link_to x.project_debit_summary_id, admin_project_debit_summaries_path(x.project_debit_summary_id) if x.project_debit_summary_id
    end
    column :created_at
    column :account_number
    column :flow_direction, :sortable => :flow_direction do |f|
    f.flow_direction == 'i' ? "Incoming" : "Outgoing"
    end
    column :state do |x|
    x.status_human_name
    end
    column "Data zmiany statusu" do |x|
    x.status_change_date
    end
    column "Data zlecenia" do |x|
    x.created_at
    end

  end
end