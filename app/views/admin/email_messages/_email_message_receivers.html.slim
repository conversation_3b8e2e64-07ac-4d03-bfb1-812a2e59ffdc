scss:
  span.status {
    padding-left: 10px;
    padding-right: 10px;

    &.state_p {
      color: #969696;
    }

    &.state_s {
      color: #01d40b;
    }

    &.state_e {
      color: #d40101;
    }
  }

  span.role {
    color: blue;
    padding-right: 20px;
  }

.panel
  h3 Odbiorcy
  .panel_contents
    ul
      - if resource.email_message_receivers.any?
        - resource.email_message_receivers.each do |emr|
          li
            span class="status state_#{emr.state}"
              = emr.status_human_name
            span class="role"
              = emr.researcher.highest_role
            |#{emr.researcher} (#{emr.researcher.email})

      - else
        | Brak
