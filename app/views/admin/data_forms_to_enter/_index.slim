- FeePlan.all.reverse.each do |plan|
  h2 = plan.title_short

  h3 Maja juz srodki na LMP
  - pagination_param = "paginate_#{plan.internal_name}"
  - patients = clinical_users.joins(:project).merge(Project.with_plan(plan)).with_money_in_lmp_acc.page(params[pagination_param])
  - if patients.any?
    = render 'table', patients: patients, pagination_param: pagination_param
  - else
    p Nie ma zadnych formularzy do wprowadzenia.

  h3 Projekt pre-paid
  - pagination_param = "paginate_#{plan.internal_name}"
  - patients = clinical_users.joins(:project).merge(Project.with_plan(plan)).where(projects: { debit_allowed: false }).page(params[pagination_param])
  - if patients.any?
    = render 'table', patients: patients, pagination_param: pagination_param
  - else
    p Nie ma zadnych formularzy do wprowadzenia.

  h3 Projekt post-paid
  - pagination_param = "paginate_#{plan.internal_name}"
  - patients = clinical_users.joins(:project).merge(Project.with_plan(plan)).where(projects: { debit_allowed: true }).page(params[pagination_param])
  - if patients.any?
    = render 'table', patients: patients, pagination_param: pagination_param
  - else
    p Nie ma zadnych formularzy do wprowadzenia.

  h3 Pozostali
  - pagination_param = "paginate_#{plan.internal_name}"
  - patients = ClinicalUsers::ForDataEntry::OthersQuery.run(plan: plan).page(params[pagination_param])
  - if patients.any?
    = render 'table', patients: patients, pagination_param: pagination_param
  - else
    p Nie ma zadnych formularzy do wprowadzenia.

  br
  br
  br