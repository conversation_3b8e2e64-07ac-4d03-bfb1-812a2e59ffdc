wb = xlsx_package.workbook
padded_number = wb.styles.add_style :format_code => "00###"
bold = wb.styles.add_style b: true

wb.add_worksheet(name: 'Podsumowa<PERSON>') do |ws|
  balance = @balance
  render 'admin/shared/header', ws: ws, start_date: @date.beginning_of_month, end_date: @date.end_of_month, balance: balance, bold: bold
  
  ws.add_row ["Data operacji", "Beneficjent", "Numer rachunku nadawcy", "Numer protokołu", "CRO", "Numer noty obciażeniowej", "Numer rachunku", "Kwota DT", "Kwota CT", "Saldo po operacji"], style: [bold, bold, bold, bold, bold, bold, bold, bold, bold, bold]
  @all_transfers.each do |t|
    if t.project_debit_summary_id
      sign = '+'
      balance += t.amount
    else
      sign = '-'
      balance -= t.amount
    end
    
    row = [
      t.status_change_date.strftime('%d/%m/%y'),
      t.clinical_user.try(:patient_code),
      ClinicalTransfer.format_account_number(t.clinical_user.try(:pir_account_number)),
      t.get_project.try(:clinical_protocol_code),
      t.get_project.try(:cro).try(:name),
      transfer_note_number(transfer: t),
      t.formatted_account_number(force_unmask: true),
      sign == '-' ?  "#{sign}#{ t.amount }" : '',
      sign == '+' ?  "#{sign}#{ t.amount }" : '',
      "#{balance}"
    ]
    ws.add_row row, style: [nil, nil, padded_number, padded_number, nil,padded_number, padded_number, nil, nil]
  end
  ws.add_row []
  ws.add_row []
  ws.add_row ['Liczba obciążeń', @transfers.size]
  ws.add_row ['Kwota obciążeń', "-#{@transfers.map(&:amount).sum}"]
  ws.add_row ['Liczba zasileń', @pds_transfers.size]
  ws.add_row ['Kwota zasileń', "#{@pds_transfers.map(&:amount).sum}"]
  ws.add_row ["Saldo na dzień #{ @date.end_of_month.strftime('%d/%m/%y') }:", "#{ balance }"]
end

wb.add_worksheet(name: 'CRO') do |ws|
  ws.add_row ['TRANSAKCJE KLIENTÓW - PAYCLINICAL.COM']
  ws.add_row ["Raport za okres:", "#{ @date.beginning_of_month } - #{ @date.end_of_month }"]
  ws.add_row []
  ws.add_row ['Nazwa CRO', 'Liczba zasileń', 'Kwota zasileń', 'Liczba obciążeń', 'Kwota obciążeń']
  
  @cros.each do |cro|
    inc_t = @pds_transfers.select { |t| t.cro == cro }
    out_t = @transfers.select { |t| t.cro == cro }
    ws.add_row [
      cro.name,
      inc_t.size,
      inc_t.map(&:amount).sum,
      out_t.size,
      out_t.map(&:amount).sum
    ]
  end
end

wb.add_worksheet(name: 'Konta projektow przedplaconych') do |ws|
  ws.add_row ['Numer protokołu', 'Nazwa CRO', 'Numer rachunku PIR projektu', 'Saldo projektu']
  padded_number = wb.styles.add_style :format_code => "00###"
  total_balance = 0
  @prepaid_projects.each do |p|
    balance = p.project_saldo_on_day(@date.to_time.end_of_month)
    total_balance += balance
    ws.add_row [
      p.clinical_protocol_code,
      p.cro.try(:name),
      ClinicalTransfer.format_account_number(p.pir_account_number),
      balance
      ], style: [nil, nil, padded_number, nil]
    end
    
    ws.add_row []
    ws.add_row []
    ws.add_row ['Liczba projektów pre-paid:', @prepaid_projects.size]
    ws.add_row ['Środki łącznie:', "#{total_balance}"]
  end
  
  wb.add_worksheet(name: 'Rachunki powiernicze uczestnikow'[0..30]) do |ws|
    ws.add_row ['Numer uczestnika', 'Id Uczestnika', 'Numer protokolu', 'Numer rachunku LMP uczestnika', 'Saldo rachunku uczestnika']
    padded_number = wb.styles.add_style :format_code => "00###"
    total_balance = 0
    @lmp_patients.each do |cu|
      balance = cu.internal_balance_on_day(end_date: @end_date).try(:abs)
      total_balance += balance
      ws.add_row [
        cu.patient_code,
        cu.id,
        cu.clinical_protocol_code,
        ClinicalTransfer.format_account_number(cu.pir_account_number),
        balance
        ], style: [nil, nil, nil,padded_number, nil]
      end
      
      ws.add_row []
      ws.add_row []
      ws.add_row ['Liczba uczestnikow majacych srodki:', @lmp_patients.size]
      ws.add_row ['Środki uczestnikow łącznie:', total_balance]
    end
    
    wb.add_worksheet(name: 'Noty obciążeniowe') do |ws|
      ws.add_row ['Numer noty', 'Data wystawienia', 'Numer protokolu', 'Nazwa CRO', 'Kwota noty', 'Data zapłaty','Kwota pozostala do zaplaty']
      total_balance = 0
      @pds.each do |pds|
        balance = pds.balance_on_date(end_date: @end_date)
        total_balance += balance
        ws.add_row [
          pds.note_number,
          pds.created_at.strftime('%d/%m/%y'),
          pds.clinical_protocol_code,
          pds.contract_research_organization.try(:name),
          pds.original_amount,
          pds_paid_date(pds: pds, end_date: @end_date),
          balance
        ]
      end
      
      ws.add_row []
      ws.add_row []
      ws.add_row ['Liczba otwartych not obciazeniowych:', @pds.size]
      ws.add_row ['Kwota niezaplaconych naleznosci:', total_balance]
    end

    wb.add_worksheet(name: 'Kredyty') do |ws|
      balance = @balance
      render 'admin/shared/header', ws: ws, start_date: @date.beginning_of_month, end_date: @date.end_of_month, balance: balance, bold: bold

      ws.add_row ['CRO', 'Saldo otwarcia', 'liczba kredytow udzielpnych w wybranym miesiacu', 'kwota kredytow udzielonych', 'liczba splaconych', 'kwota splaconych',  'saldo kredytow na koniec okresu miseiaca']
      ContractResearchOrganization.order('name asc').each do |cro|
        ws.add_row [
          cro.name,
          cro.project_debit_summaries.credit_note_unpaid.select { |pds| pds.credited_at && pds.credited_at <= @start_datetime }.map { |pds| pds.original_amount.abs }.sum,
          cro.project_debit_summaries.credit.select { |pds| pds.credited_at && pds.credited_at >= @start_datetime && pds.credited_at <= @end_date }.size,
          cro.project_debit_summaries.credit.map(&:original_amount).sum,
          cro.project_debit_summaries.credit_note_paid.size,
          cro.project_debit_summaries.credit_note_paid.map(&:original_amount).sum,
          cro.project_debit_summaries.credit.select { |pds| pds.credited_at && pds.credited_at <= @end_date }.map(&:original_amount).sum
        ]
      end
    end
    