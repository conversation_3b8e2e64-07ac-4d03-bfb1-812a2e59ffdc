= simple_form_for Correspondence.new, url: admin_correspondences_path do |f|
  = f.input :received_date, default: Date.today
  = f.input :delivery_type, collection: Correspondence::DELIVERY_TYPES, include_blank: false
  = f.input :state, collection: Correspondence::STATES, include_blank: false
  = f.input :contract_research_organization_id, collection: ContractResearchOrganization.all
  = f.input :project_id, collection: Project.all
  = f.input :clinical_center_id, collection: ClinicalCenter.all
  = f.input :comments
  = f.submit