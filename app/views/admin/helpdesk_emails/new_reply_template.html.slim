p <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> do: #{ resource.from }

= simple_form_for @form, url: reply_template_admin_helpdesk_email_path(resource.id) do |f|
  = f.hidden_field :helpdesk_email_id

  = f.input :template, collection: @form.templates, label_method: Proc.new {|x| x[:subject]}, value_method: Proc.new {|x| x[:body]}
  = f.hidden_field :subject
  = f.input :body, as: :text

  = f.submit "Odpowiedz"


div style='margin-bottom: 5rem;'
- @mailer = HelpdeskMailer.with(helpdesk_email: resource, body: '%BODY%', researcher: @researcher, subject: '%SUBJECT%').custom_reply
= render 'admin/mailer_preview', mailer: @mailer, link_path: nil


coffee:
  $('#helpdesk_email_reply_form_template').on 'change', (e) ->
    subject = $("#helpdesk_email_reply_form_template option:selected").text();
    $('#helpdesk_email_reply_form_subject ').val( subject )
    $('#helpdesk_email_reply_form_body').val($(this).val())
    e.preventDefault()
