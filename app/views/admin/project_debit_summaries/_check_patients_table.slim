- patient_name_method = local_assigns.fetch :patient_name_method, :full_name

table
  thead
    th ID
    th Numer pacjenta
    th Site
    th I<PERSON>ę i nazwisko
    th Potwierdzony
    th Destination
    th Środki na nocie
    th Środki w LMP
    th Action Required?
  tbody
    - clinical_users.each do |cu|
      tr
        td = link_to cu.id, [:admin, cu]
        td = link_to cu.patient_code, [:admin, cu]
        td = link_to cu.clinical_center, [:admin, cu.clinical_center]
        td = link_to cu.send(patient_name_method), [:admin, cu]
        td
          - if cu.perm_data
            span Tak
          - else
            span style='color: red;' NIE
        td
          = cu.transfer_destination
        td = cu.transfers_in_summary(pds).sum(:amount)
        td = cu.clinical_transfers.processing.sum(:amount)
        td
          - color = cu.action_required ? 'red' : 'black'
          span style="color: #{color}" #{cu.action_required ? 'Tak' : 'Nie'}