h3 Podaj plik CSV aby wygenerowac nowy plik z wieksza iloscia danych.

= form_tag admin_alior_csv_generate_csv_path, multipart: true do |f|
  = label_tag :file, 'Plik Alior CSV'
  br
  = file_field_tag :file
  br
  br
  = submit_tag 'Parse', data: { disable_with: false }


br
br
br
br

h3 Podaj plik CSV aby zliczyć transfery przychodzące i wychodzące.

= form_tag admin_alior_csv_generate_summary_csv_path, multipart: true do |f|
  = label_tag :file, 'Plik Alior CSV'
  br
  = file_field_tag :file
  br
  br
  = submit_tag 'Get summary'


