wb = xlsx_package.workbook
padded_number = wb.styles.add_style :format_code => "00###"
bold = wb.styles.add_style b: true

wb.add_worksheet(name: 'Report') do |ws|
  ws.add_row ['Study protocol', 'Site', 'Subject', 'Activation date', 'Due amount']

  @patients.each do |cu|
    ws.add_row [
      cu.clinical_protocol_code,
      cu.clinical_center_code,
      cu.patient_code,
      cu.data_confirmed_at,
      ClinicalUsers::MoneyInLmpAndWaitingNotes.call(clinical_user: cu)
    ]
  end
end