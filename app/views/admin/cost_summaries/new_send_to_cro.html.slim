= form_tag preview_send_to_cro_admin_cost_summaries_path, method: :get do
  = label_tag :cro_id, 'CRO'
  = select_tag :cro_id, options_from_collection_for_select(ContractResearchOrganization.order('name asc'), :id, :name)
  br
  br
  = label_tag 'date_from', 'Poczatek okresu'
  = date_field_tag 'date_from', Time.zone.now.prev_quarter.beginning_of_quarter.to_date
  br
  br
  = label_tag 'date_to', 'Koniec okresu'
  = date_field_tag 'date_to', Time.zone.now.prev_quarter.end_of_quarter.to_date
  br
  br
  = submit_tag 'Preview'
