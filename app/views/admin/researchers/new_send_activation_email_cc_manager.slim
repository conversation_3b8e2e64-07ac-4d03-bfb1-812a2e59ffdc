= form_tag send_activation_email_cc_manager_admin_researcher_path(resource), method: :get do |f|
  = label_tag :cra_email, "CRA/CRA+"
  br
  = select_tag :cra_email, options_from_collection_for_select(@cra, "email", "name_and_email")
  br
  br
  = label_tag :manager_email, "Manager+"
  br
  - @managers.each do |m|
    = check_box_tag "manager_email[#{m.email}]"
    = label_tag "manager_email[#{m.email}]", m.name_and_email
    br
  br
  br
  = submit_tag 'Wyślij'
