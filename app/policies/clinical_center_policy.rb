class ClinicalCenterPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      scope
    end
  end

  def show_sp_summary?
    user.sp_admin_at_site?(record) || !user.role_at_center(record, get_role_name: false).investigator?
  end

  def add_action_item?
    if record.project.free_plan?
      project_role == 'Operator'
    else
      true
    end
  end

  def close?
    return false unless project_role
    ['Manager', 'Operator'].include? project_role
  end

  def manage_messages?
    is_cra_or_manager_in_cc?
  end

  def show?
    user.clinical_centers.where(project_id: record.try(:project_id)).pluck(:id).include? record.id
  end

  def update?
    is_cra_or_manager_in_cc? || project_role == 'Operator'
  end

  def activate?
    ['Manager', 'Operator'].include? project_role
  end

  def access_messages?
    is_cra_or_manager_in_cc?
  end

  def set_primary_cra?
    is_manager_in_cc?
  end

  def is_cra_or_manager_in_cc?
    user.clinical_center_roles.joins(:project_role).where(project_roles: { project_role: ['CRA', 'CRA+', 'Manager', 'Operator'], project_id: record.project_id }, clinical_center_roles: { clinical_center_id: record.id }).exists?
  end

  def is_manager_in_cc?
    user.clinical_center_roles.joins(:project_role).where(project_roles: { project_role: ['Manager'], project_id: record.project_id }, clinical_center_roles: { clinical_center_id: record.id }).exists?
  end

  def new_clinical_user?
    ProjectRole::ROLES_CREATE.include? project_role
  end

  def project_role
    user.role_at_center(record)
  end
end
