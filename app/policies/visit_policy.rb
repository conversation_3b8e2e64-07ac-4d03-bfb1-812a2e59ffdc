class VisitPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      scope
    end
  end

  def send_reminder?
    ['Investigator', 'Investigator+', 'Manager', 'Operator', 'CTA'].include? role
  end

  def reset?
    !record.paid? && !record.clinical_transfer.present?
  end

  def show?
    return false unless record.clinical_user
    return (record.clinical_user_id == user.id) if user.is_a?(ClinicalUser)

    ClinicalUsersAvailableToResearcher.run(project: record.clinical_user.project, researcher: user).pluck(:id).include? record.clinical_user_id
  end

  def accept?
    role = user.role_in_project(project).try(:project_role)
    ClinicalTransfer::PROJECT_ROLES_CAN_ACCEPT.include? role
  end

  def update?
    if user.is_a? ClinicalUser
      return (record.clinical_user_id == user.id) && record.user_can_edit_visit
    end

    return false if role == 'Observer'
    if role == 'Investigator' || role == 'CTA'
      return researcher_has_access_to_clinical_center? && record.inv_can_edit_visit
    end
    return researcher_has_access_to_clinical_center? if ['CRA', 'Investigator+'].include?(role)
    return true
  end

  def pay?
    return false if role == 'Investigator'
    return false if role == 'CTA'
    return false if role == 'Observer'
    return researcher_has_access_to_clinical_center? if ['CRA', 'Investigator+'].include?(role)
    return true
  end

  def cancel?
    record.can_be_reverted? && role == 'Manager'
  end

  def edit?
    role
  end

  def destroy?
    #return false if record.clinical_transfers.not_cancelled.any?
    # TODO: usuwanie wizyty ktora ma jakieś clinical_transfers
    return false if record.clinical_transfers.any?
    if role == 'Manager'
      record.exceptional?
    else
      record.researcher_id == user.id
    end
  end

  def remind?
    return false unless record.may_send_reminder?
    return (record.clinical_user_id == user.id) if user.is_a?(ClinicalUser)
    return false if role == 'Observer'
    return false unless role
    return true
  end

  def view_cost_document?
    return false unless role.present?
    if project.cra_manager_can_view_visit_documents
      return true
    else
      ['Investigator', 'Investigator+', 'Operator', 'CTA'].include? role
    end
  end

  def set_as_not_applicable?
    !record.has_transfer?
  end


  def project
    record.project
  end

  def role
    user.try(:role_in_project, project).try(:project_role)
  end

  def researcher_has_access_to_clinical_center?
    user.clinical_center_roles.where(clinical_center_id: clinical_center.id).exists?
  end

  def clinical_center
    record.clinical_center
  end

  def cro
    record.cro
  end
end
