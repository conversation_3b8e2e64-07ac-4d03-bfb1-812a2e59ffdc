class ProjectPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      scope
    end
  end

  def add_funds?
    user.cro_roles.where(contract_research_organization_id: record.contract_research_organization_id).exists?
  end

  def access_site_payments_projects_summaries?
    role && (!role.investigator? || user.sp_admin_at_site_in_project?(record))
  end

  def add_center?
    return false unless project_role

    ['Manager', 'CRA', 'CRA+', 'Operator'].include? project_role
  end

  def add_shipment?
    project_role == 'Operator'
  end

  def show_shipment?
    add_shipment?
  end

  def suspend?
    ['CRA', 'CRA+', 'Manager', 'Operator'].include?(project_role)
  end

  def can_see_balance?
    ['CRA', 'CRA+', 'Manager', 'Operator'].include? project_role
  end

  def update?
    user.payclinical_employee || user.is_super_manager_in_project?(record)
  end

  def index?
    return false if all_researcher_roles == ['Investigator']
    return false if all_researcher_roles == ['CTA']

    true
  end

  def show?
    user.projects.pluck(:id).include? record.id
  end

  def show_history?
    project_role && !['CTA', 'Investigator', 'Investigator+'].include?(project_role)
  end

  def new?
    user.cro_roles.exists?
  end

  def new_clinical_user?
    ProjectRole::ROLES_CREATE.include? project_role
  end

  def create?
    return false unless user.managed_cro

    user.managed_cro == record.contract_research_organization
  end

  def upload_clinical_user_forms?
    return false unless project_role

    ['CTA', 'Investigator', 'Investigator+', 'Operator'].include? project_role
  end

  def is_manager?
    user.is_manager_in_project?(record)
  end

  def is_cra_or_manager?
    return false unless project_role

    ['Manager', 'CRA', 'CRA+'].include? project_role
  end

  def invite_researcher?
    return false unless project_role

    ['Manager', 'CRA', 'CRA+', 'Operator'].include? project_role
  end

  def access_pd_summaries?
    return false unless project_role

    ['CRA', 'CRA+', 'Manager', 'Operator', 'Observer'].include? project_role
  end

  def add_project_visit_templates?
    return false unless project_role

    ['CRA', 'CRA+', 'Manager', 'Operator'].include? project_role
  end

  def project_role
    user.role_in_project(record).try(:project_role)
  end

  def role
    user.role_in_project(record)
  end
end
