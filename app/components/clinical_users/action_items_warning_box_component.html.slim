.summary_section.warning.relative
  - if @clinical_user.show_action_items?
    - if unresolved_actions_count > 0
      = action_items_summary
      .mt-0 style=("margin-bottom: 10px;")
      = link_to 'Show issues', '#action_items_table', class: 'default_sm_btn'
  - else
    = helpers.clinical_user_action_required_notice(clinical_user: @clinical_user)
    - if @clinical_user.user_action_requirements.not_resolved.any?
      .mt-0 style=("margin-bottom: 10px;")
      = link_to 'Show issues', '#actions_required_table', class: 'default_sm_btn'