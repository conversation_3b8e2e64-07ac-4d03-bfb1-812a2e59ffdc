class SitePayments::Summaries::ResearcherAccountNumberComponent < ViewComponent::Base
  def initialize(researcher:, currency:, viewer:)
    @researcher = researcher
    @currency = currency
    @viewer = viewer
  end

  def account_number
    return '-' unless currency_account

    if CurrencyAccountPolicy.new(@viewer, currency_account).show?
      link_to currency_account.account_number || '-', Rails.application.routes.url_helpers.site_payments_currency_account_path(currency_account)
    else
      currency_account.account_number
    end
  end

  def currency_account
    @researcher.currency_accounts.where(currency: @currency).last
  end

  def second_line
    if bank_account
      "Linked to #{ bank_account.number }"
    else
      "Please enter the account no."
    end
  end

  def bank_account
    @bank_account ||= @researcher.bank_accounts.where(currency: @currency).last
  end
end
