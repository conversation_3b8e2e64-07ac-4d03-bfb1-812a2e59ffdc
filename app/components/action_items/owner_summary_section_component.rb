class ActionItems::OwnerSummarySectionComponent < ViewComponent::Base
  include ActionItems::TargetSummarySection::Shared

  def initialize(action_item:, klass: nil)
    @action_item = action_item
    @klass = klass
  end

  def call
    render ActionItems::TargetSummarySectionComponent.new(klass: klass, owner_line: owner_line, observers_line: observers_line)
  end

  def panel_class
    'default'
  end

  def owner_line
    if target
      "This action item is assigned to: #{ target.full_name } (#{ target.email })"
    else
      "This action item is assigned to: None yet. Please select another owner."
    end
  end

  def target
    Array(@action_item.owner_researcher).first
  end
end
