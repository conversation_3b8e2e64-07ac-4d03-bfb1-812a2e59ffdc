table.table#action_items_table
  thead
    th style='width: 120px;' Date
    th ID#
    th Owner
    th Issue
    th Sponsor/Study
    th Context
    th Status
    th
  tbody
    - @action_items.each do |ai|
      tr id="ai_#{ ai.id }"
        td
          | #{ ai.created_at.strftime("%d-%m-%Y") }
          br
          span.row_second_line_2.protip data-pt-title='Deadline'
            = ai.deadline.strftime("%d-%m-%Y")
        td = link_to sprintf('%.5i', ai.id), v2_sponsor_action_item_path(ai)
        td
          - if ai.added_by
            = link_to ai.added_by.short_full_name, v2_sponsor_researcher_path(ai.added_by)
            br
            span.row_second_line_2 = ai.added_by.role_in_project(ai.project)&.project_role
        td = truncate(ai.description)
        td = render SponsorStudyComponent.new(cro: ai.cro, project: ai.project)
        td = render ActionItems::ResourceLinkComponent.new(action_item: ai)
        td = ai.status
        td = link_to 'Show', v2_sponsor_action_item_path(ai), class: 'default_btn'