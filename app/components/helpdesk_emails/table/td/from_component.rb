module HelpdeskEmails
  module Table
    module Td
      class FromComponent < ViewComponent::Base
        def initialize(helpdesk_email:, params: {})
          @helpdesk_email = helpdesk_email
          @params = params
        end

        def td_class
          result = 'helpdesk_email_researcher'
          result << ' researcher_found' if @helpdesk_email.researcher.present?
          result
        end

        def from_info
          if @helpdesk_email.researcher
            link_to researcher_name || @helpdesk_email.from, v2_sponsor_researcher_path(@helpdesk_email.researcher), class: 'hover_orange'
          else
            link_to @helpdesk_email.from&.split('@')&.first, 'javascript:;', data: { toggle: 'tooltip', title: @helpdesk_email.from }
          end
        end

        def researcher
          @researcher ||= @helpdesk_email.researcher
        end

        def researcher_name
          if researcher
            "#{ researcher.first_name&.first }. #{ researcher.last_name }"
          end
        end

        def all_emails
          @all_emails ||= HelpdeskEmail
            .where(from: @helpdesk_email.from )
            .new_or_pending
            .not_responses
        end

        def new_or_pending_emails_size
          all_emails
            .where(
              message_date: ..@helpdesk_email.message_date,
              id: ..@helpdesk_email.id
            ).size
        end

        def all_emails_size
          all_emails.size
        end

        def role_span
          klass = highest_role == 'Manager' ? 'red' : 'black_color'

          tag.span class: klass do
            highest_role
          end
        end

        def highest_role
          return unless researcher

          researcher
            .project_roles
            .pluck(:project_role)
            .uniq
            .compact
            .sort_by do |pr|
              ProjectRole::ROLE_SORT_VALUE[pr]
            end
            .reverse!
            .first
        end

        def path_to_researcher_emails
          final_q = (@params[:q] || {}).merge({ from_eq: @helpdesk_email.from })
          v2_sponsor_helpdesk_emails_path(scope: @params[:scope], q: final_q)
        end
      end
    end
  end
end
