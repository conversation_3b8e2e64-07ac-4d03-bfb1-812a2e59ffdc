class Citibank::CsvReport::RowParser
  attr_reader :row, :dkp, :multiple_users

  def initialize(row:, dkp:)
    @row = formatted_row(row)
    @dkp = dkp
  end

  def formatted_row(row)
    row = row.map{ |cell| cell.delete("\"") }
    row[1] = row[1].gsub(',', '')
    if row[2].include?(',')
      row[2] = row[2].gsub('.', '').gsub(',', '.')
    end
    row[3] = row[3].gsub('.', '').gsub(',', '.')

    row
  end

  def updated_row
    new_row = row.dup
    new_row[2] = amount

    if transfer
      new_row[1] = "PayClinical " + row[1]

      [
        transfer_direction,
        transfer.cro.present? ? transfer.cro.fakir_nota_CT : 'no cro',
        transfer.cro.present? ? transfer.cro.fakir_nota_CT : transfer.incoming? ? transfer.cro.fakir_nota_DT : dkp,
        cro_id,
        project_id,
        clinical_user_id,
        multiple_users ? 'dupl' : '0',
        new_row
      ].flatten
    elsif cros.present?
      ['1', cros.first.fakir_nota_DT, 'C', cros.first.id, '', '', '', new_row].flatten
    else
      incoming = row[2].to_d >= 0 ? 1 : 0
      [incoming.to_s, '149', 'P', '', '', '', '', new_row].flatten
    end
  end

  def transfer_direction
    transfer.incoming? ? 1 : 0
  end

  def transfer
    return unless clinical_users || cros

    @transfer || begin
      transfers = ClinicalTransfer.in_citi_files.where(amount: amount)

      if clinical_users.any?
        transfers = transfers.where(clinical_user_id: clinical_users)
        @multiple_users = clinical_users.size > 1
      end

      if cros && clinical_users.blank?
        transfers = transfers.where('title like ?', "#{ first_name }%")
      end

      transfers.order('id desc').first
    end
  end

  def amount
    row[2].to_d.abs
  end

  def clinical_users
    ClinicalUser.where('unaccent(first_name) like ?', "%#{first_name}%")
                .where('unaccent(last_name) like ?', "%#{last_name}%")
  end

  def cros
    ContractResearchOrganization.where('lower(name) like ?', "%#{ first_name.downcase }%")
  end

  def cro_id
    transfer.cro.try(:id)
  end

  def project_id
    transfer.try(:project_id)
  end

  def clinical_user_id
    transfer.try(:clinical_user_id)
  end

  def first_name
    title.split[0].strip
  end

  def last_name
    title.split[1].strip
  end

  def title
    row[1]
  end
end
