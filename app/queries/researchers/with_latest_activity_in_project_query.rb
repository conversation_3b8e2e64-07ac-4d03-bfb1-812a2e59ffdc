class Researchers::WithLatestActivityInProjectQuery < ApplicationQuery
  def initialize(project:, researchers: Researcher.all)
    @project = project
    @researchers = researchers
  end

  def run
    latest_activity_researcher || latest_logged_in_researcher
  end

  private

  def researchers
    return @researchers if @researchers.is_a?(ActiveRecord::Relation)

    Researcher.where(id: @researchers)
  end

  def latest_activity_researcher
    @project.clinical_user_account_activities.order_by_id.where(researcher_id: researchers).first&.researcher
  end

  def latest_logged_in_researcher
    researchers.order(last_sign_in_at: :desc).first
  end
end