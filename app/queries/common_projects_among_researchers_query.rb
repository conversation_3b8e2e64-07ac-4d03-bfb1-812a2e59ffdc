class CommonProjectsAmongResearchersQuery
  def initialize(params = {})
    @researcher = params[:researcher]
    @project_roles = Array params[:project_roles]
    @other_researcher = params[:other_researcher]
  end

  def call
    researcher_projects.where(id: @other_researcher.projects)
  end

  private

  def researcher_projects
    if @project_roles.any?
      @researcher.projects.joins(:project_roles).where(project_roles: { project_role: @project_roles }).distinct
    else
      @researcher.projects
    end
  end
end
