class GenerateProjectDebitsAndInvoices
  attr_reader :start_date, :end_date

  def initialize(params = {})
    @start_date = params.fetch(:start_date, 1.month.ago.beginning_of_month.beginning_of_day)
    @end_date = params.fetch(:end_date, 1.month.ago.end_of_month.end_of_day)
  end

  def call
    Project.not_test.with_fees_for_period(period).each do |p|
      Rails.logger.info { "Sending Invoice to project -> #{ p.id }" }
      ResearcherMailer.with(fee_report: fee_report(p)).send_pd_and_invoices.deliver
      Rails.logger.info { "Invoice sent" }
    end
  end

  def period
    start_date..end_date
  end

  def fee_report(p)
    ProjectFeeReport.new(start_date: start_date, end_date: end_date, project_id: p.id)
  end
end
