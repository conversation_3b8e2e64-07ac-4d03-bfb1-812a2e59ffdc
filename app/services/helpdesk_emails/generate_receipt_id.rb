class HelpdeskEmails::GenerateReceiptId < ApplicationService
  delegate_missing_to :@helpdesk_email

  def initialize(helpdesk_email:)
    @helpdesk_email = helpdesk_email
  end

  def call
    "#{ HelpdeskEmail::RECEIPT_ID_PREFIX }#{ year }-#{ email_id }-#{ date_time }"
  end

  private

  def date
    message_date
  end

  def year
    date.strftime('%y')
  end

  def email_id
    format('%05i', id)
  end

  def date_time
    date.strftime('%m%d%H%M')
  end
end