class HelpdeskEmails::GetTag < ApplicationService
  delegate_missing_to :@helpdesk_email

  def initialize(helpdesk_email:)
    @helpdesk_email = helpdesk_email
  end

  def call
    if after_check_in?
      return :plan if !free_plan?
      return :vip if tag == 'vip'
      return :plan if fee_plan
      set_before_check_in_tag
    else
      set_before_check_in_tag
    end
  end

  private

  def set_before_check_in_tag
    return :vip if sender_registered? && (from_manager? || from_vip_cro? || paid_sla_project?)
    return :safe if sender_registered?
    return :risk
  end

  def after_check_in?
    checked_in_at || !new?
  end

  def sender_registered?
    @helpdesk_email
      .researcher
      .present?
  end

  def from_manager?
    @helpdesk_email
      .researcher
      .is_manager?
  end

  def from_vip_cro?
    @helpdesk_email
      .researcher
      .contract_research_organizations
      .where(importance: 'vip')
      .exists?
  end

  def paid_sla_project?
    @helpdesk_email
      .researcher
      .projects
      .with_premium_plans
      .exists?
  end
end