class CurrencySpreads::FindForNote < ApplicationService
  def initialize(note:)
    @note = note
  end

  def call
    project_spread || cro_spread || global_spread
  end

  private

  def project_spread
    CurrencySpread.where(
      resource: @note.project,
      currency: target_currency,
      base_currency: base_currency
    )
    .order_by_id
    .first
    &.note
  end

  def cro_spread
    CurrencySpread.where(
      resource: @note.contract_research_organization,
      currency: target_currency,
      base_currency: base_currency
    )
    .order_by_id
    .first
    &.note
  end

  def global_spread
    CurrencySpread.default_for_note
  end

  def base_currency
    @note.project_currency == 'PLN' ? @note.note_currency : @note.project_currency
  end

  def target_currency
    @note.project_currency == 'PLN' ? @note.project_currency : @note.note_currency
  end
end