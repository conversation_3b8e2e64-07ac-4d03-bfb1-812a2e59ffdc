class ActionItems::Escalations::First::GetCc < ApplicationService
  delegate_missing_to :@action_item

  def initialize(action_item:)
    @action_item = action_item
  end

  def call
    if owner == 'Investigator'
      ActionItems::Targets::Cras::GetCc.call(action_item: @action_item)
    elsif owner == 'CRA'
      ActionItems::Targets::Managers::GetCc.call(action_item: @action_item)
    elsif owner == 'Manager'
      cro.super_managers[1..-1]
    elsif owner == 'Operator'
      ActionItems::Targets::Operators::GetCc.call(action_item: @action_item)
    end
  end
end