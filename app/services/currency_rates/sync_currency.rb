class CurrencyRates::SyncCurrency < ApplicationService
  def initialize(date:, currency:)
    @date = date
    @currency = currency
  end

  def call
    r = HTTParty.get("https://api.nbp.pl/api/exchangerates/rates/a/#{@currency}/#{formatted_date}")

    if r.not_found?
      r = HTTParty.get("https://api.nbp.pl/api/exchangerates/rates/a/#{@currency}/#{formatted_date_week_ago}/#{formatted_date}")
    end

    rate = r['rates'].last

    CurrencyRate.create!(
      base_currency: @currency,
      target_currency: 'PLN',
      rate_source: rate['no'],
      rate: rate['mid'],
      comments: rate['no'],
      valid_from: @date,
      valid_to: @date
    )
  end

  private

  def formatted_date_week_ago
    (@date - 7.days).strftime('%Y-%m-%d')
  end

  def formatted_date
    @date.strftime('%Y-%m-%d')
  end
end