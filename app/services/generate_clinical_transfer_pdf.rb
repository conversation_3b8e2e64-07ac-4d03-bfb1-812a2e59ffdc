class GenerateClinicalTransferPdf
  attr_reader :clinical_transfer, :locale, :current_researcher, :print_two_pages, :temp

  def initialize(clinical_transfer:, locale:, current_researcher: nil, print_two_pages: false, temp: false)
    @clinical_transfer = clinical_transfer
    @locale = locale
    @current_researcher = current_researcher
    @print_two_pages = print_two_pages
    @temp = temp
  end

  def call
    file_name = filename
    obj = self

    Prawn::Document.generate(file_name, page_size: 'A4', bottom_margin: 21) do
      font_families.update("Source_Sans_Pro" => {
        :normal => "#{Rails.root}/lib/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf",
        :bold => "#{Rails.root}/lib/fonts/Source_Sans_Pro/SourceSansPro-SemiBold.ttf",
      })

      font "Source_Sans_Pro", size: 11

      if obj.array_of_transfers_given?
        obj.clinical_transfer.sort_by { |t| [t.clinical_user.try(:patient_code) || '0', t.visit_date || Time.current] }.each_with_index do |transfer, index|
          ClinicalTransferConfirmationSectionPdf.new(self, clinical_transfer: transfer, locale: obj.locale).render
          start_new_page if index < obj.clinical_transfer.size - 1
       end
      else
        if obj.print_two_pages
          ClinicalTransferConfirmationSectionPdf.new(self, clinical_transfer: obj.clinical_transfer, locale: 'en').render
          start_new_page
          ClinicalTransferConfirmationSectionPdf.new(self, clinical_transfer: obj.clinical_transfer, locale: 'pl').render
        else
          ClinicalTransferConfirmationSectionPdf.new(self, clinical_transfer: obj.clinical_transfer, locale: obj.locale).render
        end
      end
    end

    file_name.gsub!(/public\//,'')
    file_name
  end

  def filename
    prefix = locale == 'pl' ? 'Potwierdzenie_przelewu' : 'Subject_Reimbursement'
    if array_of_transfers_given?
      file = "#{ prefix }_#{clinical_transfer.first.clinical_protocol_code}_#{clinical_transfer.first.clinical_center_code}_#{ I18n.l Date.today, format: :local_only_date_dash }_#{locale}"
    else
      file = "#{ prefix }_#{clinical_transfer.clinical_protocol_code}_#{clinical_transfer.clinical_center_code}_#{ I18n.l Date.today, format: :local_only_date_dash }_#{locale}"
    end

    if temp
      return "tmp/#{file}.pdf"
    else
      return "public/system/transfer_confirmations/#{file}.pdf"
    end
  end

  def array_of_transfers_given?
    clinical_transfer.is_a?(ActiveRecord::Relation) || clinical_transfer.is_a?(Array)
  end
end
