class ProjectDebitSummaries::Pay < ApplicationService
  attr_reader :amount

  def initialize(note:, source_currency_account_id:, extra_transfer_params: {}, amount:)
    @note = note
    @source_currency_account_id = source_currency_account_id
    @currency_account = CurrencyAccount.find(@source_currency_account_id)
    @extra_transfer_params = extra_transfer_params
    @result = {}
    @amount = amount
  end

  def call
    ActiveRecord::Base.transaction(joinable: false, requires_new: true) do
      update_project_balance if project
      create_internal_transfer_to_note
      create_internal_transfer_from_note
      create_internal_transfer_to_project unless credited_note? || @note.is_a?(ProjectDebitSummary::ManualCro)
      create_bank_transfer if credited_note?
      process_note
      process_signed_debits
    end

    @result[:amount_used] = used_amount
    @result
  end
  private

  def money_used
    @money_used ||= Money.new(
      amount: ProjectDebitSummaries::GetBalanceUpdateAmount.call(
        note: @note,
        amount: @amount,
        currency_account: @currency_account
      ),
      currency: @currency_account.currency
    )
  end

  def used_amount
    @used_amount ||= money_used.amount
  end

  def used_amount_pln
    @used_amount_pln ||= money_used.amount_in(
      use_currency: 'PLN',
      rate: @note.currency_exchange&.currency_rate_used
    )
  end

  def used_amount_note_currency
    money_used.amount_in(
      use_currency: @note.cro_currency,
      rate: @note.currency_exchange&.currency_rate_used
    )
  end

  def create_bank_transfer
    amount = credited_note_amount_money.amount_in(
      use_currency: cro_credit_acc.currency,
      rate: note_exchange_rate
    )

    @result[:bank_transfer] = ClinicalTransfer.create!(
      amount: amount,
      status_change_date: Time.new,
      state: ClinicalTransfer::BOOKED,
      project_debit_summary_id: @note.id,
      flow_direction: ClinicalTransfer::INCOMING,
      account_number: cro_credit_acc.account_number,
      title: credit_transfer_title,
      source_acc_nr: @currency_account.account_number,
      source_name: 'Let Me Pay',
      name: 'Let Me Pay'
    )
  end

  def credit_transfer_title
    "Repaid credit for debit note# #{@note.note_number}"
  end

  def credited_note?
    @credited_note ||= @note.credit?
  end

  def project
    @project ||= @note.project
  end

  def update_project_balance
    unless project.debit_allowed
      project.update! saldo: project.saldo + used_amount_pln
    end
  end

  def amount_for_note
    @amount_for_note ||= if credited_note?
      @note.amount_left_to_pay
    else
      used_amount
    end
  end

  def amount_for_note_pln
    @amount_for_note_pln ||= if credited_note?
      @note.currency_exchange&.total || @note.original_amount
    else
      used_amount
    end
  end

  def create_internal_transfer_to_note
    transfer_params = {
      amount: amount_for_note,
      destination: @note.currency_account,
      source_currency_account_id: @source_currency_account_id,
      force_currency_rate: note_exchange_rate
    }

    transfer_params.merge!(@extra_transfer_params)
    InternalTransfer.create!(transfer_params)
  end

  def create_internal_transfer_from_note
    if credited_note?
      amount = credited_note_amount_money.amount_in(
        use_currency: @note.currency_account.currency,
        rate: note_exchange_rate
      )

      return unless amount > 0

      params = {
        amount: amount,
        destination: cro_credit_acc,
        source: @note.currency_account,
        for_resource: @note,
        title: credit_transfer_title
      }

      if source_and_destination_match_currency_exchange?(params)
        params[:destination_amount] = params[:amount] / note_exchange_rate
      end

      InternalTransfer.create!(params)
    end
  end

  def source_and_destination_match_currency_exchange?(params)
    exchange = @note.currency_exchange

    return false unless exchange.present?

    params[:source].currency == exchange.target_currency && params[:destination].currency == exchange.base_currency
  end

  def cro_credit_acc
    @note.cro.credit_currency_accounts.where(currency: @currency_account.currency).last
  end

  def note_exchange_rate
    @note_exchange_rate ||= @note.currency_exchange&.currency_rate_used
  end

  def credited_note_amount_money
    @credited_note_amount_money ||= begin
      transfers = InternalTransfer.where(
        destination: @note.currency_account,
        source: CurrencyAccount::ForCroCredit.where(resource: @note.cro)
      )
      amount = transfers.sum(:amount)
      currency = transfers.last&.source&.currency

      Money.new(
        amount: amount,
        currency: currency || 'PLN'
      )
    end
  end

  def create_internal_transfer_to_project
    InternalTransfer.create!(
      amount: project_transfer_amount.round(2),
      destination: @note.project.currency_account,
      source: @note.currency_account,
      for_resource: @note,
      title: credit_transfer_title,
      force_currency_rate: note_exchange_rate
    )
  end

  def project_transfer_amount
    if !credited_note?
      money_used.amount_in(
        use_currency: @note.currency_account.currency,
        rate: note_exchange_rate
      )
    else
      credited_note_amount_money.amount_in(
        use_currency: @note.currency_account.currency,
        rate: note_exchange_rate
      )
    end
  end

  def process_note
    @note.saldo += used_amount_note_currency

    if @note.saldo >= 0
      @note.state = ProjectDebitSummary::PAID
      @note.state_changed_at = Time.new
      @note.credit_paid_at = Time.new if credited_note?
    end

    @note.save!
  end

  def process_signed_debits
    ProjectDebitSummaries::ProcessSignedDebits.call(
      note: @note,
      amount: money_used.amount_in(
        use_currency: (@note.project_debits.first&.currency || 'PLN'),
        rate: note_exchange_rate
      )
    )
  end
end
