class ProjectDebitSummaries::Manual::Create < ApplicationService
  include ProjectDebitSummaries::BaseCrud

  def initialize(project:, amount:, currency:, date:, currency_account_id: nil)
    @project = project
    @amount = amount
    @currency = currency
    @date = date
    @currency_account_id = currency_account_id
  end

  def call
    ProjectDebitSummary.transaction do
      create_note
      create_currency_exchange if needs_currency_conversion?
      @note.update! manual_amount: balance, manual_note_currency_account_id: @currency_account_id
      @note.generate_prepaid_note_pdf
    end

    @note
  end

  private

  def needs_currency_conversion?
    @currency != @project.currency
  end

  def debits_currency
    @currency
  end

  def note_currency
    @project.currency
  end

  def debits_balance
    @amount
  end
end