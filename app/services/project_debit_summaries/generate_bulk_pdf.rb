class ProjectDebitSummaries::GenerateBulkPdf < ApplicationService
  include ProjectDebitSummaries::PdfPo

  def initialize(project_debit_summary:, project_debits: [], locale: 'pl', generate_in_foreign_currency: false)
    @project_debit_summary = project_debit_summary
    @project_debits = project_debits
    @locale = locale
    @generate_in_foreign_currency = generate_in_foreign_currency
  end

  def call
    pds = @project_debit_summary
    locale = @locale
    if @project_debits.blank?
      return pds.file_path if pds.file_path
      return
    end

    project = @project_debits.first.project
    I18n.locale = locale

    first_date = @project_debits.map(&:start_date).min
    last_date = @project_debits.map(&:completion_date).max

    sum = 0
    project_debits = @project_debits.sort_by { |pd| pd.clinical_center.try(:clinical_center_code) || 0 }
    pd_tab = project_debits.map { |pd|
      [ pd.clinical_protocol_code,
        approver_or_po_for_debit(pd),
        pd.full_note_number,
        "%.2f" % pd.amount_for_summary,
        "%.2f" % sum += pd.amount_for_summary,
        '?'
      ]
    }

    pd_tab.insert(
      0,
      [
        'Protocol#',
        aprover_or_po_label,
        I18n.t('project_debit_summary_pdf.pds_nr'),
        I18n.t('project_debit_summary_pdf.amount'),
        I18n.t('project_debit_summary_pdf.balance'),
        'Załącznik#'
      ]
    )

    logopath = "#{Rails.root}/app/assets/images/lmp_logo_pdf.png"
    file_name = "public/system/debit_pdfs/#{pds.note_number.gsub(/ /,'_')[3..-1]}_#{ locale }.pdf"

    ProjectDebitSummary.transaction do
      ProjectDebitSummaries::BulkPdf.new(
        project_debit_summary: pds,
        pd_tab: pd_tab,
        project_debits: project_debits,
        total_amount: sum,
        generate_in_foreign_currency: @generate_in_foreign_currency
      ).render_file(file_name)

      project_debits.each do |pd|
        pd.project_debit_summary_id = pds.id
        pd.save!
      end

      file_name.gsub!(/public\//,'')
      pds.send("#{ locale }_file_path=", file_name)

      if locale == (project_debits.first.try(:researcher).try(:locale) || 'pl')
        pds.file_path = file_name
      end
      pds.save!
      file_name
    end
  end
end