class ProjectDebitSummaries::GeneratePdf
  def initialize(project_debit_summary:, project_debits: [], locale: 'pl', generate_in_foreign_currency: false)
    @project_debit_summary = project_debit_summary
    @project_debits = project_debits
    @locale = locale
    @generate_in_foreign_currency = generate_in_foreign_currency
  end

  def call
    params = {
      project_debit_summary: @project_debit_summary,
      project_debits: @project_debits,
      locale: @locale,
      generate_in_foreign_currency: @generate_in_foreign_currency
    }

    if @project_debit_summary.bulk_notes
      ProjectDebitSummaries::GenerateBulkPdf.new(**params).call
    else
      ProjectDebitSummaries::GenerateStandardPdf.new(**params).call
    end
  end
end