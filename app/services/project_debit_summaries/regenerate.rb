class ProjectDebitSummaries::Regenerate < ApplicationService
  include ProjectDebitSummaries::BaseCrud

  def initialize(project_debit_summary:)
    @note = project_debit_summary
    @project = @note.project
    @project_debits = @note.project_debits
  end

  def call
    raise 'Cannot regenerate cancelled note!' if @note.cancelled?

    ProjectDebitSummary.transaction do
      create_currency_exchange if needs_currency_conversion?
      create_pdf
      create_xls
    end
  end

  private

  def currency_rate_date
    @note.created_at
  end
end