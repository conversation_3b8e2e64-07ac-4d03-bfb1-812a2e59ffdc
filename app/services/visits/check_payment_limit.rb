class Visits::CheckPaymentLimit < ApplicationService
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>
  delegate_missing_to :@visit

  def initialize(visit:)
    @visit = visit
  end

  def call
    if subject_limit && over_limit?
      error_message
    end
  end

  private

  def subject_limit
    @subject_limit ||= clinical_user.limit_per_patient_for_all_payments
  end

  def over_limit?
    allowed_amount <= 0
  end

  def allowed_amount
    @allowed_amount ||= clinical_user.amount_left_for_all_payments
  end

  def error_message
    "This patient has a limit of #{ number_to_currency subject_limit, unit: currency } for all payments. Your request exceeds the limit. The left amount is #{ number_to_currency allowed_amount, unit: currency}. Please contact the Sponsor if you need to increase it."
  end
end