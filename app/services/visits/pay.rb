module Visits
  class Pay < ApplicationService
    attr_reader :visit, :researcher, :project, :params

    delegate_missing_to :visit

    def initialize(visit:, researcher:, project:, params:)
      @visit = visit
      @researcher = researcher
      @project = project
      @params = params
    end

    def call
      return 'The amount due must be greater than 0.00.' unless amount > 0
      
      Visit.transaction do
        v_params = params.slice(:amount, :description, :prescription, :state, :visit_date,
                                       :visit_payment_categorizations_attributes)
        v_params.merge!(
          high_priority: false,
          state: Visit::SUCCESS,
          transfer_ordered_by_researcher_id: researcher.id
        )

        r_role = researcher.current_role(project)

        priority_transfer = ['on', 'true', true, '1'].include? params[:priority]
        determined_state = ClinicalTransfer.determine_transfer_state(project, amount, r_role, visit, false, researcher)

        if [ClinicalTransfer::PROCESSING, ClinicalTransfer::DEBIT_WAITING].include?(determined_state)
          v_params[:transfer_approved_by_researcher_id] = researcher.id
        end

        update!(v_params)

        errors = Visits::CheckPaymentLimit.call(visit: visit)

        clinical_center = clinical_user.clinical_center
        transfer_to = params[:transfer_to]

        if transfer_to && (transfer_to == 'clinical_center')
          transfer_to_account_number = clinical_center.account_number
          transfer_to_name = clinical_center.transfer_receiver
        else
          transfer_to_account_number = clinical_user.account_number
          transfer_to_name = clinical_user.transfer_to_name
        end

        transfer_title = ClinicalTransfer.transfer_title(visit: visit, transfer_to: transfer_to)

        transfer_params = {
          clinical_user_id: clinical_user.id,
          researcher_id: researcher.id,
          amount: amount,
          flow_direction: ClinicalTransfer::OUTGOING,
          state: determined_state,
          status_change_date: Time.new,
          account_number: transfer_to_account_number,
          name: transfer_to_name,
          title: transfer_title,
          comment: 'Przelew jako opłata za wizytę',
          visit_id: id,
          project_id: clinical_user.project.id,
          clinical_center_id: clinical_center.id,
          transfered_to: transfer_to,
          priority: priority_transfer,
          auto_accepted_within_amount_and_km_range: ClinicalTransfer.eligible_for_auto_accept_within_amount_and_km_range?(role: r_role, visit: visit, amount: amount)
        }

        object_to_validate_bank_acc_nr = if transfer_to == 'clinical_center'
                                           clinical_center
                                         else
                                           clinical_user
                                         end
        transfer_accepted = [ClinicalTransfer::PROCESSING, ClinicalTransfer::DEBIT_WAITING].include?(determined_state)

        # Poniżej logika dla debetów, tylko gdy przelew wchodzi do zestawienia
        # przelew wchodzi do zestawienia, gdy jest zaaprobowany
        if project.debit_allowed && transfer_accepted
          pd = project.get_current_project_debit(researcher: researcher, clinical_center: visit.clinical_center)
          pd.saldo = pd.saldo - amount
          transfer_params[:project_debit_id] = pd.id
          pd.save!
        end

        # jesli przelew jest priority to zamykamy zestawienie dzis o polnocy
        if project.debit_allowed && priority_transfer
          pd = project.get_current_project_debit(researcher: researcher, clinical_center: visit.clinical_center)
          pd.completion_date = Date.today.end_of_day
          pd.save!
        end

        # jesli jest juz zaaprobowany przelew
        if transfer_accepted
          CurrencyAccounts::TopUpMultiCurrencyProject.call(
            currency_account: project.currency_account,
            target_amount: amount,
            cro: project.cro,
            clinical_protocol_code: project.clinical_protocol_code
          ) if project.currency_account.balance < amount

          project.saldo = project.saldo - amount
          project.save!

          InternalTransfer.create!(
            source: project.currency_account,
            destination: visit.clinical_user.currency_account,
            amount: amount,
            for_resource: visit
          )
          transfer_params[:project_saldo] = project.saldo
        end

        @transfers = []

        if FeatureFlipper.transfer_per_visit_cost?(project)
          visit.visit_payment_categorizations.each do |vpc|
            vpc_params = transfer_params.merge(
              amount: vpc.amount,
              visit_payment_categorization: vpc,
            )
            vpc_params[:account_number] = vpc.supplier.account_number if vpc.supplier
            vpc_params[:transfered_to] = :supplier if vpc.supplier
            transfer = ClinicalTransfer.new(vpc_params)
            transfer.validate_bank_account_sec_code!(object_to_validate_bank_acc_nr) unless vpc.supplier
            transfer.save!
            @transfers << transfer
          end
        else
          transfer = ClinicalTransfer.new(transfer_params)
          transfer.validate_bank_account_sec_code!(object_to_validate_bank_acc_nr)
          transfer.save!
          @transfers << transfer
        end


        if @transfers.any? && @transfers.any?(&:waiting?) && (r_role == 'Manager')
          AdminMailer.with(visit: visit, researcher: researcher, clinical_transfers: @transfers).manager_ordered_waiting_transfer.deliver
        end

        errors
      end
    end
  end
end