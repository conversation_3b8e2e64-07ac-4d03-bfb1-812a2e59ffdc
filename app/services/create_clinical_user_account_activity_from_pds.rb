class CreateClinicalUserAccountActivityFromPds
  attr_reader :pds

  def initialize(params = {})
    @pds = ProjectDebitSummary.find(params[:project_debit_summary_id])
  end

  def call
    ActiveRecord::Base.transaction do
      pds.related_visits.each do |visit|
        ClinicalUserAccountActivity.create(clinical_user_id: visit.clinical_user_id, source_id: visit.id,
          source_type: 'Visit', activity_type: ClinicalUserAccountActivity::PDS_CREATED)
      end
    end
  end
end
