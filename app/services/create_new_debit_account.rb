class CreateNewDebitAccount
  attr_reader :project, :project_debit

  def initialize(params = {})
    @project = params[:project]
    @researcher = params[:researcher]
    @clinical_center = params[:clinical_center]
  end

  def call
    @project_debit = ProjectDebit.new(start_date: start_date, completion_date: completion_date,
      account_number: account_num, project_id: project.id, saldo: 0, researcher_id: @researcher.id,
      status: ProjectDebit::STARTED, status_change_date: current_time, clinical_center_id: @clinical_center.id)
    @project_debit.save!
  end

  def current_time
    Time.now
  end

  private

  def account_num
    ProjectDebit.generate_next_account_num(project.id)
  end

  def start_date
    current_time
  end

  def completion_date
    return nil if project_has_manual_pd_closing?
    case project.contract_research_organization.close_pd_freq
    when '0'
      return Time.current.end_of_day if current_time.thursday?
      Chronic.parse('next thursday').end_of_day
    when '1'
      completion_date_for_1
    when '2'
      completion_date_for_2
    end
  end

  def completion_date_for_1
    if current_time < first_thursday_this_month
      first_thursday_this_month
    elsif current_time > first_thursday_this_month and current_time < third_thursday_this_month
      third_thursday_this_month
    else
      first_thursday_next_month
    end
  end

  def completion_date_for_2
    if current_time < first_thursday_of_this_month
      first_thursday_of_this_month
    else
      first_thursday_next_month
    end
  end

  def first_thursday_of_this_month
    Chronic.parse("1st thursday this #{ current_month }").end_of_day
  end

  def project_has_manual_pd_closing?
    project.contract_research_organization.manual_pd_closing?
  end

  def first_thursday_this_month
    Chronic.parse("first thursday this #{ current_month }").end_of_day
  end

  def third_thursday_this_month
    Chronic.parse("third thursday this #{ current_month }").end_of_day
  end

  def first_thursday_next_month
    Chronic.parse("first thursday next month").end_of_day
  end

  def now_is_before_end_of_first_thursday_of_the_month
    current_time < first_thursday_of_this_month
  end

  def third_friday_of_last_month
    Chronic.parse('3rd friday last month').beginning_of_day
  end

  def now_is_before_end_of_thursday
    current_time < (current_time.beginning_of_week + 3.days).end_of_day
  end

  def now_is_after_last_fridays_beginning_of_day
    current_time > (current_time.beginning_of_week - 3.days)
  end

  def last_friday_beginning_of_day
    Chronic.parse('last friday').beginning_of_day
  end

  def this_friday_beginning_of_day
    Chronic.parse('this week friday').beginning_of_day
  end

  def current_month
    current_time.strftime("%B")
  end

  def after_third_friday_of_last_month
    current_time > third_friday_of_last_month
  end

  def first_friday_of_this_month
    Chronic.parse("1st friday this #{ current_month }").beginning_of_day
  end

  def first_friday_of_last_month
    Chronic.parse('first friday of last month').beginning_of_day
  end
end
