// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

.select2-result-label {
  border-bottom: 1px solid #eee;
  padding-top: 0;
  padding-bottom: 0;
}

.select2-results .select2-highlighted{
  color: #000;
}

.tab-content {
  padding-top: 20px;
}

.ui-timepicker-select {
  width: 100px;
  margin-left: 10px;
  position: relative;
  bottom: 4px;
}

.ui_tpicker_time_label, .ui_tpicker_time {
  display: none;
}

.ui_tpicker_hour_label, .ui_tpicker_minute_label {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
}

.ui-timepicker-div dl dd.ui_tpicker_hour, .ui-timepicker-div dl dd.ui_tpicker_minute {
  margin-bottom: 0;
}

.details {
  display: none;
}

.details, .overview {
  cursor: pointer;
}

#clinical-centers-table {
  background: #fff;
}

.event-item {
  height: 50px;
  padding: 2px 30px 2px 10px;
  cursor: pointer;

}

.events-list {
  max-height: 290px;
  overflow-y: auto;

  margin: 0;
  padding: 0;
  border: 1px solid #eeeeee;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  background: white;
}

.events-list > li:first-child {
  border-top: 1px solid #DDD;
}

.events-list > li {
  padding-top: 2px;
  padding-bottom: 2px;
  position: relative;
  border-bottom: 1px solid #DDD;
}

.events-list > li:hover {
  background: whiteSmoke;
}

.event-title, .fb-user-details {
  display: block;
  padding-bottom: 2px;

}

.ui-timepicker-div .ui-widget-header {
  margin-bottom: 8px;
}

.ui-timepicker-div dl {
  text-align: left;
}

.ui-timepicker-div dl dt {
  height: 25px;
  margin-bottom: -25px;
}

.ui-timepicker-div dl dd {
  margin: 0 10px 10px 65px;
}

.ui-timepicker-div td {
  font-size: 90%;
}

.ui-tpicker-grid-label {
  background: none;
  border: none;
  margin: 0;
  padding: 0;
}

.ui-timepicker-rtl {
  direction: rtl;
}

.ui-timepicker-rtl dl {
  text-align: right;
}

.ui-timepicker-rtl dl dd {
  margin: 0 65px 10px 10px;
}

.datetime_picker_alt {
  width: 75px;
}

.datetime_picker_wrap .date {
  width: 120px;
  margin-bottom: 0;
}

.datetime_picker_wrap {
  display: inline-block;
}

.datetime_picker_wrap .btn{
  margin-left: 5px;
}

.datetime_picker_wrap input {
  margin-bottom: 0;
  width: 75px;

}

p.emptyView {
  text-align: center;
  margin: 5px;
}

.ui-datepicker-calendar tr td {
  width: 44px;
}

.ui_tpicker_hour_label,
.ui_tpicker_minute_label,
.ui_tpicker_minute,
.ui_tpicker_hour {
  display: inline-block;
  width: 30px;
  margin: 0;
}

.ui_tpicker_minute_label {
  display: none
}

.ui_tpicker_minute  .ui-timepicker-select,
.ui_tpicker_hour  .ui-timepicker-select {
  width: 60px;
  margin-bottom: 0;
}

.ui-timepicker-div .ui_tpicker_minute,
.ui-timepicker-div .ui_tpicker_hour {
  margin-left: 35px;
}

.ui-timepicker-div .ui-timepicker-select.prompt {
  border: 1px solid #0044CC;
}

.ui-datepicker .ui-datepicker-close {
  background: #5BB65B;
  color: #fff;
  font-weight: normal;
}

.btn.padding-25 {
  padding-left: 25px;
  padding-right: 25px;
}

#todoapp.filter-active #todo-list .completed {
  display: none
}

#todoapp.filter-completed #todo-list .active {
  display: none
}

#summaryapp {
  input, span.add-on, .btn {
    border-radius: 0;
  }
}




