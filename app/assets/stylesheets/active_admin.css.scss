@import "active_admin/mixins";
@import "active_admin/base";
@import "active_admin_sidebar";
@import "select2";
@import "jquery-ui-timepicker-addon";

.aa_btn {
  -webkit-border-radius: 200px;
  -moz-border-radius: 200px;
  border-radius: 200px;
  display: inline-block;
  font-weight: bold;
  font-size: 1.0em;
  font-family: Helvetica, Arial, sans-serif;
  line-height: 12px;
  margin-right: 3px;
  padding: 7px 16px 6px;
  text-decoration: none;
  -webkit-font-smoothing: antialiased;
  background: #838a90;
  background: -webkit-linear-gradient(-90deg, #838a90, #414549);
  background: -moz-linear-gradient(-90deg, #838a90, #414549);
  background: linear, -90deg, #838a90, #414549;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FF838A90', endColorstr='#FF414549');
  -ms-filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FF838A90', endColorstr='#FF414549');
  text-shadow: #000 0 1px 0;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1), 0 1px 0 0px rgba(255, 255, 255, 0.2) inset;
  border: solid 1px #484e53;
  border-color: #484e53;
  border-top-color: #616a71;
  border-right-color: #484e53;
  border-bottom-color: #363b3f;
  border-left-color: #484e53;
  color: #efefef;
  cursor: pointer;
  color: white !important;
  text-decoration: none !important;
}

#header {
  max-width: 100vw;
}

#title_bar {
  max-width: 100vw;
}

table#index_table_clinical_mobile_messages {
  th.msg, td.msg {
    width: 660px;
  }
}

table#index_table_project_debit_signatures {
  td.numer_noty {
    width: 150px;
  }
}
li.has_nested a {
  z-index: 1 !important;
}

.md-form {
  label {
    float: left;
    margin-right: 10px;
  }
}

.admin_user_action_requirements {
  .action_items {
    .action_item:first-of-type {
      display: none;
    }
  }
}

#index_table_user_action_requirements{
  td.powód, th.powód {
    width: 300px !important;
  }
}

.admin_mail_preview {
  width: 600px;
}

#index_table_helpdesk_emails {
  th.subject, td.subject {
    a {
      width: 400px !important;
    }
    width: 400px !important;
  }

  th.body, td.body {
    a {
      width: 400px !important;
    }

    width: 400px !important;
  }

  td.from {
    color: red;

    .from_researcher_present {
      a {
        color: #0eb70e;
      }
      color: #0eb70e;
    }
  }
}

body.active_admin {
  .sidebar_section {
    .filter_numeric_range {
      input {
        width: 88px;
      }
      .seperator {
        display: inline-block;
        text-align: center;
        width: 12px;
      }
    }
  }
}

.formtastic.project_role .has_many_container.clinical_center_roles {
  .select2-container {
    width: auto !important;
  }
}

.col-laczna_kwota_bankowa, .col-laczna_kwota_przekazow {
  text-align: right;
}

#index_table_project_debit_summaries {
  .col-saldo {
    white-space: nowrap;
    overflow: hidden;
  }
}

li#dashboard {
  a {
    background: #6495ed !important;
    font-size: 15px;
  }
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.admin_researchers .has_many_container.clinical_center_roles {
  .select2-container {
    width: 75% !important;
  }
}