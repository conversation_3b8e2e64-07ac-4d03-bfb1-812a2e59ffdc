@import "bootstrap4";
@import "shared/variables/*";
@import "font-awesome";
@import url('https://fonts.googleapis.com/css?family=News+Cycle:400&subset=latin-ext');
@import 'mdb';

@import "application/styles/*";
@import "shared/styles/*";
@import "shared/styles/pagination";
@import "shared/styles/responsive";
@import "shared/browser_compatibility";
@import "shared/internet_explorer";
@import "summernote";
@import "jasny_bootstrap_fileinput";
@import "footer.css.scss";
@import 'protip';

html {
  overflow: visible !important;
}

ul#slide-out {
  width: 290px;
  .link_row_one {
    width: 290px;
  }
}

.form-group.boolean.optional.clinical_user_perm_notifications {
  margin-bottom: -20px;
}

.picker__table td {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

.picker__day--infocus {
  padding: .35rem 0;
}

button.picker__button--today, .picker__button--clear, .picker__button--close {
  font-weight: 100;
}
