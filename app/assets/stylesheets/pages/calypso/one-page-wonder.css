/*!
 * Start Bootstrap - One Page Wonder HTML Template (http://startbootstrap.com)
 * Code licensed under the Apache License v2.0.
 * For details, see http://www.apache.org/licenses/LICENSE-2.0.
 */

body {
    margin-top: 50px; /* Required padding for .navbar-fixed-top. Remove if using .navbar-static-top. Change if height of navigation changes. */
}

.header-image {
	display: block;
	width: 100%;
	text-align: left;
	background-repeat: no-repeat;
	background-position: center center scroll;
	background-image: url("/calypso/mainimage.png");
	background-attachment: scroll;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	background-size: cover;
	-o-background-size: cover;
}

.headline {
	padding-top: 75px;
	padding-right: 0;
	padding-left: 0;
	padding-bottom: 75px;
}

.headline h1 {
	font-size: 62px;
	font-style: inherit;
	color: #fff;
	font-weight: 700;
	line-height: normal;
}

.headline h2 {
	font-size: 26px;
	color: #8cc640;
	font-style: italic;
	font-weight: 400;
	line-height: normal;
}

.featurette-divider {
    margin: 80px 0;
}

.featurette {
}

.featurette-image.pull-left {
    margin-right: 40px;
}

.featurette-image.pull-right {
    margin-left: 40px;
}

.featurette-heading {
	font-size: 43px;
	color: #488ac8;
	text-align: left;
	font-weight: bold;
}
.featurette-headingtwo {
	font-size: 55px;
	color: #014971;
	text-align: left;
	font-weight: bold;
	line-height: normal;
	margin-bottom: 0px;
}
.featurette-sub {
	font-size: 24px;
	color: #003d7d;
	text-align: left;
	font-weight: bold;
}

footer {
    margin: 50px 0;
}

@media(max-width:1200px) {
    .headline h1 {
	font-size: 64px;
    }

    .headline h2 {
        font-size: 26px;
    }

    .featurette-divider {
        margin: 50px 0;
    }

    .featurette-image.pull-left {
        margin-right: 20px;
    }

    .featurette-image.pull-right {
        margin-left: 20px;
    }

    .featurette-heading {
        font-size: 35px;
    }

    .featurette-sub {
        font-size: 18px;
    }


}

@media(max-width:991px) {
    .headline h1 {
	font-size: 34px;
    }

    .headline h2 {
	font-size: 26px;
	line-height: normal;
    }

    .featurette-divider {
        margin: 40px 0;
    }

    .featurette-image {
        max-width: 50%;
    }

    .featurette-image.pull-left {
        margin-right: 10px;
    }

    .featurette-image.pull-right {
        margin-left: 10px;
    }

    .featurette-heading {
        font-size: 30px;
    }
}

@media(max-width:768px) {
    .container {
        margin: 0 15px;
    }

    .featurette-divider {
        margin: 40px 0;
    }

    .featurette-heading {
        font-size: 28px;
    }
	.featurette-sub {
        font-size: 16px;
    }
	.featurette-headingtwo {
        font-size: 28px;
}
.buttonleft {
	float: left;
	text-align: right;
	padding-top: 20px;
}
   }
	 .featurette-image {
        max-width: 100%;
    }
