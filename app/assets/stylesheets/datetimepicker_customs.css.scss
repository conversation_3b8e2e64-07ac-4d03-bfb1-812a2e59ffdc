.bootstrap-datetimepicker-widget {
  .datepicker {
    p {
      display: none !important;
    }
  }
}

.picker-switch{
  a:hover{
    text-decoration: none;
  }

  a{
    font-weight: bold;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }

  .icon-time:before{
   content: attr(data-text-time);
   left: -55px;

  }
  .icon-calendar:before{
    content: attr(data-text-calendar);
    left: -65px;

  }
  .icon-time:before, .icon-calendar:before{
    display: inline-block;
    position: relative;
    font-style: normal;
    color: #000;
  }
  .icon-time{
    position: relative;
    right: -30px;
    background-position: -48px -24px !important;

  }
  .icon-calendar{
    position: relative;
    right: -35px;
    background-position: -192px -120px !important;

  }


  [class^="icon-"], [class*=" icon-"] {
    display: inline-block;
    width: 14px;
    height: 14px;
    line-height: 14px;
    vertical-align: text-top;
    background-image: url("/assets/glyphicons-halflings.png");
    background-position: 14px 14px;
    background-repeat: no-repeat;
    margin-top: 1px;
  }
}


.datepicker-days .weekend{
  background-color: #FFFFF5;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}

.datepicker-days .window, .datepicker-days .weekend.window{
  background-color: #E4FFC8;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}



