@import "font-awesome";
@import "fonts/nucleo_icons";
@import "nucleo";
@import "argon";
@import 'shared/variables/main_colors'; // mdb
@import 'mdb/mdb.scss'; // mdb
@import 'animate';
@import 'home/svg';
@import 'home/gmaps';
@import 'shared/styles/spinner';
@import "shared/whcookies";
@import "shared/cookies";
@import 'responsive';
@import 'nav';
@import 'footer';
@import 'pages';
@import 'contact';
@import 'index';
@import "select2";
@import "select2_customs";
@import "dotted-scrollspy";
@import url('https://fonts.googleapis.com/css?family=Yantramanav:100,300,400,500,700,900');
$custom-font: 'Yantramanav', sans-serif;
$custom-blue-color: #46b5e1;
$border-color: #00838f;
$custom-red-color: #d73f2a;

.custom_border {
  border-top: 2px solid $border-color;
  border-bottom: 2px solid $border-color;
}

body {
  color: #222;
}

a {
  outline: none !important;
}

.text-custom-green {
  color: #0C7C31;
}

.fixed-height-card-description {
  overflow: scroll;
  height: 180px;
}

.icons-container.on-screen {
  .poland_map {
    transform: scale(1.4);
  }
}

.headroom--not-top {
  background-color: rgba(255,255,255,0.95) !important;
  border-bottom: 1px solid #e5e3e6;
  box-shadow: none;
}

.svg_120 {
  height: 120px;
  width: 120px;
}

.btn-round {
  box-sizing: border-box;
  border-width: 2px;
  font-size: 12px;
  font-weight: 600;
  padding: 0.5rem 18px;
  line-height: 1.75;
  cursor: pointer;
  text-transform: uppercase;
  opacity: 1;
  border-radius: 30px;
}

.mh-100vh {
  min-height: 100vh;
}

.hover-red-btn:hover {
  background-color: #c1393e !important;
  border: 1px solid #9e2d31 !important;
  color: white !important;
}

.hover-red-btn {
  background-color: #ffffff52;
}

.btn-custom-blue {
  border-color: $custom-blue-color;
  background-color: $custom-blue-color;
  color: white;

  &:hover {
    color: white;
  }
}

.btn-custom-red {
  border-color: $custom-red-color;
  background-color: $custom-red-color;
  color: white;

  &:hover {
    color: white;
  }
}

.text-custom-red {
  color: $custom-red-color;
}

.text-custom-blue {
  color: $custom-blue-color;
}

.radius-0 {
  border-radius: 0;
}

.input-group {
  flex-wrap: initial;
}

.btn:hover {
  box-shadow: none !important;
  transform: scale(1.05);
}

.md-form label {
  top: -.6em;
}

.md-form label.active {
  transform: translateY(-12px) scale(0.7);
}

.md-form input[type=date],
.md-form input[type=datetime-local],
.md-form input[type=email],
.md-form input[type=number],
.md-form input[type=password],
.md-form input[type=search-md],
.md-form input[type=search],
.md-form input[type=tel],
.md-form input[type=text],
.md-form input[type=time],
.md-form input[type=url],
.md-form textarea.md-textarea {
  width: 100%;
}

.select2-container .select2-choice {
  height: 30px;
  padding: 1px 1px 0 8px;
}

.form-check-input[type="checkbox"].filled-in:checked + label:after,
label.btn input[type="checkbox"].filled-in:checked + label:after {
  border: 2px solid #0398a5;
  background-color: #00a4b6;
}

.form-check-input[type="checkbox"].filled-in:not(:checked) + label:after,
label.btn input[type="checkbox"].filled-in:not(:checked) + label:after {
  top: 2px;
}

.form-check-input[type="checkbox"].filled-in:checked + label:after,
label.btn input[type="checkbox"].filled-in:checked + label:after {
  top: 2px;
}

.form-check-input[type="checkbox"].filled-in:checked + label:before,
label.btn input[type="checkbox"].filled-in:checked + label:before {
  top: 2px;
}

.has-danger .custom-control,
.has-danger .form-check-inline,
.has-danger .form-check-label,
.has-danger .form-control-feedback,
.has-danger .form-control-label {
  color: #d9534f;
}

.form-group.has-danger {
  margin-left: -1em;
  margin-right: -1em;
  padding-left: 1em;
  padding-right: 1em;
  background-color: #ffecf0;
  border: 1px solid #ebcccc;
  margin-top: 1em;

  .md-form {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

.has-danger:after,
.has-success:after {
  content: none !important;
}

.tos_switch span {
  margin-right: 0;
  cursor: pointer;
  padding-left: 10px;
  z-index: 9999999;
}

.tos_switch span:first-child {
  padding-right: 10px;
}

.tos_switch span.active_switch {
  color: #027280 !important;
}

.tos_switch span:last-child {
  padding-left: 10px;
  border-left: 1px solid #ececec;
}

label.researcher_tos_label {
  color: #313131;
  padding: 10px;
  font-family: news cycle;
  color: #393939;
  height: auto;
  line-height: 1.3;
  padding-top: 1px;
  font-family: sans-serif;
  font-weight: normal;
  font-size: 14px;
  border: 1px solid #ccc;
}

.download_tos_pdf span {
  margin-left: 0;
  color: red;
  margin-right: 10px;
}

.researcher_tos label.boolean {
  left: -35px;
  color: transparent !important;
  top: 30px !important;
}

.form-group.has-danger.researcher_tos {
  margin-top: -20px;
  padding-bottom: 20px;
  margin-left: -1em;
  padding-left: 3em;
}

.summary_section {
  padding: 10px;
  padding-left: 15px;
  padding-right: 15px;
  line-height: 1.3;
  border: 1px solid #78bd54;
  background-color: rgba(120, 189, 84, 0.06);
  color: #316914;
  margin-top: 30px;
  transition: .5s;

  .default_sm_btn {
    background-color: white;
    margin-right: 10px !important;
  }

  label {
    color: #316914;
  }

  p {
    margin-bottom: 0;
  }

  &.danger {
    background-color: rgba(244, 0, 0, 0.1);
    color: #af2626;
    border: 1px solid rgb(244, 0, 0);

    label {
      color: #af2626;
    }
  }

  &.warning {
    background-color: rgba(255, 136, 0, 0.1);
    color: #bb6604 !important;
    border: 1px solid #e07800;

    label {
      color: #bb6604 !important;
    }
  }

  &.default {
    background-color: rgba(123, 123, 123, 0.06);
    border: 1px solid #bfbfbf;
    color: $main-dark-grey-color;

    label {
      color: #757575;
    }
  }
}

.footer .footer-link:hover, .footer .nav .nav-item {
  .footer-twitter-link {
    &:hover {
      color: #00aced !important;
    }
  }
}

.footer .footer-link:hover, .footer .nav .nav-item {
  .footer-linkedin-link {
    &:hover {
      color: #0077B5 !important;
    }
  }
}

.btn {
  border-radius: 0px;
  box-shadow: none;
}

#contact_section {
  min-height: 100vh;
}

#navbar-main {
  min-height: 54px;
}

.navbar-toggler {
  top: -5px;
}

.sign-in-border {
  margin-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  height: 60px !important;
  line-height: 40px !important;
}

.navbar-transparent .navbar-toggler-icon
{
    background-image: url('data:image/svg+xml;charset=utf8,%3Csvg viewBox=\'0 0 30 30\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath stroke=\'rgba(0, 0, 0, 0.95)\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-miterlimit=\'10\' d=\'M4 7h22M4 15h22M4 23h22\'/%3E%3C/svg%3E');
}

.scroll_text_container {
  min-height: 150px;
}

.invitation-text {
  position: absolute;
  bottom: 20%;
  left: 10%;
  color: white;
  font-size: 36px;
  line-height: 42px;
}

.alert {
  position: absolute;
  left: 0;
  right: 0;
  padding-top: .5rem;
  padding-bottom: .5rem;
  z-index: 99;
  transition: .5s;
  background-color: #FFF;
  color: #222 !important;

  &.alert_success, &.alert_notice {
    &:hover {
      background-color: #cfe8c6;
    }
  }

  &.alert_warning {
    &:hover {
      background-color: #faf2cc;
    }
  }

  &.alert_danger, &.alert_alert {
    &:hover {
      background-color: #f1c8ca;
    }
  }
}


@media (max-width: 576px) {
  .container {
    max-width: 98%;
  }

  .scroll_text_container {
    min-height: 40vh;
  }
}

@media (min-width: 1270px) {
  .container,
  .scroll-container {
    max-width: 1270px;
  }
  .container-short {
    max-width: 1040px !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .title-container {
    max-width: 90%;
  }
}
