@import "font-awesome";
@import "fonts/nucleo_icons";
@import "nucleo";
@import "argon";
@import 'animate';
@import 'home/svg';
@import 'home/gmaps';
@import 'shared/styles/spinner';
@import 'responsive';
@import 'nav';
@import 'footer';
@import 'pages';
@import 'contact';
@import 'index';
@import url('https://fonts.googleapis.com/css?family=Yantramanav:100,300,400,500,700,900');


#index-main-section {
  background: image-url('nurse_patient.jpg') no-repeat fixed top center;
  background-size: cover;

  &:before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    transition: 1s;
    opacity: 0.05;
    z-index: 0;
    background: image-url('pattern.png');
  }
}


$custom-font: 'Yantramanav', sans-serif;
$custom-blue-color: #46b5e1;
$border-color: #00838f;
$custom-red-color: #d73f2a;

.custom_border {
  border-top: 2px solid $border-color;
  border-bottom: 2px solid $border-color;
}

.text-custom-green {
  color: #0C7C31;
}

.fixed-height-card-description {
  overflow: scroll;
  height: 180px;
}

.icons-container.on-screen {
  .poland_map {
    transform: scale(1.4);
  }
}

.headroom--not-top {
  background-color: rgba(255,255,255,0.95) !important;
  border-bottom: 1px solid #e5e3e6;
  box-shadow: none;
}

.svg_120 {
  height: 120px;
  width: 120px;
}

.btn-round {
  box-sizing: border-box;
  border-width: 2px;
  font-size: 12px;
  font-weight: 600;
  padding: 0.5rem 18px;
  line-height: 1.75;
  cursor: pointer;
  text-transform: uppercase;
  opacity: 1;
  border-radius: 30px;
}

.mh-100vh {
  min-height: 100vh;
}

.red-btn {
  background-color: #bb0e1499;
  border: 1px solid #9e2d31 !important;
  color: white !important;
  transition: linear 0.2s;
}
.hover-red-btn:hover {
  background-color: #c1393e !important;
  border: 1px solid #9e2d31 !important;
  color: white !important;
}

.red-btn:hover {
  background-color: #ffffffcc !important;
  color: #bb0e14 !important;
}
.hover-red-btn {
  background-color: #ffffff52;
}

.btn-custom-blue {
  border-color: $custom-blue-color;
  background-color: $custom-blue-color;
  color: white;

  &:hover {
    color: white;
  }
}

.btn-custom-red {
  border-color: $custom-red-color;
  background-color: $custom-red-color;
  color: white;

  &:hover {
    color: white;
  }
}

.text-custom-red {
  color: $custom-red-color;
}

.text-custom-blue {
  color: $custom-blue-color;
}

.radius-0 {
  border-radius: 0;
}

.input-group {
  flex-wrap: initial;
}

.btn:hover {
  box-shadow: none !important;
  transform: none !important;
}


.navbar-transparent .navbar-nav .nav-link {
  color: white !important;
}

.main-title-container .main-title {
  color: white !important;
}

.main-title-container h3.main-title-desc {
  color: white !important;
}

main #cookies-wrapper {
  display: none !important;
}
