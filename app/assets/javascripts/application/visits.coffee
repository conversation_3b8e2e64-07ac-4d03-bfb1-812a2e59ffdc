$ ->
  $('form#new_visit .new_visit_datepicker').pickadate
    format: 'dd-mm-yyyy'
    onSet: (context) ->
      days_ago_section = $('.new_visit_days_ago')
      date = new Date(context.select)
      return if isNaN date
      today = new Date
      days_ago = Math.round((date - today)/(1000*60*60*24));
      days_ago_section.find('.first_row_text').text(Math.abs(days_ago))
      days_diff = if today >= date then 'Days ago' else 'Days from now'
      days_ago_section.find('.second_row_text').text(days_diff)
      # days_ago_section.show()
