$ ->
  input = document.getElementById('search_term')
  if input
    autocomplete
      input: input
      fetch: (text, update) ->
        suggestions = []

        $.ajax(
          method: "GET",
          url: "/v2/sponsor/search",
          data: { term: text }
          dataType: 'json').done (data) ->
            for row in data
              suggestions.push { label: row.name, value: row.link }
            update(suggestions)

      onSelect: (item) ->
        window.location.href = item.value
      emptyMsg: 'Nothing found, please try a different query.'
      debounceWaitMs: 300