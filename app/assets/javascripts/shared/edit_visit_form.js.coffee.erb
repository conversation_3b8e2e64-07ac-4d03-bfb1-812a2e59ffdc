# visit next step
isScrolledIntoView = (elem) ->
  docViewTop = $(window).scrollTop()
  docViewBottom = docViewTop + $(window).height()
  elemTop = $(elem).offset().top
  elemBottom = elemTop + $(elem).height()
  elemBottom <= docViewBottom and elemTop >= docViewTop

animate_visit_helper_if_visible = ->
  el = $("#visit_helper_icon")
  if el.length != 0
    if isScrolledIntoView(el)
      unless el.hasClass('animated')
        el.addClass('animated bounceInLeft')
    else
      if el.hasClass('animated')
        el.removeClass('animated bounceInLeft')

cost_row_is_valid = (tr) ->
  tr = $(tr)
  select = tr.find(".visit_visit_payment_categorizations_visit_payment_category_id select").val()
  input = tr.find(".visit_visit_payment_categorizations_amount input").val()

  return false if select == "" and input != "0.00"
  return false if select == "" and input == "0.00"
  return false if select != "" and input == "0.00"
  return true

move_visit_icon = ->
  arr = [[$(".visit_visit_date"), $("input#visit_visit_date").val() == "", 5]]

  any_cost_valid = false

  $("#costs tr.nested-fields:visible").not('.predicted_row').each ->
    console.log( cost_row_is_valid(this) )
    if cost_row_is_valid(this) == true
      any_cost_valid = true
      return

  arr.push([$("#costs .nested-fields:visible").first(), !any_cost_valid, 0])
  arr.push([$("#visit_happened").parent(".md-form").parent(".md-form"), $("#visit_happened").prop('checked') == false, -5])

  if $("#visit_save_btn").length != 0
    arr.push([$("#visit_save_btn"), true, 10])

  new_arr = jQuery.map(arr, (n, i) ->
    n[1]
  )

  if jQuery.inArray( true, new_arr ) != -1
    if $("#visit_helper_icon").length == 0
      if $("#visit_previous_costs_info").length != 0
        $("form.edit_visit").append('<i class="fa fa-angle-double-right" id="visit_helper_icon"></i>')
  else
    $("#visit_helper_icon").remove


  $.each arr, (key, value) ->
    if value[1] == true

      return false if value[0].length == 0 || $("#panel1").length == 0

      top = value[0].offset().top - $("#panel1").offset().top - value[2]
      $("i#visit_helper_icon").removeClass('animated bounceInLeft')
      $("i#visit_helper_icon").css('top', top+'px')
      animate_visit_helper_if_visible()
      return false
    else
      if key == (arr.length - 1)
        $("#visit_helper_icon").remove()
    return

subject_signed_in = ->
  $('body.authenticated.subject').length == 1
calc_days_before_visit = ->
  $("input#visit_visit_date").on 'change', (e) ->
    start_split = $(this).val().split('-')
    start = new Date(start_split[2], start_split[1] - 1, start_split[0]);
    now = new Date()
    diff = Math.ceil(start - now)
    days = Math.ceil(diff/1000/60/60/24)

    abs_days = Math.abs(days)
    day_plurar = "days"

    if $('#visit_actual_state').val() == "Anticipated" && ($(this).val() != "")
      $('#visit_actual_state').val('To be reimbursed')

    if abs_days == 1
      day_plurar = "day"
    $("h1.visits_completed.text-xs-right.red_color").text(abs_days)
    if days <= 0
      $('h3.text-xs-right').text(day_plurar+" after the visit")
    else
      $('h3.text-xs-right').text(day_plurar+" before the visit")
    return

color_visit_date_label = (input) ->
  label = $(input).parent().find('label')
  if $(input).val().length > 0
    label.removeClass('red')
  else
    label.addClass('red')

visit_date_checker = ->
  el = $("input#visit_visit_date")
  color_visit_date_label(el)
  $(el).on 'change', (e) ->
    color_visit_date_label(el)

scroll_to_prediction_section = ->
  if $('.predicted_row').length > 0
    if $.urlParam('enforce_prediction') == 'true'
      $('html, body').animate { scrollTop: $('.predicted_row').offset().top - 185 }, 1000

scroll_to_activity_section = ->
  if $('#activity_section').length > 0
    if $.urlParam('focus') == 'activity_log'
      $('html, body').animate { scrollTop: $('#activity_section').offset().top - 20 }, 1000

handle_vpc_amount_focus = (el) ->
  el = $(el)
  if el.is(":focus")
    show_max_vpc_limit(el)
  else
    handle_vpc_amount_focusout(el)

show_max_vpc_limit = (el) ->
  limit = limit_for_vpc(vpc_id_for_amount(el))
  currency = $('form[data-behavior="pay_for_visit_form"]').data('currency') || 'PLN'

  if limit?
    el.parent('.md-form').append("<span class='vpc_limit'>Max. #{ (Math.round(limit * 100) / 100).toFixed(2) } #{ currency }</span>")

handle_vpc_amount_focusout = (el) ->
  adjust_cost = $('form[data-behavior="pay_for_visit_form"]').data('adjust-cost')

  return unless adjust_cost

  hide_max_vpc_limit(el)
  limit = parseFloat limit_for_vpc(vpc_id_for_amount(el))
  amount = parseFloat el.val()
  el.val((Math.round(limit * 100) / 100).toFixed(2)) if amount > limit

hide_max_vpc_limit = (el) ->
  el.parent('.md-form').find('.vpc_limit').remove()

vpc_id_for_amount = (el) ->
  el.parents('tr.nested-fields').find('.visit_visit_payment_categorizations_visit_payment_category_id option:selected').val()

limit_for_vpc = (id) ->
  try
    $('#available_vpc_amount_limits').data("limit-#{id}")
  catch e
    console.log e
    null

$(window).on 'scroll', ->
  animate_visit_helper_if_visible()

$ ->
  return unless $('form[data-behavior="pay_for_visit_form"]').length > 0

  $(document).on 'focus blur', '.visit_visit_payment_categorizations_amount input', (e) ->
    handle_vpc_amount_focus(this)
    calculate_km_after_km_change(e)
    calculate_total_amount()

  visit_id = $('form.edit_visit').data('visit-id')
  currency = $('form[data-behavior="pay_for_visit_form"]').data('currency') || 'PLN'

  $('.visit_visit_payment_categorizations_amount input').on 'keypress', (e) ->
    code = e.keyCode or e.which
    if code == 13
      if ($(this).closest('.nested-fields').next().length > 0)
        $(this).closest('.nested-fields').next().find('.visit_visit_payment_categorizations_amount input').focus().get(0).setSelectionRange(0,0)
    else
    return

  predicted_cost_rows_without_input_values = () ->
    $("tr.predicted_row.nested-fields").filter ->
      $(@).find('input.numeric').val() == '' && $(@).find('input.string').val() == ''
  scroll_to_prediction_section()
  scroll_to_activity_section()
  visit_date_checker()

  move_visit_icon()
  animate_visit_helper_if_visible()

  clear_all_predicted_cost = () ->
    $.each predicted_cost_rows_without_input_values(), (index, element) ->
      clear_cost_row(element)

  check_visit_date_valid = (input) ->
    return if subject_signed_in()
    current_date = moment($(input).val(), 'DD-MM-YYYY').toDate()
    last_visit_date = Date.parse $(input).data('last-visit-date')
    next_visit_date = Date.parse $(input).data('next-visit-date')
    $('#visit_date__date_warning').remove()
    if (current_date? and last_visit_date?) and (current_date < last_visit_date)
      last_visit_name = $(input).data('last-visit-name')
      last_visit_date_formatted = moment(last_visit_date).format('DD-MM-YYYY')
      text = "The visit should be after #{ last_visit_name } on #{ last_visit_date_formatted }."
      $('.visit_visit_date').append("<p id ='visit_date__date_warning'>#{text}</p>")
    else if (current_date? and next_visit_date?) and (current_date > next_visit_date)
      next_visit_name = $(input).data('next-visit-name')
      next_visit_date_formatted = moment(next_visit_date).format('DD-MM-YYYY')
      text = "The visit should be before #{ next_visit_name } on #{ next_visit_date_formatted }."
      $('.visit_visit_date').append("<p id ='visit_date__date_warning'>#{text}</p>")


  check_visit_date_valid($('[data-behavior="visit_date_input"]').first())
  $('[data-behavior="visit_date_input"]').on 'change', ->
    check_visit_date_valid(this)

  $("tr.predicted_row select").on 'change', (e) ->
    if $(this).closest('tr.nested-fields').hasClass('predicted_row')
      clear_cost_row(this)
    $(this).unbind(e)

  calc_days_before_visit()

  $('tr.predicted_row .visit_visit_payment_categorizations_amount input').focusin(->
    parent_tr = this.closest('tr')
    data_id = $(parent_tr).data('id')
    $("tr.vpc_copy[data-id='"+data_id+"']").children().last().text(this.placeholder)
    return
  ).focusout ->
    parent_tr = this.closest('tr')
    data_id = $(parent_tr).data('id')
    $("tr.vpc_copy[data-id='"+data_id+"']").children().last().text("Predicted cost")
    return

  $(document).on 'click', '[data-behavior="accept_prognosed_vpc"]', ->
    parent_tr = $(this).parents('tr')
    remove_vpc_copy_tr(parent_tr)
    amount_input = parent_tr.find('.visit_visit_payment_categorizations_amount input')
    prognosed_amount = amount_input.data('prognosed-amount')
    parent_tr.find('input').prop('placeholder', '')
    prognosed_amount = parseFloat(prognosed_amount).toFixed(2)
    amount_input.val(prognosed_amount)
    parent_tr.removeClass('predicted_row')

    km_input = parent_tr.find('.visit_visit_payment_categorizations_km input')
    if km_input.length > 0
      prognosed_amount = km_input.data('prognosed-amount')
      prognosed_amount = parseFloat(prognosed_amount).toFixed(0)
      km_input.val(prognosed_amount)

    calculate_total_amount()

  $(document).on 'click', '[data-behavior="clear_all_costs"]', (e) ->
    e.preventDefault()
    clear_all_costs()
    $(this).hide()
    $('.next_cost_btn').click()
    $('.next_cost_btn').click()
    setTimeout ( ->
      $(':focus').blur()
    ), 200

    show_cost_prediction_section()

  $(document).on 'click', '[data-behavior="clear_all_predicted_costs"]', (e) ->
    e.preventDefault()
    clear_all_predicted_cost()
    $(this).hide()

  $(document).on 'click', '.clear_cost_row', (e) ->
    clear_cost_row(e.target)

  $(document).on 'click', '.reject_visit_prediction', (e) ->
    # ta wizyta juz nie bedzie pokazywac predykcji by default
    $('#visit_predictions_allowed').val('f')
    row_id = $(@).data('row-id')
    $(".visit_vpc_upload_btn_wrapper[data-row-id=#{ row_id }]").show()

  $(document).on 'keyup', '.visit_visit_payment_categorizations_amount input', (e) ->
    parent_tr = $(e.target).closest('tr')
    remove_vpc_copy_tr(parent_tr)

  $(document).on 'keyup', '.visit_visit_payment_categorizations_km input', (e) ->
    parent_tr = $(e.target).closest('tr')
    remove_vpc_copy_tr(parent_tr)

  setTimeout( ->
    $('form.edit_visit').data 'serialize', $('form.edit_visit').serialize()
  , 1000)

  $(window).bind 'beforeunload', (e) ->
    if $('form.edit_visit').serialize() != $('form.edit_visit').data('serialize')
      return true
    else
      e = null
    return

  $('form.edit_visit').on 'change', (e) ->
    if e.target.id == 'visit_happened'
    else
      # TODO: zmienić treść alertu
      $("input[name='save_and_pay_visit']").val('Pay to Subject')
      if $('input#visit_happened').prop('checked') == true
        $('input#visit_happened').prop('checked', false)
        html_alert = '<div class="alert alert-alert">
                    ALERT
                  </div>'
        $('.alert-messages').html(html_alert)
        $('.alert').show()
    check_visit_happend("input#visit_happened")
    move_visit_icon()
    return

  $('form.edit_visit').submit ->
    $(window).unbind 'beforeunload'

  $('.visit_date_container').hover (->
    if !($('input#visit_visit_date').val() == "")
      $('#clear_visit_date').css('opacity', '1')
    return
  ), ->
    $('#clear_visit_date').css('opacity', '0')
    return

  $('#clear_visit_date').on 'click', (e) ->
    e.preventDefault()
    $("input#visit_visit_date").val('')
    visit_date_checker()
    $("label[for=visit_visit_date]").removeClass('active')
    $("#visit_happened").prop('checked', false)
    move_visit_icon()


  $('input[type="submit"]').on 'click', ->
    $('form.edit_visit').data 'serialize', $('form.edit_visit').serialize()
    name = $(@).attr('name')
    $('#visit_action_type').val(name)

  check_visit_happend = (checkbox_input) ->
    unless $(checkbox_input).prop('checked')
      $('.summary_section.total_amount').addClass('warning')
    else
      $('.summary_section.total_amount').removeClass('warning')

  check_visit_happend("input#visit_happened")

  form_data = {}

  engine_km_cost =
    above_900: "<%= Modifier.km_cost %>"

  $("input#visit_happened").on 'change', (e) ->
    check_visit_happend(this)
    clear_all_predicted_cost()

  $('form[data-behavior="pay_for_visit_form"] input').on 'change', (e) ->
    set_pay_btn_title_as_pay_and_save()

  $('.visit_visit_payment_categorizations_amount input').on 'change', (e) ->
    calculate_km_after_km_change(e)

  $('a.next_cost_btn.add_fields').on 'click', (e) ->
    setTimeout (->
      $('.md-form label').css('transition', '0.2s').css('opacity', '1')

      $('.numeric_only_input').inputmask 'numeric+',
        rightAlign: true
        digits: 2
        radixPoint: '.'
    ), 200

  $(document).on 'click', '.visit_visit_payment_categorizations_amount input.numeric_only_input, .visit_visit_payment_categorizations_km input.numeric_only_input', ->
    value = parseInt $(@).val()
    $(@).val('') if value == 0

  calculate_km_after_km_change = (e) ->
    target = e.target
    target_split = target.id.split('_')
    if target_split[target_split.length - 1] == 'id'
      amount_id_prefix = target_split.slice(0, target_split.length - 4).join('_')
      amount_id = amount_id_prefix + '_amount'
      amount = parseFloat($("##{amount_id}").val())
    else
      amount = parseFloat(target.value)
    km_input = $(target).closest('tr').find('.visit_visit_payment_categorizations_km input')
    $(km_input).parent().find('label').addClass("active")
    selected_engine = $(target).parents('.nested-fields').find('.visit_visit_payment_categorizations_engine option:selected').val()
    km_amount_multi = km_input.data('country-km-amount')
    km_from_amount = amount / km_amount_multi
    km_input_val = (Math.round(km_from_amount * 100) / 100).toFixed(0)

    if km_input_val == "NaN" || km_input_val == '0.00' || km_input_val == '0,00'
      km_input.val(0)
    else
      km_input.val(km_input_val)

  $('.visit_visit_payment_categorizations_engine select').on 'change', (e) ->
    handle_engine_select(e)

  recalculate_km_amount = (e) ->
    selected_engine = e.target.value
    km_input = $(e.target).parents('.nested-fields').find('.visit_visit_payment_categorizations_km input')
    km_amount_multi = km_input.data('country-km-amount')
    km_input_val = km_input.val()
    amount_from_km = km_input_val * km_amount_multi
    amount_input = $(e.target).parents('.nested-fields').find('.visit_visit_payment_categorizations_amount input')
    amount_input.val (Math.round(amount_from_km * 100) / 100).toFixed(2)

  $('.visit_visit_payment_categorizations_amount input').on 'focusout', (e) ->
    row = $(this).closest('tr.nested-fields')
    km_input = row.find('.visit_visit_payment_categorizations_km input')
    calculate_km_after_km_change(e) if km_input
    calculate_total_amount()

  $(".visit_visit_payment_categorizations_descr input").on 'focusout', (e) ->
    if $(this).val() == ""
      $(this).parent().find('label').removeClass('active')

  calculate_total_amount = ->
    amount_inputs = $('.visit_visit_payment_categorizations_amount input')
    total = 0

    for input in amount_inputs
      value = $(input).val()
      if $(input).hasClass('vpc_copy')
      else if value == ""
        $(input).parent().find('label').addClass('active')
        $(input).val('0.00')
      else
        $(input).val( parseFloat($(input).val()).toFixed(2) )

      total += parseFloat value if value
    total_with_precision = (Math.round(total * 100) / 100).toFixed(2)
    $('#total_visit_cost').text " #{total_with_precision}"
    if form_data['total_cost'] and form_data['total_cost'] != total_with_precision
      reset_visit_happened_checkbox()
    form_data['total_cost'] = total_with_precision

    prev_visit_costs_info(total)
    move_visit_icon()

    if total <= 0
      disable_pay_btns()
    else
      enable_pay_btns()

  disable_pay_btns = ->
    disable_pay_btn('#visit_pay_btn')
    disable_pay_btn('#visit_pay_to_site_btn')

  disable_pay_btn = (selector) ->
    return if $(selector).attr('disabled') == 'disabled'
    return if $(selector).data('force-disabled') == true
    # tooltip_wrapper = $('#visit_form_buttons').data('unauthorized-wrapper')
    $(selector).attr('disabled', true).wrap("<div class='tooltip-source-wrapper' data-toggle='tooltip' style='display: inline-block;' data-position='bottom' data-title='The amount due must be greater than 0.00 #{ currency }.'></div>")
    $('.tooltip-source-wrapper').tooltip()

  enable_pay_btns = ->
    enable_pay_btn('#visit_pay_btn')
    enable_pay_btn('#visit_pay_to_site_btn')

  enable_pay_btn = (selector) ->
    return if $(selector).attr('disabled') == undefined
    return if $(selector).data('force-disabled') == true
    $(selector).unwrap()
    $(selector).attr('disabled', false)

  handle_engine_select = (e) ->
    recalculate_km_amount(e)
    calculate_total_amount()


  prev_visit_costs_info = (total) ->
    prev_cost_div = $('#visit_previous_costs_info')
    return false if prev_cost_div.length == 0

    prev_cost = prev_cost_div.data('prevVisitCosts')

    if total > 0.0 && prev_cost
      val = ((1 - (total / prev_cost)) * 100).toFixed(1)
      val = Math.abs(val)

      if total > prev_cost
        val_span = '<span class="higher">'+val+'\%</span>'
        text = 'This cost is ' + val_span + ' higher than the cost of the previous visit.'
      else
        val_span = '<span class="lower">'+val+'\%</span>'
        text = 'This cost is ' + val_span + ' lower than the cost of the previous visit.'

      $('#visit_previous_costs_info').html(text);
    else
      $('#visit_previous_costs_info').html('');


  clear_cost_row = (el) ->
    row = $(el).closest('tr.nested-fields')
    row.find('.visit_visit_payment_categorizations_amount input').val('')
    row.find('.visit_visit_payment_categorizations_km input').val('')
    row.find('.visit_visit_payment_categorizations_descr input').val('')
    row.find('label').removeClass('active')
    # row.find('td.amount_inputs label').removeClass('active')
    row.find('input').prop('placeholder', '')
    row.removeClass('predicted_row')
    remove_vpc_copy_tr(row)
    category_select = row.find('select.mdb-select')
    category_select.material_select('destroy')
    category_select.val(category_select.find('option:first').val())
    category_select.material_select()
    calculate_total_amount()
    set_pay_btn_title_as_pay_and_save()
    toggle_km_fields_target(row)
    # re-initialize

    amount_input = row.find('.visit_visit_payment_categorizations_amount  input')
    km_input = row.find('.visit_visit_payment_categorizations_km  input')
    descr_input = row.find('.visit_visit_payment_categorizations_descr  input')

    category_select.on 'change', (e) ->
      toggle_km_fields(e)
      $(this).closest('tr').find('.visit_visit_payment_categorizations_amount').find('label').addClass('active')
      calculate_km_after_km_change(e)
      set_pay_btn_title_as_pay_and_save()

    amount_input.on 'change', (e) ->
      calculate_km_after_km_change(e) if km_input
      calculate_total_amount()

  amount_inputs = $('.visit_visit_payment_categorizations_amount input')
  category_inputs = $(".visit_visit_payment_categorizations_visit_payment_category_id select")
  km_inputs = $(".visit_visit_payment_categorizations_km input")

  toggle_km_fields_target = (target) ->
    category_div = target.find('.visit_visit_payment_categorizations_visit_payment_category_id')
    row = category_div.closest('.nested-fields')
    selected_cat = category_div.find('ul.select-dropdown li.active').text()

    if selected_cat == 'Kilometers' or selected_cat == 'Mileage' or selected_cat == 'Kilometry'
      show_km_inputs(row)
      # reset_amount_input(row)
      calculate_total_amount()
    else
      hide_km_inputs(row)
      # reset_amount_input(row)
      reset_km_inputs(row)
      calculate_total_amount()

  toggle_km_fields = (e) ->
    target = $(e.target)
    category_div = target.closest('.visit_visit_payment_categorizations_visit_payment_category_id')
    row = category_div.closest('.nested-fields')
    selected_cat = category_div.find('ul.select-dropdown li.active').text()

    if selected_cat == 'Kilometers' or selected_cat == 'Mileage' or selected_cat == 'Kilometry'
      show_km_inputs(row)
      # reset_amount_input(row)
      calculate_total_amount()
    else
      hide_km_inputs(row)
      # reset_amount_input(row)
      reset_km_inputs(row)
      calculate_total_amount()

  calculate_km_amount = (e) ->
    if $(e.target).val() == ""
      $(e.target).val(0)

    amount_field_id = "#{ e.target.id[0..-4] }_amount"
    amount_field = $("##{ amount_field_id }")
    input = $(e.target)
    km_value = parseFloat input.val()
    selected_engine = $(e.target).parents('.nested-fields').find('.visit_visit_payment_categorizations_engine option:selected').val()
    if selected_engine == undefined
      selected_engine = "above_900"
    km_amount_multi = input.data('country-km-amount')
    amount = km_value * km_amount_multi
    amount_rounded = (Math.round(amount * 100) / 100).toFixed(2)
    amount_field.val amount_rounded
    amount_field.focus()

  add_cost_row = ->
    $('.add_fields').click()

  no_cost_rows_present = ->
    $('#costs .nested-fields:visible .change_cost_row_btn').length == 0


  $('#visit__edit__visit_payment_categorizations').on('cocoon:after-insert', (e, added_vpc) ->
    added_vpc_obj = $(added_vpc)
    amount_input = added_vpc_obj.find('.visit_visit_payment_categorizations_amount  input')
    NumberFormatting.convert_number_inputs_to_text([amount_input])
    km_input = added_vpc_obj.find('.visit_visit_payment_categorizations_km  input')
    descr_input = added_vpc_obj.find('.visit_visit_payment_categorizations_descr  input')
    category_select = added_vpc_obj.find('.visit_visit_payment_categorizations_visit_payment_category_id select')
    engine_select = added_vpc_obj.find('.visit_visit_payment_categorizations_engine select')

    $('.numeric_only_input').inputmask 'numeric+',
      rightAlign: true
      digits: 2
      radixPoint: '.'

    $('.visit_visit_payment_categorizations_amount input').on 'change', (e) ->
      calculate_km_after_km_change(e)

    $(km_input).attr('type', 'text')
    for input in [amount_input, km_input, descr_input]
      input.on 'change', (e) ->
        set_pay_btn_title_as_pay_and_save()

    amount_input.val('0.00')

    setTimeout ( ->
      amount_input.next('label').addClass('active')
      amount_input.focus()
    ), 200

    category_select.material_select()
    engine_select.material_select()
    service = new AddClassToNthChildMdbSelect($('[data-behavior="add_class_to_nth_option"]').last())
    service.call()


    amount_input.on 'change', (e) ->
      calculate_km_after_km_change(e) if km_input
      calculate_total_amount()

    km_input.on 'change', (e) ->
      calculate_km_amount(e)
      calculate_total_amount()

    category_select.on 'change', (e) ->
      toggle_km_fields(e)
      $(this).closest('tr').find('.visit_visit_payment_categorizations_amount').find('label').addClass('active')
      calculate_km_after_km_change(e)
      set_pay_btn_title_as_pay_and_save()

    engine_select.on 'change', (e) ->
      handle_engine_select(e)
      set_pay_btn_title_as_pay_and_save()

    move_visit_icon()

  ).on('cocoon:after-remove', (e) ->
    reset_hidden_amounts()
    calculate_total_amount()
    set_pay_btn_title_as_pay_and_save()

    $("tr.error_message").remove()
    visible_vpc_input_rows = $('tr.nested-fields:visible').length

    if visible_vpc_input_rows == 0
      $('.add_fields').first().click()

  )

  amount_inputs.on 'change', (e) ->
    calculate_km_after_km_change(e) if km_input
    calculate_total_amount()

  category_inputs.on 'change', (e) ->
    toggle_km_fields(e)
    calculate_km_after_km_change(e)

  km_inputs.on 'change', (e) ->
    calculate_km_amount(e)
    calculate_total_amount()

  add_cost_row() if no_cost_rows_present()

  clear_all_costs = ->
    btns = $('#costs .nested-fields:visible .change_cost_row_btn')
    for btn in btns[1..(btns.length - 1)]
      btn.click()
    clear_cost_row($('#costs .nested-fields:visible .change_cost_row_btn'))
    set_pay_btn_title_as_pay_and_save()

  $('[data-behavior="show_clear_all_costs_modal"]').on 'click', ->
    bootbox.confirm
      message: 'Are you sure you want to clear all costs?'
      buttons:
        confirm:
          label: 'Yes'
          className: 'btn-success'
        cancel:
          label: 'No'
          className: 'btn-danger'
      callback: (result) ->
        console.log 'This was logged in the callback: ' + result
        clear_all_costs() if result


  show_km_inputs = (row) ->
    row.find('td.amount_inputs').addClass('with_km_input')
    row.find('td.amount_inputs .visit_visit_payment_categorizations_km input').val(0)
    row.find('td.amount_inputs .visit_visit_payment_categorizations_km label').addClass('active')

    $('.numeric_only_input').inputmask 'numeric+',
      rightAlign: true
      digits: 2
      radixPoint: '.'

    # row.find('.visit_visit_payment_categorizations_amount input').prop('readonly', true)

  hide_km_inputs = (row) ->
    row.find('td.amount_inputs').removeClass('with_km_input')
    # row.find('.visit_visit_payment_categorizations_amount input').prop('readonly', false)

  reset_amount_input = (row) ->
    row.find('.visit_visit_payment_categorizations_amount input').val('')

  reset_km_inputs = (row) ->
    row.find('.visit_visit_payment_categorizations_km input').val('')

  reset_hidden_amounts = ->
    $('.nested-fields:hidden').find('.visit_visit_payment_categorizations_km input').val('')
    $('.nested-fields:hidden').find('.visit_visit_payment_categorizations_amount input').val('')
    calculate_total_amount()

  set_pay_btn_title_as_pay_and_save = ->
    $("#pay_btn").addClass('disabled')
    $('#pay_btn_wrapper').tooltip
      placement: 'bottom'
      title: 'You must save your changes first.'

  reset_visit_happened_checkbox = ->
    checkbox = document.getElementById('visit_happened')
    checkbox.checked = false if checkbox.checked


  calculate_total_amount()

  remove_vpc_copy_tr = (tr) ->
    tr.find('input').removeClass('vpc_copy')
    tr.find('.vpc_copy_actions').remove()
    row_id = tr.data('id')
    vpc_copy_tr = $("tr.vpc_copy[data-id='#{row_id}']")
    vpc_copy_tr.remove()
    tr.find('.remove_btn').show()

  show_cost_prediction_section = ->
    html = $('[data-predicted-cost-section-html]').first().data('predicted-cost-section-html')
    $('#cost_prediction').html(html)
