MIN_PASSWORD_LENGTH = 6
$ ->
  disable_submit_btn = (btn, password_correct) ->
    if password_correct
      $(btn).attr('disabled', false)
    else
      $(btn).attr('disabled', true)

  user_password_correct = (password) ->
    password.length >= MIN_PASSWORD_LENGTH
  password_level = (password) ->
    strength = 0
    if password.length > MIN_PASSWORD_LENGTH
      strength += 1
    # // If password contains both lower and uppercase characters, increase strength value.
    if password.match(/([a-z].*[A-Z])|([A-Z].*[a-z])/)
      strength += 1
    # // If it has numbers and characters, increase strength value.
    if password.match(/([a-zA-Z])/) && password.match(/([0-9])/)
      strength += 1
    # // If it has one special character, increase strength value.
    if password.match(/([!,%,&,@,#,$,^,*,?,_,~])/)
      strength += 1
    # // If it has two special characters, increase strength value.
    if password.match(/(.*[!,%,&,@,#,$,^,*,?,_,~].*[!,%,&,@,#,$,^,*,?,_,~])/)
      strength += 1


    if strength >= 4
      'Mocne'
    else if strength >= 3
      'Średnie'
    else if strength >= 2
      'Słabe'
    else
      'Bardzo słabe'

  window.broker = $({})
  broker.on 'password_changed', (e, password) ->
    password_correct = user_password_correct(password)
    pass_input = $('div.form-group.user_password')
    indicator = $('#first_password_valid_indicator')
    if password.length > 0
      indicator.css('visibility', 'visible')
    else
      indicator.css('visibility', 'hidden')
      return

    if password_correct == true
      indicator.text(password_level(password)).addClass('password_valid_indicator__valid').removeClass('password_valid_indicator__invalid')
    else
      indicator.text("Zbyt krótkie").removeClass('password_valid_indicator__valid').addClass('password_valid_indicator__invalid')

  broker.on 'user_password_confirmation_changed', (e, input_field) ->
    source = $(input_field).data('password-source')
    source = $(source)
    pass = source.val()
    confirmation = $(input_field).val()
    indicator = $('#password_confirmation_valid_indicator')
    unless user_password_correct(pass)
      indicator.css('visibility', 'hidden')
      return

    if confirmation.length > 0
      indicator.css('visibility', 'visible')
    else
      indicator.css('visibility', 'hidden')
      return
    password_correct = pass == confirmation

    disable_submit_btn($(input_field).closest('form').find('.submit_btn'),  password_correct)
    if password_correct
      indicator.text('OK').addClass('password_valid_indicator__valid').removeClass('password_valid_indicator__invalid')
    else
      indicator.text("Doesn't match").removeClass('password_valid_indicator__valid').addClass('password_valid_indicator__invalid')

  $('[data-behavior="validate_user_password"]').on 'keyup', ->
    if $(@).data('object') == 'student'
      MIN_PASSWORD_LENGTH = 5

    password = $(@).val()
    broker.trigger('password_changed', [password])
    broker.trigger('user_password_confirmation_changed', [$('[data-behavior="validate_password_confirmation"]')])

    if $(@).data('passwordConfirmationSource')
      btn = $(@).closest('form').find('.submit_btn')
      confirmation = $($(@).data('passwordConfirmationSource')).val()
      password_correct = password == confirmation
      disable_submit_btn(btn, password_correct)

  $('[data-behavior="validate_password_confirmation"]').on 'keyup', ->
    broker.trigger('user_password_confirmation_changed', [this])
