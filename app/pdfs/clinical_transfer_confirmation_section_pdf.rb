class ClinicalTransferConfirmationSectionPdf < SimpleDelegator
  attr_reader :locale

  def initialize(prawn, params = {})
    super(prawn)

    @clinical_transfer = params[:clinical_transfer]
    @locale = params.fetch :locale, :pl
  end

  def render
    I18n.with_locale(locale) do
      header
      text "#{I18n.t('clinical_transfers_report.subject_reimbursement').upcase }", style: :bold, size: 18
      move_down(-1)
      text "#{I18n.t('clinical_transfers_report.transfer_confirmation') }", style: :bold, color: "666666", size: 15

      move_down(35)
      text "#{I18n.t('clinical_transfers_report.transfer_receiver') }", style: :bold, color: "000000", size: 11
      move_down(2)

      horizontal_line 0, 524
      stroke
      move_down(0)

      bounding_box([0,618], width: 260) do

        if @clinical_transfer.transfered_to == 'clinical_center'
          title(txt: "#{I18n.t('clinical_transfers_report.site_receiver') }:")
          body(txt: @clinical_transfer.masked_destination(full_mask: true))
        else
          title(txt: "#{I18n.t('clinical_transfers_report.first_and_last_name') }:")
          move_down(3)
          body(txt: @clinical_transfer.masked_destination(full_mask: true))
          move_down(-3)
        end

        title(txt: "#{I18n.t('clinical_transfers_report.receiver_acc_nr') }:")
        if @clinical_transfer.formatted_account_number and @clinical_transfer.formatted_account_number != "0"
          body(txt: "#{ @clinical_transfer.in_poland? ? 'PL' : '' }#{ MaskAccountNumber.call(@clinical_transfer.formatted_account_number)}".gsub('PLPL', 'PL'))
        else
          body(txt: "#{I18n.t('clinical_transfers_report.receiver_post_transfer') }")
        end

        title(txt: "#{I18n.t('clinical_transfers_report.transfer_amount') }:")
        case locale
        when "en"
          body(txt: "#{ @clinical_transfer.currency } #{("%.2f" % @clinical_transfer.amount).gsub(/\./,'.')}")
        else
          body(txt: "#{ @clinical_transfer.currency } #{("%.2f" % @clinical_transfer.amount).gsub(/\./,',')}")
        end

      end


      bounding_box([300,618], width: 220) do
        title(txt: "#{I18n.t('clinical_transfers_report.user_code') }:")
        if @clinical_transfer.master? && @clinical_transfer.transfered_to == 'clinical_center'
          body(txt: "#{I18n.t('clinical_transfers_report.master_transfer_user_code')}")
        else
          body(txt: "#{@clinical_transfer.clinical_user.patient_code.upcase}")
        end

        title(txt: "#{I18n.t('clinical_transfers_report.transfer_date') }:")

        case locale
        when "en"
          body(txt: @clinical_transfer.status_change_date.strftime('%Y/%m/%d'))
        else
          body(txt: @clinical_transfer.status_change_date.strftime('%d/%m/%Y'))
        end

        title(txt: "#{I18n.t('clinical_transfers_report.transfer_referential_number') }:")
        body(txt: "#{@clinical_transfer.ref_number}")
      end

      move_down(0)
      title(txt: "#{I18n.t('clinical_transfers_report.transfer_title') }:")
      transfer_title = "#{UnicodeUtils.safe_upcase(@clinical_transfer.title)}"
      transfer_title.slice!(-1) if transfer_title[-1] == "."
      body(txt: transfer_title)

      title(txt: "#{I18n.t('clinical_transfers_report.costs_title')}:")
      body(txt: "#{I18n.t('clinical_transfers_report.costs_body')}")

      move_down(10)

      text "#{I18n.t('clinical_transfers_report.transfer_creator') }", style: :bold, size: 11
      move_down(2)
      horizontal_line 0, 524
      stroke
      move_down(10)

      title(txt: "#{I18n.t('clinical_transfers_report.name') }:")
      body(txt: "#{UnicodeUtils.safe_upcase @clinical_transfer.project.company_name}")

      title(txt: "#{I18n.t('clinical_transfers_report.transfer_creator_address') }:")
      if @clinical_transfer.project.company_street.present? and @clinical_transfer.project.company_zip_code.present? and @clinical_transfer.project.company_city.present?
        string = "#{UnicodeUtils.safe_upcase @clinical_transfer.project.company_street}, "+
        "#{UnicodeUtils.safe_upcase @clinical_transfer.project.company_zip_code} "+
        "#{UnicodeUtils.safe_upcase @clinical_transfer.project.company_city} "
      else
        string = ""
      end

      body(txt: string)

      title(txt: "#{I18n.t('clinical_transfers_report.creator_bank_acc_nr') }:")
      body(txt: "PL#{ ClinicalTransfer.format_account_number(LmpSetting.elixir_sender_nrb_nr) }")

      title(txt: "#{I18n.t('clinical_transfers_report.project_code') }:")
      body(txt: "#{UnicodeUtils.safe_upcase @clinical_transfer.project.clinical_protocol_code}")

      title(txt: "#{I18n.t('clinical_transfers_report.project_sponsor') }:")
      body(txt: "#{UnicodeUtils.safe_upcase @clinical_transfer.project.name}")

      bounding_box([0,60], width: 540) do
        if @clinical_transfer.master?
          text "#{I18n.t('clinical_transfers_report.master_transfer_info')}", inline_format: true
        else
          text "#{I18n.t('clinical_transfers_report.normal_transfer_info')}", inline_format: true
        end
      end


      start_new_page
      header

      text "REIMBURSED VISITS", style: :bold, size: 18
      move_down(-1)
      text "STUDY PROTOCOL: #{UnicodeUtils.safe_upcase @clinical_transfer.project.clinical_protocol_code}", style: :bold, color: "666666", size: 15

      move_down(20)

      grouped_ct = if @clinical_transfer.master?
        ClinicalTransfer.where(master_transfer_id: @clinical_transfer.id)
      else
        [@clinical_transfer]
      end
      visits = Visit.where(id: grouped_ct.map(&:visit_id)).sort_by { |v| [v.patient_code || '123', v.visit_date || Date.today] }

      data = [["ID", "Order number", "Subject#", "Visit date", "Visit name", "Amount", "Balance"]]
      balance = 0.0

      visits.each_with_index do |visit, index|
        balance += visit.amount.to_f
        amount = "#{("%.2f" % visit.amount).gsub(/\./,'.')}"
        balance_s = "#{("%.2f" % balance).gsub(/\./,'.')}"
        data += [[index +1, @clinical_transfer.ref_number, visit.clinical_user.patient_code, "#{I18n.l visit.visit_date, format: :only_date_slash}", visit.name&.truncate(30), amount, balance_s]]
      end

      table data, header: true, width: 530 do |table|
        table.column(1).style :width => 80
        table.column(0..4).style :align => :center
        table.column(5..6).style :align => :right
        table.row(0).style :align => :center, :valign => :center, border_widths: [0, 0, 1, 0]
        table.row(0).column(5..6).style :align => :right
        table.row(0).font_style = :bold
        table.row(1..99999).style height: 21, border_width: 0
      end

      move_down(40)

      balance_s = "#{("%.2f" % balance).gsub(/\./,'.')}"
      text "Total amount: #{balance_s} #{ @clinical_transfer.currency }", size: 12, style: :bold, align: :right, inline_format: true
      move_down(1)
      text "Execution date: #{I18n.l @clinical_transfer.status_change_date, format: :only_date_slash}", size: 12, align: :right, inline_format: true

      move_down(10)


      footer
    end
  end


  private

  def title(txt:)
    text txt, style: :bold
    move_down(1)
  end

  def header
    text "Let Me Pay Sp. z o.o.", size: 10, align: :right, style: :bold
    text 'Iłżecka 26, 02-135 Warsaw', size: 10, align: :right

    bounding_box([0, cursor + 30], width: 400) do
      image logopath, :width => 170
    end

    move_down(30)
  end

  def footer
    left_options = { at: [bounds.left, 0], size: 10,  width: 300, align: :left, start_count_at: 1 }
    number_pages "#{I18n.t('clinical_transfers_report.footer_left', date: "#{I18n.l Time.current, format: :local_date_dash_at_cest_slash}") }", left_options

    right_options = { at: [bounds.right - 250, 0], size: 10,  width: 250, align: :right, start_count_at: 1 }
    number_pages "#{I18n.t('clinical_transfers_report.footer_right')}", right_options
  end

  def body(txt:, inline_format: false)
    if txt.blank?
      txt = "#{I18n.t('clinical_transfers_report.confidential')}"
    end

    text txt, inline_format: inline_format, size: 13
    move_down(10)
  end

  def logopath
    "#{Rails.root}/public/pc-logo-dark.png"
  end
end
