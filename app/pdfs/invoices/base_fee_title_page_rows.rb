module Invoices::BaseFeeTitlePageRows
  def base_new_clinical_user_fees_first_page_row
    [
      "#{I18n.t('invoice_pdf.new_patient_acc_activation')} #{according_to_attachment('new_patient_accounts_fees')}",
      formatted_number(report.new_patient_accounts_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_clinical_user_maintenance_fees_first_page_row
    text = if report.project.premium_plan?
      I18n.t('invoice_pdf.summary_page.premium_patient_acc_maintenance', fee_plan: report.project.fee_plan_name)
    else
      I18n.t('invoice_pdf.patient_acc_maintenance')
    end

    [
      "#{text} #{according_to_attachment('new_and_active_patient_accounts')}",
      formatted_number(report.new_and_active_patient_accounts_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_post_transfer_fees_first_page_row
    [
      "#{I18n.t('invoice_pdf.sending_post_transfers')} #{according_to_attachment('post_transfer')}",
      formatted_number(report.post_transfer_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_transfer_fees_first_page_row
    [
      "#{I18n.t('invoice_pdf.sending_bank_transfers')} #{according_to_attachment('transfer')}",
      formatted_number(report.transfer_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_data_entry_fees_first_page_row
    [
      "#{I18n.t('invoice_pdf.data_entry_fees_first_page_title')} #{according_to_attachment('data_entry')}",
      formatted_number(report.data_entry_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_project_plan_fees_first_page_row
    title = if report.project.plan_fee_type == 'pay_per_patient'
      I18n.t('invoice_pdf.summary_page.project_plan_fee_per_patient', fee_plan: report.project.fee_plan_name)
    else
      I18n.t('invoice_pdf.summary_page.project_plan_fee', fee_plan: report.project.fee_plan_name)
    end

    [
      "#{title} #{according_to_attachment('project_plan')}",
      formatted_number(report.project_plan_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_site_fees_first_page_row
    [
      "#{I18n.t('invoice_pdf.summary_page.site_fees', fee_plan: report.project.fee_plan_name)} #{according_to_attachment('site_fees')}",
      formatted_number(report.site_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_project_startup_fees_first_page_row
    [
      "#{I18n.t('invoice_pdf.summary_page.project_startup_fees')} #{according_to_attachment('project_startup_fees')}",
      formatted_number(report.project_startup_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end

  def base_min_project_patients_fees_first_page_row
    [
      "#{I18n.t('invoice_pdf.summary_page.min_project_patients_fees')} #{according_to_attachment('min_project_patients_fees')}",
      formatted_number(report.min_project_patients_fees_total_without_vat),
      formatted_number(@first_page_fee_balance),
      project.invoice_currency
    ]
  end
end
