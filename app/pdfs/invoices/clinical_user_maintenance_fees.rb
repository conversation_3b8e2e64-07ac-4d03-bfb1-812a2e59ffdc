module Invoices::ClinicalUserMaintenanceFees
  def clinical_user_maintenance_fees_page
    start_new_page
    attachment_title(attachment_symbol: 'new_and_active_patient_accounts', title: I18n.t('project_invoice_xls.cu_acc_fees'))
    text "#{I18n.t('invoice_pdf.nr_of_active_acc')}: #{report.active_patients.size}"
    text "#{I18n.t('invoice_pdf.nr_of_inactive_acc')}: #{report.inactive_patients.size}"
    text "#{I18n.t('invoice_pdf.nr_of_all_acc')}: #{report.all_patients_count}"
    move_down 30
    text I18n.t('invoice_pdf.patient_acc_maintenance_summary')
    move_down 10

    table clinical_user_maintenance_fees_rows, width: bounds.width do |table|
      table.row(0).style valign: :center, align: :center
      table.row(1..-1).column(0..2).style align: :center, valign: :center
      table.row(1..-1).column(3..-1).style align: :right, valign: :center
      no_border_pdf_table(table)
    end

    move_down 30
    active_acc_nr = report.active_patients_fees.group_by(&:clinical_user_id).size
    conj_text = active_acc_nr == 1 ? I18n.t('invoice_pdf.one_patient_acc') : I18n.t('invoice_pdf.many_patient_acc', new_acc_nr: active_acc_nr)

    fee_section_summary(action: "#{I18n.t('invoice_pdf.maintenance')} #{conj_text}", fee_total_method: :active_patient_accounts_fees_total, singular: true)

    move_down 10
    text report.active_patient_accounts_fees_total_summary_row, style: :bold

    foreign_currency_fee_section(:active_patient_accounts_fees_total)
  end

  def clinical_user_maintenance_fees_rows
    [report.active_patients_fees_header] +
      report.active_patients_fees.group_by(&:clinical_user_id).map do |_cu, fees|
        report.fee_row(fees)
      end
  end

  def clinical_user_maintenance_fees_first_page_row
    fees_first_page_row(
      base_row: base_clinical_user_maintenance_fees_first_page_row,
      total_with_vat: report.new_and_active_patient_accounts_fees_total_with_vat,
      total_vat: report.new_and_active_patient_accounts_fees_total_vat
    )
  end
end
