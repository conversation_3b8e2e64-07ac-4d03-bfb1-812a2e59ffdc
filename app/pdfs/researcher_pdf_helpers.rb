module ResearcherPdfHelpers
  include ActionView::Helpers::DateHelper
  include <PERSON><PERSON>el<PERSON>

  def visits_count_and_total(visits)
    return unless visits.present?

    "#{ visits.count }\n <font size='#{ Reports::BaseReport::SM_FONT_SIZE }'>#{ number_to_currency(visits.sum(:amount), unit: visits.first.try(:currency)) }</font>"
  end

  def acc_nr_and_swift(object:)
    "#{ pl_acc_nr(acc_nr: object.pir_account_number) }\n <font size='#{Reports::BaseReport::SM_FONT_SIZE }'>Swift: #{ object.swift_code }</font>"
  end

  def last_activity_time(object:)
    object.last_activity_date ? "#{time_ago_in_words(object.last_activity_date, time_ago_in_words: false, locale: :en_short)}" : 'None'
  end
end
