class ClinicalUsersTransfersConfirmationsPdf < Reports::BaseReport
  attr_reader :locale

  def initialize(params = {})
    @clinical_transfers = params[:clinical_transfers]
    @locale = params.fetch :locale, 'pl'
    super({ page_size: 'A4', bottom_margin: 21 })
    pdf_content
    font_families.update("Source_Sans_Pro" => {
      :normal => "#{Rails.root}/lib/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf",
      :bold => "#{Rails.root}/lib/fonts/Source_Sans_Pro/SourceSansPro-SemiBold.ttf",
      })

    font "Source_Sans_Pro", size: 11
  end

  def pdf_content
    clinical_users_transfers.each do |clinical_user, clinical_transfers|
      clinical_user_section(clinical_user, clinical_transfers)
    end
  end

  def clinical_users_transfers
    @clinical_transfers.group_by { |ct| ct.clinical_user }
  end

  def clinical_user_section(clinical_user, clinical_transfers)
    ct_size = clinical_transfers.size
    clinical_transfers.each.with_index do |ct, i|
      clinical_transfer_confirmation(ct, new_page: (i + 1) < ct_size)
    end
  end

  def clinical_transfer_confirmation(transfer, new_page: true)
    ClinicalTransferConfirmationSectionPdf.new(self, clinical_transfer: transfer, locale: locale).render
  end
end
