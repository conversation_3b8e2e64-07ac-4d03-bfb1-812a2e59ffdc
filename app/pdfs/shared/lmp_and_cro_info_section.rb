class Shared::LmpAndCroInfoSection < SimpleDelegator
  def initialize(prawn, cro:, cro_coords: [320, 600])
    super(prawn)
    @cro = cro
    @cro_coords = cro_coords
  end

  def render
    lmp_info_box
    move_down(20)
    note_company_info_box
  end

  def lmp_info_box
    text 'Let Me Pay Sp. z o.o.'
    text 'Wiśniowy Business Park'
    text I18n.t('project_debit_summary_pdf.street')
    text I18n.t('project_debit_summary_pdf.nip')
  end

  def note_company_info_box
    bounding_box(@cro_coords, width: 200) do
      move_down(85)
      text @cro.company_name.to_s, style: :bold
      text @cro.company_street.to_s, style: :bold
      text "#{@cro.company_zip_code} #{@cro.company_city}", style: :bold
      if @cro.company_nip.present?
        text "#{I18n.t('project_debit_summary_pdf.client_nip')}: #{@cro.company_nip}", style: :bold
      end
      move_down(10)
    end
  end
end