class PaymentStatusReport

  attr_accessor :clinical_centers, :padding, :options


  def initialize(clinical_centers=[], options = {})

    self.tap {|report|
      report.clinical_centers = [*clinical_centers]
      report.padding = 15
      report.options = options
      yield report if block_given?
    }
  end

  def all_memberships
    clinical_centers.map{|cc| cc.memberships.reported}.flatten

  end

  def clinical_centers=(cc)
    @clinical_centers = [*cc]
  end

  def clinical_center_uniq_visits_names(project_visit_t= nil)
    types = project_visit_t ? project_visit_t.visit_types : clinical_centers.collect(&:visit_types).flatten.uniq
    types.sort_by(&:position).map {|vt| [vt.id, vt.name]}
  end

  def categories
    categories = self.clinical_center_uniq_visits_names(options[:project_visit_template]).map {|x| x[1]}
    categories = categories + Array.new(15-categories.length) {''} if categories.length < 10
  end

  def months_range
    minmax = all_memberships.minmax_by &:created_at

    first_day = minmax[0].created_at.to_date.beginning_of_month rescue DateTime.now.to_date.beginning_of_month
    last_day = minmax[1].created_at.to_date.beginning_of_month  rescue DateTime.now.to_date.beginning_of_month
    months_range = [*first_day..last_day].select {|d| d.day == 1}



    if months_range.length < padding
      size = padding - months_range.length
      months_range.push *Array.new(size) {|i| last_day+(i+1).months}
    else
      months_range = months_range[-padding..-1]
    end
    months_range
  end


  def series
    hashes = self.clinical_center_uniq_visits_names(options[:project_visit_template]).map {|x| x[0]}
    series = [
        {
            name: I18n.t('custom.planned_visits'),
            data: self.with_status(['p'], clinical_centers, hashes),
            color: '#FACA33'
        },
        {
            name: I18n.t('custom.not_paid_done_visits'),
            data: self.with_status(['w','v'],  clinical_centers, hashes),
            color: '#F07156'
        },
        {
            name: I18n.t('custom.done_paid_visits'),
            data: self.with_status(['s'],  clinical_centers, hashes),
            color: '#A1D562'
        },
        {
            name: I18n.t('custom.unplanned_visits'),
            data: self.with_status(['u'],  clinical_centers, hashes),
            color: '#eee',
            borderColor: '#ccc'
        }].reverse!
    series
  end


  def title
    "#{I18n.t('dashboard.index.payments_status_in_center_all')} #{"- #{options[:project_visit_template][:name]}" if options[:project_visit_template]}"
  end


  def chart
    LazyHighCharts::HighChart.new('graph') do |f|
      f.title(text: title)
      f.xAxis(:categories => categories)
      f.yAxis(title: {text:  I18n.t('dashboard.index.patients_percent')})
      f.legend({:layout => "horizontal", margin: 30})
      series.each do |s|
        f.series(s)
      end
      f.chart({:defaultSeriesType=>"column"})
      f.options[:plotOptions] = {
        column: {
          stacking: 'normal'
        }
      }
      f.options[:colors] = %w(#1aadce #492970 #f28f43 #77a1e5 #c42525 #a6c96a #2f7ed8 #0d233a #8bbc21 #910000 )
    end
  end





  def subtitle
    users = @cc.collect(&:clinical_users).flatten.uniq
    template = opts[:project_visit_template]
    users = users.select {|u|
      u.project_visit_template == template
    } if template

    if single?
      "#{single[:name]} (#{single[:clinical_center_code] if single[:clinical_center_code]}) #{"- #{I18n.t('dashboard.all_clinical_centers.chart.n_members', count: users.count )}" if users}"
    else
      "#{I18n.t('dashboard.all_clinical_centers.chart.subtitle')}  #{"- #{I18n.t('dashboard.all_clinical_centers.chart.n_members', count: users.count )}" if users}"
    end
  end



  def with_status(status, clinical_center, hashes)
    cc = [*clinical_center]
    t = {}
    hashes.each { |vh| t[vh] = [] }
    p  t
    hashes.each do |vh|
      s = 0
      users = cc.collect(&:clinical_users).flatten.uniq
      users = users.select {|u| u.project_visit_template == options[:project_visit_template]} if options[:project_visit_template]
      users.each { |u|
        s += u.visits.where('state in (?)', status).where(visit_type_id: vh).count
      }
      t[vh] = s
    end
    val = t.values
    val.length < 10 ? t.values + Array.new(15-t.values.length) {0} : val
  end

end