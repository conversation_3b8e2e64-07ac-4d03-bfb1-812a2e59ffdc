module FeeReports::ProjectPlanFees
  def project_plan_fees
    @project_plan_fees ||= all_fees.select { |f| f.type_name == Fee::TYPE_NAMES['Premium project'] }
  end

  def project_plan_header
    [
      I18n.t('invoice_pdf.description'),
      I18n.t('bulk_invoice_pdf.headers.amount'),
      I18n.t('bulk_invoice_pdf.headers.balance'),
      ''
    ]
  end

  def project_plan_row(fee, total)
    [
      project_plan_row_title(fee),
      formatted_number(fee.amount),
      formatted_number(total),
      fee.currency
    ]
  end

  def project_plan_fees_total
    project_plan_fees.inject(0) { |sum, n| sum + (n.try(:amount) || 0) }.round(2)
  end

  def project_plan_fees_total_without_vat
    convert_to_pln project_plan_fees_total
  end

  def project_plan_fees_total_vat
    (project_plan_fees_total_without_vat * vat_percentage).round(2)
  end

  def project_plan_fees_total_with_vat
    project_plan_fees_total_without_vat + project_plan_fees_total_vat
  end

  def project_plan_fees_total_summary_row
    "#{I18n.t('bulk_invoice_pdf.total_amount')} #{formatted_number(convert_to_pln(project_plan_fees_total))} #{ project.invoice_currency }"
  end

  def project_plan_row_title(fee)
    title = if fee.project.plan_fee_type == 'pay_per_patient'
      patients_count = Fees::PremiumProjects::GetPatientsCount.call(fee: fee)

      patients_string = if patients_count > 1
        I18n.t('invoice_pdf.patients_count', count: patients_count)
      else
        I18n.t('invoice_pdf.one_patient_count')
      end

      I18n.t('invoice_pdf.project_plan_fee_per_patient_desc', fee_plan: fee.translated_plan_name, patients: patients_string)
    else
      I18n.t('invoice_pdf.project_plan_fee_desc', fee_plan: fee.translated_plan_name)
    end
  end
end
