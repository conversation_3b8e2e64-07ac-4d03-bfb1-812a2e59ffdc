class SubjectSurveys::BaseController < ApplicationController
  layout 'home'

  before_action :set_locale

  def set_locale
    I18n.locale = :pl
  end

  def authentice_survey_user
    return if user_authenticated_for_survey?

    redirect_to new_subject_surveys_session_path, notice: 'Proszę sie zalogowac.'
  end

  def user_authenticated_for_survey?
    session[:user_for_survey_id].present? || clinical_user_signed_in?
  end
end
