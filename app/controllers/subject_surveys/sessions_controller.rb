class SubjectSurveys::<PERSON><PERSON><PERSON>roller < SubjectSurveys::BaseController
  before_action :ensure_user_not_authorized

  def new
    @form = Surveys::LoginForm.new(params[:surveys_login_form])
  end

  def create
    @form = Surveys::LoginForm.new(params[:surveys_login_form])

    if resend? || (@form.clinical_user && @form.code.blank?)
      @form.send_code_to_patient

      redirect_to(new_subject_surveys_session_path(surveys_login_form: params[:surveys_login_form]), notice: 'Kod SMS został wysłany na numer Pana telefonu. Proszę wpisać otrzymany kod, aby u<PERSON>ska<PERSON> dostęp do formularza.') and return
    end

    if @form.save
      session[:user_for_survey_id] = @form.clinical_user.id

      redirect_to new_subject_surveys_survey_response_path
    else
      render :new
    end
  end

  private

  def resend?
    params[:resend] && @form.clinical_user
  end

  def ensure_user_not_authorized
    if user_authenticated_for_survey?
      redirect_to new_subject_surveys_survey_response_path
    end
  end
end