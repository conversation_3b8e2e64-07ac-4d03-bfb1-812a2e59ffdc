class SitePayments::ProjectSummariesController < SitePayments::Base<PERSON>ontroll<PERSON>
  def show
    @project = current_researcher.site_payments_projects.find(params[:project_id])

    authorize @project, :access_site_payments_projects_summaries?

    @clinical_centers = if current_researcher.sp_admin_at_site_in_project?(@project)
      current_researcher.sp_admin_sites
    else
      current_researcher.site_payments_clinical_centers
    end

    @clinical_centers = @clinical_centers
      .where(project: @project)
      .order(clinical_center_code: :asc)
      .distinct
  end
end