class V2::Sponsor::Visits::ClinicalUserAccountActivitiesController < ApplicationController
  before_action :authenticate_researcher!

  def index
    @visit = Visit.find(params[:visit_id])
    authorize @visit, :show?

    @activities = ClinicalUserAccountActivity
      .where(id: @visit.activities)
      .includes(:source, :researcher, :clinical_user)
      .order('created_at desc')
      .page(params[:page])
  end
end
