class V2::Sponsor::Projects::ShipmentsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :set_project

  def index
    @shipments = @project
      .shipments
      .ransack(params[:q])
      .result
      .order('delivered_on desc')
      .page(params[:page])
  end

  def new
    authorize @project, :add_shipment?
    @shipment = Shipment.new(delivered_on: Date.today.strftime('%d-%m-%Y'))
    @shipment.user_action_requirements.build
    @clinical_center = ClinicalCenter.find_by_id(params[:clinical_center_id])
    @clinical_users = @clinical_center ? @clinical_center.clinical_users.order('patient_code asc') : @project.clinical_users.order('patient_code asc')
    respond_to :html, :js
  end

  def show
    authorize @project, :show_shipment?
    @shipment = Shipment.find(params[:id])
    @clinical_users = @clinical_center ? @clinical_center.clinical_users.order('patient_code asc') : @project.clinical_users.order('patient_code asc')
  end

  def create
    authorize @project, :add_shipment?
    @shipment = Shipment.new(shipment_params)
    @shipment.researcher_id = current_researcher.id

    case @shipment.shipment_type
    when 'normal'
      @shipment.sender_email = nil
    when 'email'
      @shipment.courier = nil
    end

    if @shipment.save
      redirect_to v2_sponsor_project_shipments_path(@project), notice: 'Shipment has been added.'
    else
      @shipment.user_action_requirements.build
      @clinical_center = @shipment.clinical_center
      @clinical_users = @clinical_center ? @clinical_center.clinical_users.order('patient_code asc') : @project.clinical_users.order('patient_code asc')
      render :new
    end
  end

  def edit
    @shipment = @project.shipments.find(params[:id])
    authorize @shipment, :edit?
    @clinical_users = @project.clinical_users.order('patient_code asc')
  end

  def update
    @shipment = @project.shipments.find(params[:id])
    authorize @shipment, :edit?

    if @shipment.update(shipment_params)
      redirect_to v2_sponsor_project_shipments_path(@project), notice: 'Shipment has been updated.'
    else
      render :edit
    end
  end

  private

  def set_project
    @project = current_researcher.projects.find(params[:project_id])
  end

  def shipment_params
    params[:shipment].permit(
      :delivered_on,
      :shipment_type,
      :courier,
      :tracking_number,
      :sender_email,
      :clinical_center_id,
      :patient_codes,
      :patient_codes_for_receipts,
      user_action_requirements_attributes: %i[
        reason
        project_id
        clinical_user_id
      ]
    )
  end
end
