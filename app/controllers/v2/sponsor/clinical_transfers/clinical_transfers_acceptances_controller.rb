

class V2::Sponsor::ClinicalTransfers::ClinicalTransfersAcceptancesController < ApplicationController
  include ClinicalTransfers::Acceptance::AddFundsRedirects

  before_action :authenticate_researcher!

  def create
    @clinical_transfer = ClinicalTransfer.find params[:clinical_transfer_id]
    @project = @clinical_transfer.project # potrzebne do autoryzacji
    @visit = @clinical_transfer.visit

    if @project.allow_visit_payments_with_costs_above_limits && @visit && @visit.any_costs_over_limit? && current_researcher.role_in_project(@project)&.project_role != 'Manager'
      categories_over_limit = @visit.cost_categories_over_limit.pluck(:name_en)
      text = categories_over_limit.many? ? "\"#{categories_over_limit.join(', ')}\" categories exceed" : "\"#{categories_over_limit.join(', ')}\" category exceeds"
      redirect_back_with_default alert: "The cost for the #{text} the limit. Please contact the manager to approve the payment."
      return if performed?
    end

    authorize @clinical_transfer, :accept?

    redirect_to_add_funds_if_not_enough_funds_for_transfer_acceptance; return if performed?

    form = ClinicalTransfers::AcceptForm.new(transfers: [@clinical_transfer], researcher: current_researcher)

    if form.save
      redirect_back_with_default notice: flash_msg_for
    else
      redirect_back_with_default alert: form.errors.full_messages.to_sentence
    end
  end
end
