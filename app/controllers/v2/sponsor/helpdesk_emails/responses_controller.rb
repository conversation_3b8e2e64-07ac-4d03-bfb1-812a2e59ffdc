class V2::Sponsor::HelpdeskEmails::ResponsesController < ApplicationController
  before_action :authenticate_researcher!
  before_action :set_email
  before_action :authorize_email

  def new
    @response = HelpdeskResponse::FromResearcher.new(helpdesk_email: @email)
    @response.subject = @response.default_subject
    @response.correspondence_locale = @email.researcher&.correspondence_locale
    set_helpdesk_cc_emails
  end

  def create
    @response = HelpdeskResponse::FromResearcher.new(params[:helpdesk_response_from_researcher])
    @response.helpdesk_email = @email
    @response.researcher = current_researcher

    if @response.save
      redirect_to v2_sponsor_helpdesk_email_path(@email), notice: 'Response has been sent.'
    else
      set_helpdesk_cc_emails
      render :new
    end
  end

  private

  def set_email
    @email = HelpdeskEmail.find(params[:helpdesk_email_id])
  end

  def authorize_email
    authorize @email, :reply?
  end
end