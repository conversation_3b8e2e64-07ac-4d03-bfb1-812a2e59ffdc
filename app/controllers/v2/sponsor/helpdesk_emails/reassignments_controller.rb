class V2::Sponsor::HelpdeskEmails::ReassignmentsController < ApplicationController
  include HelpdeskEmails::Assignable

  before_action :authenticate_researcher!
  before_action :authorize_operator

  def new
    get_email
    authorize @email, :update?, policy_class: HelpdeskEmailPolicy
    set_data_for_he_assignment
  end

  def create
    @email = HelpdeskEmail::ForCheckIn.find(params[:helpdesk_email_id])

    authorize @email, :update?, policy_class: HelpdeskEmailPolicy

    @email.assign_attributes(params[:helpdesk_email])
    @email.set_tag

    if @email.save
      redirect_to v2_sponsor_helpdesk_emails_path
    else
      redirect_back_with_default alert: @email.errors.full_messages.to_sentence
    end
  end

  private

  def get_email
    @email = HelpdeskEmail.find(params[:helpdesk_email_id])
  end
end