module V2
  module Sponsor
    module PayclinicalEmployees
      class SearchesController < BaseController
        def new; end

        def show
          search_term = params[:search_term]
          researchers = Researcher.where('phone_number ILIKE ?', "%#{search_term}%")
          clinical_users = ClinicalUser.where('phone_number ILIKE ?', "%#{search_term}%")
          @results = Kaminari.paginate_array([researchers, clinical_users].flatten).page(params[:page])
          render :new
        end
      end
    end
  end
end
