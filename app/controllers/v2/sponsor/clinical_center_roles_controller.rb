class V2::Sponsor::ClinicalCenterRolesController < ApplicationController
  before_action :authenticate_researcher!

  def destroy
    ccr = ClinicalCenterRole.find(params[:id])
    authorize ccr
    begin
      researcher_name = ccr.researcher.try(:full_name)
      ccr.destroy!
      redirect_to v2_sponsor_project_researchers_path(ccr.project), notice: flash_msg_for(message_vars: { researcher_name: researcher_name })
    rescue => e
      ExceptionNotifier.notify_exception(e, data: { message: "<PERSON>ład przy usuwaniu roli badacza w osrodku, ccr_id #{ ccr.id }, badacz #{ current_researcher.id }" })
      redirect_to v2_sponsor_project_researchers_path(ccr.project), alert: flash_msg_for(action_result_type: 'fail')
    end
  end
end
