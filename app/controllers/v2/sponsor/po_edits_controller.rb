class V2::Sponsor::PoEditsController < ApplicationController
  before_action :authenticate_researcher!

  def new
    @pds = ProjectDebitSummaryPolicy::Scope.new(current_researcher, ProjectDebitSummary).resolve
    @pds = @pds.find { |pds| pds.id.to_s == params[:project_debit_summary_id] }
  end

  def update
    @pds = ProjectDebitSummaryPolicy::Scope.new(current_researcher, ProjectDebitSummary).resolve
    @pds = @pds.find { |pds| pds.id.to_s == params[:project_debit_summary_id] }
    if @pds.update(note_params) and @pds.regenerate
      redirect_to v2_sponsor_project_debit_summaries_path, notice: "The debit note #{ @pds.note_number } has been revised."
    else
      flash.now[:alert] = "The debit note cannot be revised."
      render :new, alert: @pds.errors.full_messages.to_sentence
    end
  end

  private

  def note_params
    params[:project_debit_summary].permit(:comment)
  end
end
