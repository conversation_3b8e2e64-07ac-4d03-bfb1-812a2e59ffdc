

class V2::Sponsor::ClinicalCenters::CommentsController < ApplicationController
  before_action :authenticate_researcher!

  def create
    @clinical_center = current_researcher.clinical_centers.find params[:clinical_center_id]
    @comment = @clinical_center.comments.new(comment_params)
    @comment.added_by = current_researcher

    if @comment.save
      redirect_back_with_default notice: 'Comment has been added.'
    else
      msg = @comment.errors.full_messages.to_sentence.downcase
      msg[0] = msg[0].upcase
      redirect_back_with_default alert: msg
    end
  end

  private

  def comment_params
    params[:comment].permit(:body)
  end
end
