

class V2::Sponsor::CallsAndMeetingsController < ApplicationController
  before_action :authenticate_researcher!
  before_action :authorize_show, only: %i[index show send_cm_email_form send_cm_email agenda minutes]
  before_action :authorize_update, only: %i[edit update]
  before_action :authorize_actions, only: %i[new create destroy set_status]
  before_action :set_call_meeting, only: %i[show edit update destroy
                                            send_cm_email_form send_cm_email set_status
                                            agenda minutes]

  def index
    if policy(:calls_and_meetings).supermanager_access?
      @calls_meetings = CallsAndMeeting.where(contract_research_organization_id: current_researcher.super_manager_cro.id)
                                       .order('date_and_time DESC').page(params[:page])
    else
      @calls_meetings = CallsAndMeeting.includes(:contract_research_organization)
                                       .order('date_and_time DESC').page(params[:page])
    end
  end

  def show; end

  def new
    @call_meeting = CallsAndMeeting.new
    @call_meeting.cme_type = params[:cme_type].in?(CallsAndMeeting::CME_TYPES) ? params[:cme_type] : 'Call'
  end

  def create
    @call_meeting = CallsAndMeeting.new(cm_params)

    if @call_meeting.save
      if params[:commit] == 'Next'
        @attendees_and_receivers_only = true
        set_attendees_from_previous_cm(@call_meeting)
        set_receivers
        render :edit
      else
        redirect_to v2_sponsor_calls_and_meetings_path
      end
    else
      render :new
    end
  end

  def edit
    set_receivers
  end

  def update
    if @call_meeting.update(cm_params)
      redirect_to v2_sponsor_calls_and_meeting_path(@call_meeting)
    else
      set_receivers
      render :edit
    end
  end

  def destroy
    @call_meeting.destroy
    redirect_back_with_default notice: "#{@call_meeting.cme_type} was removed."
  end

  # Custom member actions

  def send_cm_email_form
    if params[:field].in?(CallsAndMeeting::EMAIL_FIELDS) && params[:redirect_to].present?
      @form = SendCallMeetingEmailForm.new(
        call_meeting: @call_meeting,
        field: params[:field],
        redirect_to: params[:redirect_to],
        main_receiver: @call_meeting.main_receiver,
        receivers: @call_meeting.receivers
      )
    else
      redirect_back_with_default alert: 'Taken action was not correct.'
    end
  end

  def send_cm_email
    @form = SendCallMeetingEmailForm.new(
      params[:send_call_meeting_email_form].merge(call_meeting: @call_meeting)
    )

    if @form.save
      redirect_to @form.redirect_to
    else
      render :send_cm_email_form
    end
  end

  def set_status
    if params[:status]&.in?(CallsAndMeeting.aasm.states.map(&:name).map(&:to_s))
      @call_meeting.update_attribute(:status, params[:status])
      redirect_back_with_default
    else
      redirect_back_with_default alert: 'Taken action was not correct.'
    end
  end

  def agenda
    pdf = CallsAndMeetings::AgendaPdf.new(@call_meeting)

    send_data pdf.render, filename: "Agenda.pdf", type: 'application/pdf', disposition: 'inline'
  end

  def minutes
    pdf = CallsAndMeetings::MinutesPdf.new(@call_meeting)

    send_data pdf.render, filename: "Minutes.pdf", type: 'application/pdf', disposition: 'inline'
  end

  private

  def cm_params
    if policy(:calls_and_meetings).supermanager_access?
      params[:calls_and_meeting].permit(:agenda, :meeting_minutes, :time_part, :date_part)
    else
      params[:calls_and_meeting].permit(
        :agenda, :meeting_minutes, :contract_research_organization_id, :cme_type,
        :title, :time_part, :date_part, :receivers, :main_receiver, :attendees)
    end
  end

  def set_call_meeting
    @call_meeting = CallsAndMeeting.find(params[:id])
  end

  def set_attendees_from_previous_cm(call_meeting)
    last_cro_cm = call_meeting.contract_research_organization
                              .calls_and_meetings.where('date_and_time < ?', call_meeting.date_and_time)
                              .order(:date_and_time).last
    @attendees_from_previous_cm = last_cro_cm.present? ? last_cro_cm.attendees : ''
  end

  def set_receivers
    operator_ids      = @call_meeting.contract_research_organization
                                     .project_roles.operators.pluck(:researcher_id).uniq
    operators         = Researcher.where(id: operator_ids)
    cro_supermanagers = @call_meeting.contract_research_organization.super_managers

    @receivers = (operators + cro_supermanagers).sort_by(&:last_name)
  end

  def authorize_show
    authorize :calls_and_meetings, :show?
  end

  def authorize_update
    authorize :calls_and_meetings, :update?
  end

  def authorize_actions
    authorize :calls_and_meetings, :actions?
  end
end
