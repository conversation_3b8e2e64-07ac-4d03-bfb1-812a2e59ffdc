class V2::Sponsor::AccountUnlocksController < ApplicationController

  def new
    @form = ResearcherUnlockForm.new(params)
    render 'new', layout: 'home'
  end

  def create
    @form = ResearcherUnlockForm.new(params[:researcher_unlock_form])
    params[:token] = @form.token

    if @form.captcha_valid?
      if params[:token].blank?
        redirect_to root_path, alert: flash_msg_for(action_result_type: 'lock_token_missing') and return
      end

      @researcher = Researcher.find_by_unlock_token(params[:token])
      redirect_to(root_path, alert: 'Your request cannot be completed. Please contact <NAME_EMAIL>.') and return unless @researcher

      if @researcher.unlock_token == params[:token]
        @researcher.unlock_account
        redirect_to root_path, notice: flash_msg_for
      else
        redirect_to root_path, alert: flash_msg_for(action_result_type: 'fail')
      end

    else
      redirect_to root_path, alert: 'You have been identified as a robot. <NAME_EMAIL> if you are not a robot and if you need our assistance.'
    end
  end
end
