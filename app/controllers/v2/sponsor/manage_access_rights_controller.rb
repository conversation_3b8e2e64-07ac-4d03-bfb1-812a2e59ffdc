class V2::Sponsor::ManageAccessRightsController < ApplicationController
  before_action :authenticate_researcher!

  def new
    authorize current_researcher, :manage_access_rights?
    session[:redirect_to_after_res_role_toggle] = new_v2_sponsor_manage_access_rights_path
  end

  def show
    authorize current_researcher, :manage_access_rights?
    session[:redirect_to_after_res_role_toggle] = new_v2_sponsor_manage_access_rights_path

    @query = params[:researcher_info]
    base_query = if current_researcher.payclinical_employee
                   Researcher
                 else
                   current_researcher.cro_role_projects_researchers
                 end

    @researchers = base_query.not_blocked.where("
            first_name like :query OR
            last_name like :query OR
            email like :query
          ", { query: "%#{ @query }%" }).order("last_name, first_name").
          page(params[:page])

    render 'new'
  end
end
