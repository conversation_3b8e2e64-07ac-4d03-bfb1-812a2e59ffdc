

class V2::Sponsor::TosVersionsController < ApplicationController
  # before_action :authenticate_researcher!

  def show
    tos_version = TosVersion.find(params[:id])
    # pdf = TODO
    send_data pdf.render, filename: 'tos.pdf', type: 'application/pdf', disposition: 'inline'
  end

  def current
    current_tos = TosVersion.latest_version

    if current_tos
      tos_pdf = current_tos.try(:tos_pdf)
      send_file(tos_pdf.file.path,
                disposition: 'inline',
                filename: "Declaration_of_consent_for_processing_of_personal_data-ver.#{current_tos.version_number}.pdf",
                url_based_filename: false)
    else
      redirect_back_with_default
    end
  end
end
