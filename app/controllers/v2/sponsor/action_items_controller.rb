class V2::Sponsor::ActionItemsController < V2::Sponsor::ActionItems::BaseController
  def index
    @scope = params[:scope]
    @action_items = ActionItem
      .for_researcher(current_researcher)
      .order_by_id
      .includes(:added_by, :resource)

    if params[:project_id].present?
      @resource = current_researcher.projects.find(params[:project_id])
      @action_items = @action_items
        .where('
          resource_type = :site AND resource_id IN (:site_ids)
          OR
          resource_type = :patients AND resource_id IN (:patients_ids)
        ', {
          site: 'ClinicalCenter',
          site_ids: ClinicalCenter.where(project_id: params[:project_id]).ids,
          patients: 'ClinicalUser',
          patients_ids: ClinicalUser.where(project_id: params[:project_id]).ids
        })
    end

    if params[:clinical_center_id].present?
      @resource = current_researcher.clinical_centers.find(params[:clinical_center_id])
      @action_items = @action_items
        .where('
          resource_type = :patient AND resource_id IN (:patient_ids)
          OR
          resource_type = :site AND resource_id = :site_id
        ', { patient: 'ClinicalUser', patient_ids: ClinicalUser.where(clinical_center_id: params[:clinical_center_id]).ids, site: 'ClinicalCenter', site_id: params[:clinical_center_id] })
    end

    if valid_scope_present?
      @action_items = @action_items.send(@scope)
    end

    if params[:show_individual_items].present?
      @action_items = @action_items
        .page(params[:page])
    end
  end

  def show
    @action_item = ActionItem
      .for_researcher(current_researcher)
      .find(params[:id])
    @responses = @action_item.responses.order_by_id
    @action_item.register_visit(visitor: current_researcher)
    @action_item.update!(status: 'pending') if @action_item.status == 'new' && !current_researcher.payclinical_employee
  end

  def new
    @action_item = ActionItem::Advanced.new(params[:action_item_advanced])
    @action_item.deadline ||= 2.weeks.from_now.to_date

    set_data_for_new
  end

  def create
    @action_item = ActionItem::Advanced.new(action_item_params)
    @action_item.added_by = current_researcher
    @action_item.deadline ||= 2.weeks.from_now

    if @action_item.save
      redirect_to v2_sponsor_action_items_path, notice: "Action item #{ @action_item.number } has been added."
    else
      set_data_for_new
      render :new
    end
  end

  private

  def action_item_params
    params[:action_item_advanced].permit(
      :cro_id,
      :project_id,
      :resource_type,
      :resource_id,
      :resource_type,
      :description,
      :target_researcher_id,
      :deadline,
      escalation_researchers_attributes: [
        :researcher_id
      ]
    )
  end

  def valid_scope_present?
    @scope && ['pending', 'lvl_1_escalation', 'lvl_2_escalation', 'completed', 'finishing_pending', 'finishing_lvl_1_escalation', 'finishing_lvl_2_escalation', 'not_resolved'].include?(@scope)
  end
end
