

class V2::Sponsor::ProjectsController < ApplicationController
  before_action :authenticate_researcher!
  helper_method :sort_column, :sort_direction
  before_action :load_data_for_create, only: %i[new create]

  def index
    set_index_data

    if params[:as] == 'pdf'
      pdf = ResearcherProjectsPdf.new(current_projects: @current_projects, completed_projects: @completed_projects)
      send_data pdf.render, filename: 'projects.pdf', type: 'application/pdf', disposition: 'inline'
    end
  end

  def show
    @project = Project.find(params[:id])
    authorize @project

    if @project.multi_currency?
      redirect_to(v2_sponsor_multi_currency_projects_path(code: @project.clinical_protocol_code)) && return
    end

    @clinical_centers = @project.clinical_centers.order('clinical_center_code asc').includes([:primary_cra])
    @user_action_requirements = @project.user_action_requirements
  end

  def new
    authorize @project
  end

  def create
    authorize @project
    @project.assign_attributes(project_params)
    @project.accounting_type = 'v'
    @project.include_main_payment_categories = true
    @project.active_account_number = 'cro'
    if @project.save
      redirect_to v2_sponsor_project_clinical_centers_path(@project, new_project: true), notice: flash_msg_for
    else
      render :new
    end
  end

  def update
    @project = Project.find(params[:id])
    authorize @project
    params[:project].select { |_k, v| v == 'N/A' }.each { |k, _v| params[:project][k] = 0 }
    if @project.update(project_params)
      redirect_to v2_sponsor_project_path(@project)
    else
      redirect_back_with_default alert: @project.errors.full_messages.to_sentence
    end
  end

  private

  def sort_column
    Project.column_names.include?(params[:sort]) ? params[:sort] : 'clinical_protocol_code'
  end

  def sort_direction
    %w[asc desc].include?(params[:direction]) ?  params[:direction] : 'asc'
  end

  def load_data_for_create
    @cro = current_researcher.managed_cro
    @project = Project.new({
                             contract_research_organization_id: @cro.try(:id),
                             debit_allowed: @cro.debit_allowed_prefered,
                             investigator_transfer_limit: @cro.investigator_limit,
                             cra_transfer_limit: @cro.cra_limit,
                             manager_transfer_limit: @cro.manager_limit
                           })
  end

  def set_index_data
    projects = current_researcher.projects.order("#{sort_column} #{sort_direction}").distinct
    @project_count = GetResearcherProjectCount.call(researcher: current_researcher)
    @current_projects = projects.select { |p| !p.closed && !p.test_project }
    @current_projects << projects.select { |p| !p.closed && p.test_project }
    @current_projects = @current_projects.flatten.uniq
    # completed project only for managers
    @completed_projects = projects.select { |p| p.closed && current_researcher.role_in_project(p).manager? && !p.test_project }

    researcher_cros = Researchers::CrosWithRoles.run(researcher: current_researcher)
    @best_researcher_cro = researcher_cros.first

    @best_researchers = if researcher_cros.size == 1
      @best_researcher_cro.cro_best_researchers.for_previous_month
    else
      CroBestResearcher.none
    end
  end

  def project_params
    params[:project].permit(
      :debit_allowed,
      :clinical_protocol_code,
      :name,
      :investigator_transfer_limit,
      :cra_transfer_limit,
      :manager_transfer_limit,
      :transfer_limit
    )
  end
end
