class V2::Sponsor::ZadarmaEventsController < ApplicationController
  before_action :authenticate_researcher!
  include HelpdeskEmails::Assignable

  before_action :authorize_researcher

  def index
    @scope = params[:scope] || 'new_or_pending'
    allowed_scopes = %w[resolved new_or_pending]

    @events = if @scope != 'all' && allowed_scopes.include?(@scope)
                ZadarmaEvent.send(@scope).order_by_id
              else
                ZadarmaEvent.all.order_by_id
              end

    @events = @events.not_internal.call_starts.page(params[:page])
  end

  def edit
    set_event
    set_data_for_he_assignment
  end

  def update
    set_event
    @event.checked_in_at ||= Time.zone.now
    final_params = update_params
    final_params[:status] = 'pending' if @event.status == 'new'

    if @event.update(final_params)
      redirect_to v2_sponsor_zadarma_events_path, notice: 'Event has been updated.'
    else
      set_data_for_he_assignment
      render :edit
    end
  end

  def close
    set_event

    if @event.update(status: 'closed')
      redirect_to v2_sponsor_zadarma_events_path, notice: 'Event has been updated.'
    else
      redirect_to v2_sponsor_zadarma_events_path, alert: @event.errors.full_messages.to_sentence
    end
  end

  def reopen
    set_event

    if @event.update(status: 'pending')
      redirect_to v2_sponsor_zadarma_events_path, notice: 'Event has been updated.'
    else
      redirect_to v2_sponsor_zadarma_events_path, alert: @event.errors.full_messages.to_sentence
    end
  end

  def callback_completed
    set_event
    if @event.update(called_back_by: current_researcher, called_back_at: Time.zone.now)
      redirect_to v2_sponsor_zadarma_events_path, notice: 'Event has been updated.'
    else
      redirect_to v2_sponsor_zadarma_events_path, alert: @event.errors.full_messages.to_sentence
    end
  end

  private

  def authorize_researcher
    authorize current_researcher, :helpdesk_operator?
  end

  def set_event
    @event = ZadarmaEvent::ForCheckIn.find(params[:id])
    @email = @event
  end

  def update_params
    params[:zadarma_event_for_check_in].permit(
      :contract_research_organization_id,
      :project_id,
      :operator_id,
      :call_description,
      :without_project
    )
  end
end
