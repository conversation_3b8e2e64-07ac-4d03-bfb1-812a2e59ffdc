class V2::Sponsor::SimpleActionItemsController < V2::Sponsor::ActionItems::BaseController
  def new
    @action_item = ActionItem::Simple.new(form_params)
    @action_item.owner ||= ActionItems::FindDefaultOwner.call(action_item: @action_item)
    @action_item.deadline ||= 2.weeks.from_now.to_date
    set_texts
    session[:return_to_after_ai_create] = request.referrer
  end

  def create
    @action_item = ActionItem::Simple.new(form_params)
    @action_item.added_by = current_researcher
    @action_item.deadline ||= 2.weeks.from_now

    if @action_item.save
      redirect_to session[:return_to_after_ai_create] || v2_sponsor_action_items_path, notice: "Action item #{ @action_item.number } has been added."
    else
      puts @action_item.errors.full_messages
      set_texts
      render :new
    end
  end

  private

  def set_texts
    @templates = ActionItem::Text.for_ai(action_item: @action_item)
  end

  def form_params
    params[:action_item_simple].permit(
      :for_site_payments,
      :cro_name,
      :project_code,
      :context,
      :owner,
      :description,
      :deadline,
      :locale,
      :resource_id,
      :resource_type,
      files: []
    )
  end
end