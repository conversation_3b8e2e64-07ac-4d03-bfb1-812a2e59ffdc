# == Schema Information
#
# Table name: cro_domains
#
#  id                                :integer          not null, primary key
#  name                              :string(255)
#  created_at                        :datetime         not null
#  updated_at                        :datetime         not null
#  contract_research_organization_id :integer
#
# Indexes
#
#  index_cro_domains_on_contract_research_organization_id  (contract_research_organization_id)
#
class CroDomain < ApplicationRecord
  #attr_accessible :contract_research_organization_id, :name

  belongs_to :contract_research_organization
end
