class Fee < ApplicationRecord
  acts_as_paranoid

  STATES = {
    'Charged' => 'c',
    'Paid' => 'p'
  }.freeze

  TYPE_NAMES = {
    'Researcher' => 'r', # New researcher added
    'Patient' => 'p', # New patient added
    'Active patient' => 'a', # Active patient
    'Clinical Transfer' => 't', # For clinical transfer
    'Post Transfer' => 'm', # For transfering money by post
    'Premium project' => 'q',
    'Reactivate patient' => 'z',
    'Project start_up_fee' => 's',
    'citi_transfer_fee' => 'c',
    'min_project_patients' => 'x',
    'site_fee' => 'w'
  }.freeze

  belongs_to :project
  has_one :contract_research_organization, through: :project
  belongs_to :clinical_user
  def clinical_user # Override to get with_deleted association
    ClinicalUser.with_deleted.find(clinical_user_id) if clinical_user_id
  end
  belongs_to :researcher
  belongs_to :admin_user
  belongs_to :clinical_transfer
  belongs_to :clinical_center
  belongs_to :fee_plan

  validates :project, presence: true
  validates :state, inclusion: { in: STATES.values }
  validates :type_name, inclusion: { in: TYPE_NAMES.values }
  validate :cant_charge_closed_project

  scope :for_new_patients, -> { where(type_name: TYPE_NAMES['Patient']) }
  scope :for_active_patients, -> { where(type_name: TYPE_NAMES['Active patient']) }
  scope :for_patients, -> { where(type_name: [TYPE_NAMES['Patient'], TYPE_NAMES['Active patient']]) }
  scope :for_transfers, -> { where(type_name: TYPE_NAMES['Clinical Transfer']) }
  scope :for_booked_transfers, -> { joins(:clinical_transfer).where(clinical_transfers: { state: [ClinicalTransfer::SENT_TO_BANK, ClinicalTransfer::BOOKED, ClinicalTransfer::GROUPED] }) }
  scope :for_post_transfers, -> { where(type_name: TYPE_NAMES['Post Transfer']) }
  scope :for_researchers, -> { where(type_name: TYPE_NAMES['Researcher']) }
  scope :for_premium_projects, -> { where(type_name: TYPE_NAMES['Premium project']) }
  scope :for_patient_reactivation, -> { where(type_name: TYPE_NAMES['Reactivate patient']) }
  scope :for_new_patients_and_reactivations, -> { where(type_name: [TYPE_NAMES['Reactivate patient'], TYPE_NAMES['Patient']]) }
  scope :for_site, -> { where(type_name: TYPE_NAMES['site_fee']) }
  scope :citi_transfer_fee, -> { where(type_name: TYPE_NAMES['citi_transfer_fee']) }
  scope :for_data_entry, -> { citi_transfer_fee }
  scope :for_min_project_patients, -> { where(type_name: TYPE_NAMES['min_project_patients']) }

  delegate :clinical_protocol_code,
           :invoice_currency,
           :fee_plan_name,
           to: :project, allow_nil: true

  delegate :citi_created_at, to: :clinical_transfer, allow_nil: true

  before_create :set_currency, if: :project_id, unless: :currency
  after_create :delete, if: proc { |f| f.project.test_project || f.amount == 0 }

  def vattable?
    type_name != 't'
  end

  def for_premium_project?
    self.type_name == TYPE_NAMES['Premium project']
  end

  def clinical_center_code
    clinical_center&.clinical_center_code || clinical_user&.clinical_center_code || clinical_transfer&.clinical_center_code
  end

  def patient_code
    clinical_user&.patient_code || clinical_transfer&.patient_code
  end

  class << self
    def charge_citi_transfer_fee(clinical_transfer)
      Fee.create!(
        type_name: TYPE_NAMES['citi_transfer_fee'],
        amount: clinical_transfer.citi_transfer_fee,
        state: STATES['Charged'],
        project_id: clinical_transfer.get_project.try(:id),
        clinical_transfer_id: clinical_transfer.id
      )
    end

    def charge_new_project!(project)
      fee = Fee.new(
        project_id: project.id,
        state: STATES['Charged'],
        amount: project.start_up_fee,
        type_name: TYPE_NAMES['Project start_up_fee']
      )
      fee.save!
    end

    def charge_new_researcher!(project, researcher, admin_user)
      fee = Fee.new(
        project_id: project.id,
        researcher_id: researcher.id,
        admin_user_id: admin_user.id,
        state: STATES['Charged'],
        amount: project.fee_researcher_add.nil? ? 0 : project.fee_researcher_add,
        type_name: TYPE_NAMES['Researcher']
      )
      fee.save!
    end

    def charge_new_patient!(project, clinical_user, admin_user, researcher, charge_for_this_month: true)
      puts "charge_new_patient - id #{clinical_user.id}"
      if Fee.where(project_id: project.id, clinical_user_id: clinical_user.id, state: STATES['Charged'], type_name: TYPE_NAMES['Patient']).exists?
        puts 'Fee exists - skipping.'
        return false
      end

      h = {}
      h[:admin_user_id] = admin_user.id if admin_user
      h[:researcher_id] = researcher.id if researcher
      fee = Fee.new({
        project_id: project.id,
        clinical_user_id: clinical_user.id,
        state: STATES['Charged'],
        amount: project.fee_patient_add.nil? ? 0 : project.fee_patient_add,
        type_name: TYPE_NAMES['Patient'],
        clinical_center_id: clinical_user.clinical_center_id
      }.merge(h))
      if clinical_user.data_confirmed_at
        fee.created_at = clinical_user.data_confirmed_at
      end
      fee.save!
      if charge_for_this_month
        Fee.monthly_charge_uncharged_patients
        if ClinicalUser.chargable.where(id: clinical_user.id).exists? && clinical_user.data_confirmed_at
          Fee.charge_for_months_till_current(clinical_user: clinical_user)
        end
      end
    end

    def charge_for_months_till_current(clinical_user:, start_date: clinical_user.data_confirmed_at)
      dates = [start_date]
      loop do
        next_month = (dates.last + 1.month).beginning_of_month.to_date
        break if next_month > Date.today.beginning_of_month.to_date

        dates << next_month
      end
      dates.each do |date|
        unless clinical_user.fees.for_active_patients.where(created_at: date.to_time.all_month).exists?
          Fee.charge_active_patient!(clinical_user, created_at: date)
        end
      end
    end

    def monthly_charge_uncharged_patients
      ClinicalUser.chargable.each do |clinical_user|
        unless Fee.active_patient_charged_in_this_month?(clinical_user)
          puts "#{Time.new.month} - patient: #{clinical_user.id} - #{clinical_user.patient_code}"
          Fee.charge_active_patient!(clinical_user)
        end
      end
    end

    def charge_active_patient!(clinical_user, created_at: nil)
      project = clinical_user.project
      fee = Fee.new(
        clinical_user_id: clinical_user.id,
        project_id: project.id,
        state: STATES['Charged'],
        amount: project.fee_active_patient.nil? ? 0 : project.fee_active_patient,
        type_name: TYPE_NAMES['Active patient'],
        clinical_center_id: clinical_user.clinical_center_id
      )
      fee.created_at = created_at if created_at
      fee.save!
    end

    def charge_for_patient_reactivation!(clinical_user, created_at: nil)
      project = clinical_user.project
      fee = Fee.new(
        clinical_user_id: clinical_user.id,
        project_id: project.id,
        state: STATES['Charged'],
        amount: project.get_patient_reactivation_fee || 0,
        type_name: TYPE_NAMES['Reactivate patient'],
        clinical_center_id: clinical_user.clinical_center_id
      )
      fee.created_at = created_at if created_at
      fee.save!
    end

    def active_patient_charged_in_this_month?(clinical_user)
      last_charged_fee = clinical_user.fees.where(type_name: TYPE_NAMES['Active patient'])
                                      .order(:created_at).last
      if last_charged_fee && (last_charged_fee.created_at.month == Time.new.month)
        true
      else
        false
      end
    end
  end

  def cant_charge_closed_project
    if project&.closed && project&.closed_at && ((created_at || Time.new) > project.closed_at.end_of_month)
      errors.add(:project_id, 'Closed project cannot be charged.')
    end
  end

  def set_currency
    self.currency = project.try(:fee_currency)
  end

  def translated_plan_name
    fee_plan&.title
  end
end
