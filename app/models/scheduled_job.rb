# == Schema Information
#
# Table name: scheduled_jobs
#
#  id          :integer          not null, primary key
#  backtrace   :text
#  command     :string(255)
#  error       :text
#  finished_at :datetime
#  started_at  :datetime
#  state       :string(255)      default("pending")
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
class ScheduledJob < ApplicationRecord
  extend Enumerize

  enumerize :state, in: %i[pending success failed], default: :pending

  scope :pending, -> { where(state: 'pending') }
  scope :unstarted, -> { where(started_at: nil) }
  scope :gen_citi, -> { where('command like ?', 'GenerateCitiJob%') }
end
