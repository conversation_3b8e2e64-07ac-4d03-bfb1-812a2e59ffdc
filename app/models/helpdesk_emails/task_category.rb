class HelpdeskEmails::TaskCategory < ApplicationRecord
  validates :name, presence: true, uniqueness: true

  has_many :helpdesk_task_category_reply_templates
  has_many :reply_templates, through: :helpdesk_task_category_reply_templates, class_name: 'HelpdeskReplyTemplate'

  default_scope { order(name: :asc) }

  def email_subject_for(helpdesk_email, status_change_name)
    send("#{status_change_name}_email_subject")&.gsub('{{manual_response}}', helpdesk_email.manual_status_change_response || '')&.rstrip
  end

  def email_body_for(helpdesk_email, status_change_name)
    send("#{status_change_name}_email_body")&.gsub('{{manual_response}}', helpdesk_email.manual_status_change_response || '')&.rstrip
  end

  def sms_body_for(helpdesk_email, status_change_name)
    send("#{status_change_name}_sms_body")&.gsub('{{manual_response}}', helpdesk_email.manual_status_change_response || '')&.rstrip
  end
end
