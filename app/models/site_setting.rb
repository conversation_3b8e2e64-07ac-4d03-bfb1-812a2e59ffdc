class SiteSetting < ApplicationRecord
  VALID_HE_TEMPLATE_PLACEHOLDERS = ['receipt_id']
  HELPDESK_EMAILS_TEMPLATE_FIELDS = [
    :helpdesk_email_manager_response_email_subject,
    :helpdesk_email_manager_response_email_body,
    :helpdesk_email_manager_response_sms_body,
    :helpdesk_email_cra_response_email_subject,
    :helpdesk_email_cra_response_email_body,
    :helpdesk_email_cra_response_sms_body,
    :helpdesk_email_investigator_response_email_subject,
    :helpdesk_email_investigator_response_email_body,
    :helpdesk_email_investigator_response_sms_body,
    :helpdesk_email_not_registered_response_email_subject,
    :helpdesk_email_not_registered_response_email_body,
    :helpdesk_email_not_registered_response_sms_body
  ]

  validate :valid_he_autoresponse_fields

  class << self
    def instance
      first!
    end

    def helpdesk_email_autorespond_sms_body(helpdesk_email)
      format_helpdesk_message(helpdesk_email, "helpdesk_email_#{helpdesk_email.response_template_name}_response_sms_body")
    end

    def helpdesk_email_autorespond_email_subject(helpdesk_email)
      format_helpdesk_message(helpdesk_email, "helpdesk_email_#{helpdesk_email.response_template_name}_response_email_subject")
    end

    def helpdesk_email_autorespond_email_body(helpdesk_email)
      format_helpdesk_message(helpdesk_email, "helpdesk_email_#{helpdesk_email.response_template_name}_response_email_body")
    end

    def format_helpdesk_message(helpdesk_email, message_field_name)
      template = instance.send(message_field_name)

      return unless template.present?

      template.gsub('{{receipt_id}}', helpdesk_email.receipt_id.to_s)
    end

    def helpdesk_email_response_cc
      instance.helpdesk_email_response_cc
    end

    def investigator_info
      instance.investigator_info
    end

    def cra_managers_info
      instance.cra_managers_info
    end

    def vip_investigator_info
      instance.vip_investigator_info
    end
  end

  private

  def valid_he_autoresponse_fields
    HELPDESK_EMAILS_TEMPLATE_FIELDS.each { valid_he_autoresponse_of(_1) }
  end

  def valid_he_autoresponse_of(field)
    field_value = send(field)
    return if field_value.blank?

    placeholders = field_value.scan(/\{\{(\w+)\}\}/).flatten

    invalid_placeholders = placeholders - VALID_HE_TEMPLATE_PLACEHOLDERS

    if invalid_placeholders.any?
      errors.add(field, "contains invalid placeholders: #{invalid_placeholders.join(', ')}, valid values are: #{VALID_HE_TEMPLATE_PLACEHOLDERS}")
    end
  end
end
