class VisitPaymentCategory < ApplicationRecord
  has_translation :name

  belongs_to :project
  has_many :visit_payment_categorizations
  has_many :visits, through: :visit_payment_categorizations
  has_many :copied_visit_payment_categories, class_name: 'VisitPaymentCategory', foreign_key: 'base_visit_payment_category_id'
  belongs_to :base_visit_payment_category, class_name: 'VisitPaymentCategory'
  belongs_to :owner, polymorphic: true
  has_many :cost_limits

  accepts_nested_attributes_for :cost_limits

  # validate :is_main_project_id_nil
  validates :name_en, :name_pl, presence: true
  validates :abbr, length: { maximum: 4 }, presence: true

  before_destroy :can_be_deleted?
  after_create :update_all_cro_vpc, unless: proc { |v| v.is_copy? || v.owner_id.present? }
  # after_create :update_all_projects_vpc, unless: Proc.new { |v| v.is_copy? or v.belongs_to_project? }
  after_create :create_related_vpc_in_projects_belonging_to_owner, if: proc { |v| v.owner_is_cro? }
  after_update :update_child_categories

  scope :main, -> { where(is_main: true) }
  scope :not_main, -> { where(is_main: false) }
  scope :visible, -> { where(visible: true) }
  scope :copies, -> { where('base_visit_payment_category_id is not null') }
  default_scope -> { order('position') }
  scope :km, -> { where(abbr: 'K') }

  ENGINES = {
    above_900: 0.8358
  }.freeze

  def used_in_project?
    self.visits.exists?
  end

  def belongs_to_project?
    self.owner&.is_a?(Project)
  end

  def owner_is_cro?
    self.owner&.is_a?(ContractResearchOrganization)
  end

  def is_copy?
    base_visit_payment_category_id.present?
  end

  def is_main_project_id_nil
    if (self.is_main == true) && !self.project_id.nil? && self.base_visit_payment_category.nil?
      errors.add(:project_id,
                 'Projekt musi być pusty, jesli kategoria platności jest główna (is main)')
    end
  end

  def can_be_deleted?
    false
  end

  def update_all_cro_vpc
    transaction do
      ContractResearchOrganization.all.each do |cro|
        next if cro.has_copy_of_visit_payment_category?(self)

        vpc = cro.visit_payment_categories.create!(is_main: false, name_en: self.name_en, abbr: self.abbr,
                                                   name_pl: self.name_pl, project_id: nil, position: self.position, visible: self.visible,
                                                   base_visit_payment_category_id: self.id)
        vpc.create_related_vpc_in_projects_belonging_to_owner
      end
    end
end

  def update_all_projects_vpc
    transaction do
      Project.all.each do |project|
        next if project.has_copy_of_visit_payment_category?(self)

        project.visit_payment_categories.create!(is_main: false, name_en: self.name_en, abbr: self.abbr,
                                                 name_pl: self.name_pl, project_id: nil, position: self.position, visible: self.visible,
                                                 base_visit_payment_category_id: self.id)
      end
    end
  end

  def update_child_categories
    attrs = self.attributes.extract!('name_pl', 'name_en', 'abbr', 'position', 'visible')
    VisitPaymentCategory.transaction do
      copied_visit_payment_categories.each do |copy|
        copy.assign_attributes attrs
        copy.save!
      end
    end
  end

  def create_related_vpc_in_projects_belonging_to_owner
    transaction do
      return unless self.owner.is_a? ContractResearchOrganization

      self.owner.projects.all.each do |project|
        next if project.has_copy_of_visit_payment_category?(self)

        project.visit_payment_categories.create!(is_main: false, name_en: self.name_en, abbr: self.abbr,
                                                 name_pl: self.name_pl, project_id: nil, position: self.position, visible: self.visible,
                                                 base_visit_payment_category_id: self.id)
      end
    end
  end

  def km_category?
    [abbr, base_visit_payment_category.try(:abbr)].include? 'K'
  end

  def cost_limit_in_project(project:)
    project.limit_for_cost(visit_payment_category: self)
  end

  def copies_and_base_vpc_ids
    result = []
    result << self.id
    result << self.base_visit_payment_category_id
    if self.base_visit_payment_category
      result << self.base_visit_payment_category.copied_visit_payment_categories.pluck(:id)
    end
    result.flatten.compact.uniq
  end
end
