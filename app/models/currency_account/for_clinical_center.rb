# == Schema Information
#
# Table name: currency_accounts
#
#  id                     :bigint           not null, primary key
#  account_number         :string
#  account_number_type    :string           default("mpt")
#  balance                :decimal(30, 2)   default(0.0)
#  clinical_protocol_code :string
#  currency               :string           default("PLN")
#  resource_type          :string
#  swift                  :string
#  type                   :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  resource_id            :bigint
#
# Indexes
#
#  index_currency_accounts_on_id_and_type                    (id,type)
#  index_currency_accounts_on_resource_type_and_resource_id  (resource_type,resource_id)
#
class CurrencyAccount::ForClinicalCenter < CurrencyAccount
  delegate :cro,
    to: :resource, allow_nil: true

  def account_type_symbol
    '4'
  end
end
