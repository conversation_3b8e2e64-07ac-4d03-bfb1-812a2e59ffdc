# -*- encoding : utf-8 -*-
class ApplicationRecord < ActiveRecord::Base
  self.abstract_class = true

  scope :order_by_id, -> { order('id desc') }
  scope :random_order, -> { order("RANDOM()") }

  # after_rollback :log_and_email_validation_errors, unless: 'valid?'

  def log_and_email_validation_errors
    logger.info 'BŁĄD WALIDACJI'
    logger.info Time.now
    logger.info "#{ self.class } - #{ self.id }"
    logger.info errors.full_messages
  end

  def self.none
    where("1 = 0")
  end

  def self.created_today
    where(created_at: Time.current.all_day)
  end

  def admin_signed_in?
    AdminUser.current.present?
  end

  def translated_class_name
    I18n.t("activerecord.models.#{self.class.name.underscore}", locale: :pl)
  end

  def changesets
    if respond_to?(:versions)
      versions.map(&:changeset)
    end
  end

  def show_action_items?
    project_id == 9
  end

  def self.ransackable_attributes(auth_object = nil)
    authorizable_ransackable_attributes
  end
  def self.ransackable_associations(auth_object = nil)
    authorizable_ransackable_associations
  end
end
