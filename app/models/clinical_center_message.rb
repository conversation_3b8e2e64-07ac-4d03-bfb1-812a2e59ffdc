# == Schema Information
#
# Table name: clinical_center_messages
#
#  id                     :integer          not null, primary key
#  cc_to_researcher_ids   :string(255)
#  deleted_at             :datetime
#  message                :text             not null
#  send_to_researcher_ids :string(255)
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  clinical_center_id     :integer          not null
#  clinical_user_id       :integer
#  researcher_id          :integer
#  visit_id               :integer
#
# Indexes
#
#  index_clinical_center_messages_on_clinical_center_id  (clinical_center_id)
#  index_clinical_center_messages_on_clinical_user_id    (clinical_user_id)
#  index_clinical_center_messages_on_deleted_at          (deleted_at)
#  index_clinical_center_messages_on_researcher_id       (researcher_id)
#  index_clinical_center_messages_on_visit_id            (visit_id)
#
class ClinicalCenterMessage < ApplicationRecord
  acts_as_paranoid
  acts_as_readable :on => :updated_at

  belongs_to :researcher
  belongs_to :clinical_center
  belongs_to :clinical_user
  belongs_to :visit

  validates :clinical_center_id, :message, presence: true

  serialize :send_to_researcher_ids
  serialize :cc_to_researcher_ids

  def send_email
    ResearcherMailer.with(clinical_center_message: self).send_cc_message.deliver
  end
end

