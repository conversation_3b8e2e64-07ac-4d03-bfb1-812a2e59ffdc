# == Schema Information
#
# Table name: calls_and_meetings
#
#  id                                :integer          not null, primary key
#  agenda                            :text
#  agenda_sent_at                    :datetime
#  attendees                         :string(255)
#  cme_type                          :string(255)
#  date_and_time                     :datetime
#  deleted_at                        :datetime
#  main_receiver                     :string(255)
#  meeting_minutes                   :text
#  meeting_minutes_sent_at           :datetime
#  receivers                         :string(255)
#  status                            :string(255)
#  title                             :string(255)
#  created_at                        :datetime         not null
#  updated_at                        :datetime         not null
#  contract_research_organization_id :integer
#  reference_id                      :string(255)
#
# Indexes
#
#  index_calls_and_meetings_on_contract_research_organization_id  (contract_research_organization_id)
#  index_calls_and_meetings_on_date_and_time                      (date_and_time)
#  index_calls_and_meetings_on_deleted_at                         (deleted_at)
#  index_calls_and_meetings_on_reference_id                       (reference_id)
#
# Foreign Keys
#
#  calls_and_meetings_contract_research_organization_id_fk  (contract_research_organization_id => contract_research_organizations.id)
#
class CallsAndMeeting < ApplicationRecord
  acts_as_paranoid

  attr_accessor :date_part, :time_part

  CME_TYPES = ["Call", "Meeting"]
  EMAIL_FIELDS = ['agenda', 'meeting_minutes']
  VALID_EMAIL_REGEX = /[\w\-.]+@[a-z\d\-]+\.[a-z]+/i

  belongs_to :contract_research_organization

  before_validation :set_datetime_from_parts
  before_save :format_receivers

  validates :contract_research_organization_id, presence: { message: "Please select CRO." }
  validates :title, presence: { message: "Please enter title." }
  validates :date_and_time, presence: { message: 'Please set date and time.' }
  validates :cme_type, inclusion: { in: CME_TYPES, message: "Please select type." }


  include AASM
  aasm column: :status do
    state :planned, initial: true
    state :cancelled
    state :done

    event :set_done do
      transitions from: :planned, to: :done
    end

    event :set_cancelled do
      transitions from: :planned, to: :cancelled
    end

    event :set_planned do
      transitions from: :done, to: :planned
    end
  end

  def owner_name
    Researcher.find_by(email: main_receiver)&.full_name
  end


  private

  def set_datetime_from_parts
    return if date_part.blank? && time_part.blank?

    self.date_and_time = Time.zone.parse(date_part + ' ' + time_part)
  end

  def format_receivers
    return unless receivers.present?

    self.receivers = receivers.scan(VALID_EMAIL_REGEX).to_a.join(', ')
  end
end
