module Country
  LIST_HASH = {
    'Austria' => { shortname: 'AT', currency: 'PLN', tier: 1 },
    'Belgium' => { shortname: 'BE', currency: 'PLN', tier: 1 },
    'Estonia' => { shortname: 'EE', currency: 'PLN', tier: 1 },
    'Finland' => { shortname: 'FI', currency: 'PLN', tier: 1 },
    'France' => { shortname: 'FR', currency: 'PLN', tier: 1 },
    'Germany' => { shortname: 'DE', currency: 'PLN', tier: 1 },
    'Greece' => { shortname: 'GR', currency: 'PLN', tier: 1 },
    'Ireland' => { shortname: 'IE', currency: 'PLN', tier: 1 },
    'Italy' => { shortname: 'IT', currency: 'PLN', tier: 1 },
    'Latvia' => { shortname: 'LV', currency: 'PLN', tier: 1 },
    'Lithuania' => { shortname: 'LT', currency: 'PLN', tier: 1 },
    'Netherlands' => { shortname: 'NL', currency: 'PLN', tier: 1 },
    'Poland' => { shortname: 'PL', currency: 'PLN', tier: 1 },
    'Portugal' => { shortname: 'PT', currency: 'PLN', tier: 1 },
    'Slovakia' => { shortname: 'SK', currency: 'PLN', tier: 1 },
    'Slovenia' => { shortname: 'SI', currency: 'PLN', tier: 1 },
    'Spain' => { shortname: 'ES', currency: 'PLN', tier: 1 },
    'Luxembourg' => { shortname: 'LU', currency: 'PLN', tier: 2 },
    'Cyprus' => { shortname: 'CY', currency: 'PLN', tier: 2 },
    'Malta' => { shortname: 'MT', currency: 'PLN', tier: 2 },
    'Serbia' => { shorname: 'RS', currency: 'PLN', tier: 2 },
    'USA' => { shorname: 'US', currency: 'USD', tier: 2 },
    'Israel' => { shorname: 'IL', currency: 'ILS', tier: 2 },
    'Bulgaria' => { shortname: 'BG', currency: 'PLN', tier: 2 },
    'Croatia' => { shortname: 'HR', currency: 'PLN', tier: 2 },
    'Czech Republic' => { shortname: 'CZ', currency: 'PLN', tier: 2 },
    'Denmark' => { shortname: 'DK', currency: 'PLN', tier: 2 },
    'Romania' => { shortname: 'RO', currency: 'PLN', tier: 2 },
    'Hungary' => { shortname: 'HU', currency: 'PLN', tier: 2 },
    'Sweden' => { shortname: 'SE', currency: 'PLN', tier: 2 },
    'United Kingdom' => { shortname: 'GB', currency: 'PLN', tier: 2 },
    'Ukraine' => { shortname: 'UA', currency: 'UAH', tier: 2 }
  }.freeze

  def self.shortname(country_name)
    return unless country_name

    LIST_HASH[country_name][:shortname]
  end

  def self.icon_path(country_name)
    return unless country_name

    "country_icons/#{country_name.downcase}_64.png"
  end

  def self.name_and_currency(country_name)
    return country_name unless LIST_HASH[country_name]

    "#{country_name} - #{LIST_HASH[country_name][:currency]}"
  end

  def self.tier(country_name)
    return unless country_name

    LIST_HASH[country_name][:tier]
  end
end
