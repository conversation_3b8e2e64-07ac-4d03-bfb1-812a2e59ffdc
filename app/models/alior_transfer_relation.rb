# == Schema Information
#
# Table name: alior_transfer_relations
#
#  id                   :bigint           not null, primary key
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  alior_file_id        :bigint
#  clinical_transfer_id :bigint
#
# Indexes
#
#  index_alior_transfer_relations_on_alior_file_id         (alior_file_id)
#  index_alior_transfer_relations_on_clinical_transfer_id  (clinical_transfer_id)
#
# Foreign Keys
#
#  fk_rails_...  (alior_file_id => alior_files.id)
#  fk_rails_...  (clinical_transfer_id => clinical_transfers.id)
#
class AliorTransferRelation < ApplicationRecord
  belongs_to :clinical_transfer, optional: false
  belongs_to :alior_file, optional: false
end
