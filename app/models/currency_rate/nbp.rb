# == Schema Information
#
# Table name: currency_rates
#
#  id                     :integer          not null, primary key
#  base_currency          :string(255)
#  comments               :text
#  nbp                    :boolean          default(FALSE)
#  rate                   :decimal(, )
#  rate_source            :string(255)
#  target_currency        :string(255)
#  type                   :string           default("CurrencyRate::Nbp")
#  valid_from             :date
#  valid_to               :date
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  converted_from_rate_id :bigint
#
# Indexes
#
#  index_currency_rates_on_converted_from_rate_id  (converted_from_rate_id)
#  index_currency_rates_on_id_and_type             (id,type)
#
# Foreign Keys
#
#  fk_rails_...  (converted_from_rate_id => currency_rates.id)
#
class CurrencyRate::Nbp < CurrencyRate
end
