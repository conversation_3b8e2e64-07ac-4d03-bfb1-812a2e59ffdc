# == Schema Information
#
# Table name: clinical_user_forms
#
#  id               :integer          not null, primary key
#  deleted_at       :datetime
#  form             :string(255)
#  state            :string           default("new")
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  clinical_user_id :integer
#  researcher_id    :integer
#
# Indexes
#
#  index_clinical_user_forms_on_clinical_user_id  (clinical_user_id)
#  index_clinical_user_forms_on_deleted_at        (deleted_at)
#  index_clinical_user_forms_on_researcher_id     (researcher_id)
#
class ClinicalUserForm < ApplicationRecord
  extend Enumerize

  acts_as_paranoid

  mount_base64_uploader :form, ClinicalUserFormUploader # TODO: remove
  has_one_attached :file

  belongs_to :researcher
  belongs_to :clinical_user
  has_many :helpdesk_emails
  has_many :shipments, through: :helpdesk_emails
  has_one :project, through: :clinical_user
  has_one :contract_research_organization, through: :project

  validates :clinical_user_id, :file, presence: true

  delegate :patient_code,
    :clinical_protocol_code,
    :clinical_center_code,
    to: :clinical_user, allow_nil: true

  enumerize :state, in: [:new, :processed], default: :new

  def get_name
    clinical_user.project.try(:clinical_protocol_code) + '_' + clinical_user.clinical_center.try(:clinical_center_code) + '_' + clinical_user.patient_code + '_' + Date.today.strftime('%d_%m_%Y')
  end

  def create_helpdesk_email
    ClinicalUserForms::CreateHelpdeskEmail.call(clinical_user_form: self)
  end
end
