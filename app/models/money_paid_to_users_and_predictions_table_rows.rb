class MoneyPaidToUsersAndPredictionsTableRows
  include ReportsHelper

  def initialize(params = {})
    @project = params[:project]
  end

  def rows
    rows = [['<PERSON><PERSON><PERSON><PERSON>', 'Liczba uczesników', 'Kwota zapłacona uczestnikom', 'Średni koszt wizyty', 'Liczba wizyt do odbycia', 'Kwota szacowana']]
    clinical_centers.each do |cc|
      rows << [cc.clinical_center_code,
       cc.clinical_users.count,
       formatted_number(cc.total_amount_paid_to_users),
       formatted_number(cc.average_visit_cost),
       cc.visits.not_happened.count,
       formatted_number(cc.total_predicted_amount_for_planned_visits)]
    end
    rows
  end

  private

  def clinical_centers
    @project.clinical_centers
  end
end