# == Schema Information
#
# Table name: project_statements
#
#  id         :integer          not null, primary key
#  file_path  :string(255)
#  month      :integer
#  year       :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  project_id :integer
#
# Indexes
#
#  index_project_statements_on_project_id  (project_id)
#
class ProjectStatement < ApplicationRecord
  #Miesieczny wyciag dla projektu

  #attr_accessible :file_path, :month, :project_id, :year

  validates :file_path, :month, :project_id, :year, presence: true
  validate :cant_create_two_statements_for_the_project_and_date

  belongs_to :project

  private

  def cant_create_two_statements_for_the_project_and_date
    if ProjectStatement.where(project_id: project_id, year: year, month: month).exists?
      errors.add(:base, 'project statement for this project and date already exists')
    end
  end
end
