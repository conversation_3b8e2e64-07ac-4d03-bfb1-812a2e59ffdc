# -*- encoding : utf-8 -*-

# == Schema Information
#
# Table name: events
#
#  id                :integer          not null, primary key
#  date              :datetime
#  description       :text
#  hour              :string(255)
#  offset            :integer
#  title             :string(255)
#  type              :string(255)
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  visit_id          :integer
#  visit_template_id :integer
#
# Indexes
#
#  index_events_on_id_and_type        (id,type)
#  index_events_on_visit_id           (visit_id)
#  index_events_on_visit_template_id  (visit_template_id)
#
# Foreign Keys
#
#  events_visit_id_fk           (visit_id => visits.id)
#  events_visit_template_id_fk  (visit_template_id => visits.id)
#
class Event < EventAbstract

  #attr_accessible :date, :description, :hour, :offset, :title, :visit_id

  belongs_to :visit

  validates :visit_id, presence: true

  validate :check_offset_range
  def check_offset_range
  	visit = self.visit
    return if visit.nil?
  	return if visit.visit_date.nil?
  	unless Time.new + 2.hours < self.date
  		errors.add(:offset,
        "Data wizyty po dodaniu offsetu musi być wczesnie<PERSON><PERSON>a niż #{I18n.l Time.new + 2.hours}")
  	end
  end

  before_validation :set_date
  def set_date
    d = DateTime.parse(self.hour)
    self.date = self.visit.visit_date + offset.days
    self.date = self.date.change({hour: d.hour, min: d.minute, sec: 0 })
  end



end
