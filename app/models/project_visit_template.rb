# == Schema Information
#
# Table name: project_visit_templates
#
#  id         :integer          not null, primary key
#  name       :string(255)
#  position   :integer
#  visible    :boolean          default(TRUE)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  project_id :integer
#  visit_id   :integer
#
# Indexes
#
#  index_project_visit_templates_on_project_id  (project_id)
#  index_project_visit_templates_on_visit_id    (visit_id)
#
# Foreign Keys
#
#  project_visit_templates_project_id_fk  (project_id => projects.id)
#  project_visit_templates_visit_id_fk    (visit_id => visits.id)
#
class ProjectVisitTemplate < ApplicationRecord
  attr_accessor :min_position,
  :max_position

  belongs_to :project
  has_many :visit_templates, inverse_of: :project_visit_template
  has_many :event_templates, :through => :visit_templates
  has_many :clinical_users
  has_many :visit_payment_categorizations, through: :visit_templates
  has_many  :all_visits, class_name: 'Visit'

  accepts_nested_attributes_for :visit_templates, allow_destroy: true
  accepts_nested_attributes_for :visit_payment_categorizations, allow_destroy: true
  #accepts_nested_attributes_for :event_templates, :allow_destroy => true

  validates :position, uniqueness: { scope: :project_id }
  validates :name, :position, presence: true

  scope :not_hidden, -> { where(visible: true) }
  default_scope { order('project_visit_templates.position ASC') }

  def to_s
    name
  end

  def visit_types
    types = []
    visit_templates.each do |t|
      types << t.visit_type
    end
    types.flatten.uniq.sort_by! &:position
  end

  def visit_types_opt
    types = []
    visit_templates.includes(:visit_type).each do |t|
      types << t.visit_type
    end
    types.flatten.uniq.sort_by! &:position
  end

  def visits(clinical_center)
    hashes = visits_names.map {|x| x[0]}
    visits = clinical_center.visits.includes(:clinical_transfers).includes(:visit_type).where(visit_type_id: hashes).to_a
  end

  def visits_names
    visit_types_opt.sort_by(&:position).map {|vt| [vt.id, vt.name]}
  end
end
