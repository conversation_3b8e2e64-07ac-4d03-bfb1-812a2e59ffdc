# == Schema Information
#
# Table name: action_reasons
#
#  id            :integer          not null, primary key
#  body          :text
#  position      :integer          default(0)
#  resource_type :string(255)      default("reason")
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#
class ActionReason < ApplicationRecord
  extend Enumerize

  enumerize :resource_type, in: [:reason, :response, :action], scope: true, default: :reason
end
