# == Schema Information
#
# Table name: researcher_manual_roles
#
#  id                                :bigint           not null, primary key
#  role                              :string
#  created_at                        :datetime         not null
#  updated_at                        :datetime         not null
#  contract_research_organization_id :bigint
#  researcher_manual_id              :bigint
#
# Indexes
#
#  index_researcher_manual_roles_on_researcher_manual_id  (researcher_manual_id)
#  researcher_manual_role_cro_index                       (contract_research_organization_id)
#
# Foreign Keys
#
#  fk_rails_...  (contract_research_organization_id => contract_research_organizations.id)
#  fk_rails_...  (researcher_manual_id => researcher_manuals.id)
#
class ResearcherManualRole < ApplicationRecord
  belongs_to :contract_research_organization
  belongs_to :researcher_manual

  validates :contract_research_organization, :role, :researcher_manual, presence: true
  validates :role, inclusion: ProjectRole::ROLE

  before_validation :set_cro, unless: :contract_research_organization

  private

  def set_cro
    self.contract_research_organization = researcher_manual.contract_research_organization
  end
end
