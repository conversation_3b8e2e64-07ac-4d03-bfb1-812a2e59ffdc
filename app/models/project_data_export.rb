class ProjectDataExport
  attr_reader :tab, :visits

  def initialize(params = {})
    @params = params
    @project = Project.find(params[:project_id])
    @researcher = Researcher.find(params[:researcher_id])
    set_visits
    set_tab
  end

  def set_visits
    set_by_visit_type
    set_by_amount
  end

  def set_by_visit_type
    if include_all_visits?
      @visits = @project.visits.where(state: Visit::SUCCESS, clinical_user_id: allowed_clinical_users_ids)
    else
      @visits = @project.visits.where(state: Visit::SUCCESS, clinical_user_id: allowed_clinical_users_ids, visit_type_id: collected_visit_type_ids)
    end
  end

  def set_by_amount
    unless all_amount?
      @visits = @visits.where('amount >= ? AND amount <= ?', min_amount, max_amount)
    end
  end

  def visit_names
    @visit_names ||= @visits.sort_by {|v| v.visit_type.position }.map(&:name).uniq
  end

  def  clinical_user_ids
    @clinical_user_ids ||= @visits.sort_by {|v| v.clinical_user.patient_code}.map(&:clinical_user_id).uniq
  end

  def clinical_users_ids_accessible_by_researcher
    @project.allowed_clinical_centers(@researcher).map(&:clinical_users).flatten.map(&:id)
  end

  def allowed_clinical_users_ids
    clinical_users_ids_from_params.select { |id| clinical_users_ids_accessible_by_researcher.include? id }
  end

  def clinical_users_ids_from_params
    include_all_users? ? all_clinical_users_ids : ClinicalUser.where(id: collected_clinical_user_ids(@params))
  end

  def include_all_users?
    @params[:all_clinical_users] == '1'
  end

  def all_clinical_users_ids
    include_all_centers? ? @project.clinical_users.pluck(:id) : ClinicalUser.where(clinical_center_id: @params[:clinical_center_id]).pluck(:id)
  end

  def set_tab
    @tab = {}
    @tab[""] = visit_names

    clinical_user_ids.each do |cu|
      patient_code = ClinicalUser.find(cu).patient_code
      @tab[patient_code] = []
      visit_names.each do |vname|
        @tab[patient_code] << @visits.where(clinical_user_id: cu).select { |v| v.name == vname }.try(:first).try(:amount).try(:to_s)
      end
    end
  end

  def collected_clinical_user_ids(params)
    params.select { |k, v| k.to_s.match(/clinical_user_/) }.select{ |k,v| v == '1' }.map{ |h| h.first.to_s.split('_').last.to_i }
  end

  def collected_visit_type_ids
    @params.select { |k, v| k.to_s.match(/visit_type_/) }.select{ |k,v| v == '1' }.map{ |h| h.first.to_s.split('_').last.to_i }
  end

  def include_all_visits?
    @params[:all_visit_types] == '1'
  end

  def include_all_centers?
    @params[:clinical_center_id].blank?
  end

  def all_amount?
    @params[:all_amount] == '1'
  end

  def min_amount
    @params[:min_amount].to_d || 0
  end

  def max_amount
    @params[:max_amount].to_d || 0
  end
end