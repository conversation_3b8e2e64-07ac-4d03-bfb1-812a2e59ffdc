class SummariesForInternalTransferManager
  def initialize(amount:, project_debit_summaries:)
    @amount = amount
    @project_debit_summaries = project_debit_summaries
    @result = []
    @combinations_count = 0
  end

  def summary_payment_options
    return [] unless @amount > 0

    select_summaries_with_matching_amount
    combine_summaries(sorted_summaries)

    @result
      .uniq
      .reject { |hash| hash[:notes_ids].empty? }
  end

  private

  def select_summaries_with_matching_amount
    @project_debit_summaries.select { |pds| pds.final_amount == @amount }.each do |pds|
      @result << summaries_hash([pds])
    end
  end

  def summaries_hash(pds_array)
    total = pds_array.map(&:final_amount).sum

    {
      name: combo_name(pds_array),
      notes_ids: pds_array.map(&:id),
      total: total,
      preselect: @amount == total
    }
  end

  def combo_name(pds_array)
    nrs = pds_array.map(&:note_number).join(', ')
    coverage = pds_array.map { |pds| pds.final_amount }.sum
    remaining = @amount - coverage
    "#{nrs}, pokrywa #{coverage}, zostaje #{remaining}"
  end

  def sorted_summaries
    @sorted_summaries ||= @project_debit_summaries
                          .select { |pds| pds.final_amount != @amount }
                          .sort_by { |pds| pds.final_amount }
                          .reverse
  end

  def combine_summaries(summaries, selected_pds = [])
    return if @combinations_count > 10

    sum = selected_pds.map { |pds| pds.final_amount }.inject 0, :+

    if sum <= @amount
      @result << summaries_hash(selected_pds)
      @combinations_count += 1
    end

    return if sum >= @amount

    (0..(summaries.length - 1)).each do |i|
      pds = summaries[i]
      remaining = summaries.drop(i + 1)
      combine_summaries(remaining, selected_pds + [pds])
    end
  end
end
