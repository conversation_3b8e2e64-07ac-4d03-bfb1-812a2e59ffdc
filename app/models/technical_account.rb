# == Schema Information
#
# Table name: technical_accounts
#
#  id             :integer          not null, primary key
#  account_number :string(255)
#  balance        :decimal(, )
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  project_id     :integer
#
# Indexes
#
#  index_technical_accounts_on_project_id  (project_id)
#
class TechnicalAccount < ApplicationRecord
  #attr_accessible :balance, :project_id, :account_number

  belongs_to :project

  before_save :add_acc_nr, unless: :account_number, if: :project_id

  delegate :cro, :clinical_protocol_code, to: :project

  def other_cro_technical_accounts
    cro.technical_accounts.where('technical_accounts.id != ?', self.id)
  end

  def to_s
    clinical_protocol_code
  end

  private

  def add_acc_nr
    proj_acc = self.project.pir_account_number

    return unless proj_acc

    proj_acc[-6..-1] = "1" + "%0.5d" % self.project_id
    self.account_number = proj_acc
  end
end
