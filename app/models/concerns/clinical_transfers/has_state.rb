module ClinicalTransfers::HasState
  extend ActiveSupport::Concern

  class_methods do
    def transfer_amount_within_auto_accept_range?(visit:, amount:)
      recent_visit = visit.clinical_user.visits.paid.where.not(id: visit.id).order(visit_date: :desc).first

      return false unless recent_visit

      transfer = recent_visit.clinical_transfer

      return false unless transfer
      return false unless visit.km_equal_to(recent_visit)

      amount_range = visit.contract_research_organization.auto_accept_transfer_amount_range
      allowed_range = (transfer.amount - transfer.amount * amount_range.to_d/100.to_d)..((transfer.amount + transfer.amount * amount_range.to_d/100.to_d))

      allowed_range.include?(amount)
    end

    def eligible_for_auto_accept_within_amount_and_km_range?(role:, visit:, amount:)
      role == 'Investigator+' && visit.contract_research_organization&.auto_accept_transfer_within_amount_range && transfer_amount_within_auto_accept_range?(visit: visit, amount: amount)
    end

    def determine_transfer_state(project, amount, r_role, visit, emergency_transfer = false, researcher)
      return ClinicalTransfer::WAITING if r_role != 'Manager' && project.allow_visit_payments_with_costs_above_limits && visit.any_costs_over_limit?
      return ClinicalTransfer::PROCESSING if eligible_for_auto_accept_within_amount_and_km_range?(role: r_role, visit: visit, amount: amount)

      if (r_role == 'Investigator+') || (r_role == 'Investigator') || (r_role == 'CTA')
        _limit_role = 'investigator'
      end

      _limit_role = 'cra' if (r_role == 'CRA+') || (r_role == 'CRA')
      _limit_role = 'operator' if r_role == 'Operator'

      # auto accept  ale do limitu kwoty przypisianej do roli w danym projekcie
      if (r_role != 'Manager') && project.auto_accept_over_limit_transfer && (amount <= project.send("#{_limit_role}_transfer_limit"))
        if project.debit_allowed && !emergency_transfer
          return ClinicalTransfer::DEBIT_WAITING
        else
          return ClinicalTransfer::PROCESSING
        end
      end

      return ClinicalTransfer::WAITING if (r_role != 'Manager') &&
        TransferLimitChecker.new(
          amount: amount,
          researcher_id: researcher.try(:id),
          clinical_user_id: visit.clinical_user_id
        ).amount_over_limit?

      if r_role == 'CRA'
        ClinicalTransfer::WAITING

      elsif project.post_paid_client_approve? && (r_role != 'Manager') && (r_role != 'CRA+')
        ClinicalTransfer::WAITING

      elsif (r_role == 'Investigator+') || (r_role == 'CRA+') || (r_role == 'Operator')

        if (amount <= project.send("#{_limit_role}_transfer_limit")) && !ProjectVisitAmountChecker.new(visit: visit, amount: amount).amount_should_trigger_alert?
          if project.debit_allowed && !emergency_transfer
            ClinicalTransfer::DEBIT_WAITING
          else
            ClinicalTransfer::PROCESSING
          end
        else
          ClinicalTransfer::WAITING
        end

      elsif r_role == 'Manager'
        if TransferLimitChecker.new(amount: amount, researcher_id: researcher.try(:id), clinical_user_id: visit.clinical_user_id).amount_over_limit?
          ClinicalTransfer::WAITING
        elsif project.debit_allowed && !emergency_transfer
          ClinicalTransfer::DEBIT_WAITING
        else
          ClinicalTransfer::PROCESSING
        end
      end
    end
  end
end