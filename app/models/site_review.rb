# == Schema Information
#
# Table name: reviews
#
#  id                     :bigint           not null, primary key
#  resource_type          :string
#  type                   :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  added_by_researcher_id :bigint
#  resource_id            :bigint
#
# Indexes
#
#  index_reviews_on_added_by_researcher_id         (added_by_researcher_id)
#  index_reviews_on_id_and_type                    (id,type)
#  index_reviews_on_resource_type_and_resource_id  (resource_type,resource_id)
#
# Foreign Keys
#
#  fk_rails_...  (added_by_researcher_id => researchers.id)
#
class SiteReview < Review
  delegate :project_id,
    :cro,
    :project,
    to: :resource, allow_nil: true
end
