# == Schema Information
#
# Table name: action_items
#
#  id                   :bigint           not null, primary key
#  added_by_type        :string
#  deadline             :date
#  description          :text
#  for_site_payments    :boolean          default(FALSE)
#  locale               :string           default("en")
#  number               :string
#  owner                :string
#  resolve_comment      :text
#  resolved_at          :datetime
#  resource_type        :string
#  status               :string
#  type                 :string
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  added_by_id          :bigint
#  resolved_by_id       :bigint
#  resource_id          :bigint
#  target_researcher_id :bigint
#
# Indexes
#
#  index_action_items_on_added_by_type_and_added_by_id  (added_by_type,added_by_id)
#  index_action_items_on_id_and_type                    (id,type)
#  index_action_items_on_resolved_by_id                 (resolved_by_id)
#  index_action_items_on_resource_type_and_resource_id  (resource_type,resource_id)
#  index_action_items_on_target_researcher_id           (target_researcher_id)
#
# Foreign Keys
#
#  fk_rails_...  (resolved_by_id => researchers.id)
#  fk_rails_...  (target_researcher_id => researchers.id)
#
class ActionItem::Simple < ActionItem
  attribute :cro_name, :string
  attribute :project_code, :string
  attribute :context, :string

  validates :owner, presence: true

  def cro_name
    cro&.name
  end

  def project_code
    project&.clinical_protocol_code
  end

  def context
    type_name, code = case resource_type
    when 'ClinicalUser'
      ['Subject', resource.patient_code]
    when 'ClinicalCenter'
      ['Site', resource.clinical_center_code]
    when 'Visit'
      ['Visit', "#{ resource.clinical_user&.patient_code }/#{ resource.name }"]
    else
      [resource_type, resource_id]
    end

    "#{ type_name }: #{ code }"
  end
end
