require 'rails_helper'
require 'rspec'

RSpec.describe ProjectFeeReport do
  let(:project) { create :project }
  let(:start_date) { Date.today.beginning_of_month }
  let(:report) { ProjectFeeReport.new(project_id: project.id, start_date: start_date, end_date: Date.today.end_of_month) }

  describe '#new_patient_accounts_fees_total_with_vat' do
    context 'PLN currency' do
      it 'calculates total fee for new patient accounts' do
        create :fee, project: project, amount: 1, admin_user_id: nil, researcher_id: nil, type_name: Fee::TYPE_NAMES['Patient']
        create :fee, project: project, amount: 4, admin_user_id: nil, researcher_id: nil, type_name: Fee::TYPE_NAMES['Patient']
        total_with_vat = 5 + 5 * 0.23
        expect(report.new_patient_accounts_fees_total_with_vat).to eq(total_with_vat)
      end
    end

    context 'with foreign currency' do
      it 'gets the currency rate for this month and uses it to calculate fee in PLN' do
        create :fee, project: project, amount: 1, admin_user_id: nil, researcher_id: nil, type_name: Fee::TYPE_NAMES['Patient']
        create :fee, project: project, amount: 4, admin_user_id: nil, researcher_id: nil, type_name: Fee::TYPE_NAMES['Patient']
        create :currency_rate, target_currency: 'PLN', base_currency: 'EUR', rate: 2, valid_from: Date.today.beginning_of_month, valid_to: Date.today.end_of_month
        total_in_pln_with_vat = 12.3
        allow(report).to receive(:fee_currency).and_return('EUR')
        expect(report.new_patient_accounts_fees_total_with_vat).to eq(total_in_pln_with_vat)
      end
    end
  end

  describe '#currency_rate' do
    it 'returns currency_rate valid for given report' do
      create :currency_rate, target_currency: 'PLN', base_currency: 'EUR', rate: 2, valid_from: Date.today.beginning_of_month, valid_to: Date.today.end_of_month
      allow(report).to receive(:fee_currency).and_return('EUR')
      expect(report.currency_rate).to eq 2
    end
  end

  describe '#transfer_fees' do
    it 'returns ordered transfer fees' do
      cf1 = create :citi_file, created_at: Time.new
      cf2 = create :citi_file, created_at: 1.week.ago
      ct1 = create :clinical_transfer, state: 'b', citi_file_id: cf1.id
      ct2 = create :clinical_transfer, state: 'b', citi_file_id: cf2.id

      fee1 = create :fee, created_at: start_date + 1.day, clinical_transfer_id: ct1.id, project_id: project.id, type_name: 't'
      fee2 = create :fee, created_at: start_date + 1.day, clinical_transfer_id: ct2.id, project_id: project.id, type_name: 't'

      result = report.transfer_fees
      expect(result).to eq [fee2, fee1]
    end
  end
end
