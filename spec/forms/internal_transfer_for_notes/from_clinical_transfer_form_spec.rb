require 'rails_helper'

RSpec.describe InternalTransferForNotes::FromClinicalTransferForm do
  let!(:amount) { 100 }
  let!(:cro) { create :contract_research_organization }
  let!(:transfer) { create :clinical_transfer, amount: 100, currency_account_id: cro_currency_account.id, balance: 100 }
  let!(:project) { create :project, contract_research_organization_id: cro.id, saldo: 1000 }
  let!(:unpaid_note) { create :project_debit_summary, project_id: project.id, state: ProjectDebitSummary::CREATED, saldo: -50 }
  let!(:project_currency_account) { project.currency_account }
  let(:cro_currency_account) { cro.currency_accounts.find_by!(currency: 'PLN') }
  let(:form) { InternalTransferForNotes::FromClinicalTransferForm.new(clinical_transfer: transfer, project_debit_summary_ids: [unpaid_note.id]) }

  describe 'validations' do
    it 'is invalid without project_debit_summaries' do
      form.project_debit_summary_ids = []

      expect(form).to_not be_valid
    end

    it 'is invalid without clinical_transfer' do
      form.clinical_transfer = nil

      expect(form).to_not be_valid
    end

    it 'is invalid if project_debit_summary currencies are diffent to ' do
      unpaid_note.contract_research_organization.update! currency: 'USD'

      expect(form).to_not be_valid
    end
  end

  describe '#save' do
    it 'pays for notes' do
      expect(ProjectDebitSummaries::ProcessPayments).to receive(:call).with(hash_including(notes: [unpaid_note]))

      form.save
    end
  end
end
