require 'rails_helper'

RSpec.describe HelpdeskEmailReplyForm do
  let(:helpdesk_email) { create :helpdesk_email, to: '<EMAIL>, <EMAIL>, <EMAIL>', cc: '<EMAIL>' }
  let(:form) { HelpdeskEmailReplyForm.new(helpdesk_email_id: helpdesk_email.id, subject: 'subject', body: 'body') }

  it "sends email" do
    cc_email = '<EMAIL>'
    helpdesk_email
    ActionMailer::Base.deliveries = []
    form.cc_emails = [cc_email]
    form.save

    expect(ActionMailer::Base.deliveries.size).to eq 1
    email = ActionMailer::Base.deliveries.first
    expect(email.cc).to eq ["<EMAIL>", cc_email, "<EMAIL>"]
  end

  it "creates helpdesk response" do
    form.save

    expect(HelpdeskResponse.count).to eq 1
  end
end

