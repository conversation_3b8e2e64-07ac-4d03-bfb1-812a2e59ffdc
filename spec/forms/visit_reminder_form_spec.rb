require 'rails_helper'
require 'rspec'

RSpec.describe VisitReminderForm do
  let(:project) { create :project }
  let(:clinical_center) { create :clinical_center, project: project }
  let(:clinical_user) { create :clinical_user, project: project, clinical_center: clinical_center }
  let(:researcher) { create :researcher }
  let(:visit) { create :visit, clinical_user: clinical_user, state: Visit::WAITING }
  let(:form) { VisitReminderForm.new(reminderer: researcher, visit: visit) }

  it 'is invalid if a reminder has been created within last 7 days' do
    create :visit_reminder, visit: visit, created_at: 3.day.ago
    expect(form.valid?).to eq false
  end

  it 'creates a visit_reminder' do
    expect(VisitReminder.count).to eq(0)
    expect(form).to be_valid
    form.save
    expect(VisitReminder.count).to eq(1)
  end

  context 'send email' do
    it 'sends to CRA who created activity connected to clinical_user' do
      pr1 = create :project_role, project_role: 'CRA', project: project
      create :clinical_center_role, project_role: pr1, clinical_center: clinical_center
      pr2 = create :project_role, project_role: 'CRA+', project: project
      create :clinical_center_role, project_role: pr2, clinical_center: clinical_center
      ActionMailer::Base.deliveries = []
      clinical_center.update! primary_cra_id: pr1.researcher_id
      create :clinical_user_account_activity, clinical_user: clinical_user, researcher: pr1.researcher
      expect(form).to be_valid
      form.save

      emails = ActionMailer::Base.deliveries
      expect(emails.size).to eq 1
      expect(emails.first.to).to eq([pr1.researcher.email])
      expect(emails.first.cc).to eq([pr2.researcher.email])
    end
  end
end
