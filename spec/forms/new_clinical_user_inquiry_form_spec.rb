require 'rails_helper'
require 'rspec'

RSpec.describe NewClinicalUserInquiryForm do
  let(:clinical_user) { create :clinical_user, phone_number: '729395816' }
  let(:form) {NewClinicalUserInquiryForm.new(clinical_user_id: clinical_user.id, message: 'boom', send_sms: false, sms_text: '')}

 #  describe '#save' do
 #    it 'creates new ClinicalUserInquiry' do
 #      form.save
 #      expect(ClinicalUserInquiry.count).to eq 1
 #    end

 #    it 'sends emails to all researchers who have roles in the clinical center the clinical user belongs to' do
 #      belonging_researcher = create :researcher
 #      not_belonging_researcher = create :researcher
 #      project_role = create :project_role, researcher: belonging_researcher, project: clinical_user.clinical_center.project, project_role: 'CRA'
 #      build(:clinical_center_role, project_role: project_role, clinical_center: clinical_user.clinical_center).save(validate: false)
 #      ActionMailer::Base.deliveries = []
 #      form.save
 #      expect(ActionMailer::Base.deliveries.count).to eq 1
 #      expect(ActionMailer::Base.deliveries.last.to).to eq [belonging_researcher.email]
 #    end

 #    it 'sends sms to clinical user if send_sms is enabled' do
 #      form.sms_text = 'some text'
 #      form.send_sms = '1'
 #      form.save
 #      expect(clinical_user.clinical_mobile_messages.count).to eq 1
 #    end

 # it 'doesnt sends sms to clinical user if send_sms is disabled' do
 #      form.sms_text = 'some text'
 #      form.send_sms = '0'
 #      form.save
 #      expect(clinical_user.clinical_mobile_messages.count).to eq 0
 #    end
 #  end
end
