require 'rails_helper'
require 'rspec'
require 'sucker_punch/testing/inline'

RSpec.describe ProcessShipmentJob do
  describe 'perform' do
    it "creates clinical users and fees" do
      expect_any_instance_of(ClinicalUser).to receive(:copy_visits_from_template!)

      project = create :project
      cc = create :clinical_center, project_id: project.id
      create :project_visit_template, project_id: project.id
      sh = create :shipment, patient_codes: '12345', clinical_center_id: cc.id
      expect(Fee).to receive(:charge_new_patient!)

      ProcessShipmentJob.new.perform(shipment: sh)

      expect(ClinicalUser.count).to eq 1
      expect(ClinicalUser.first.shipment_id).not_to be_nil
    end

    it "create clinical_users without swift and account_number" do
      expect_any_instance_of(ClinicalUser).to receive(:copy_visits_from_template!)

      project = create :project
      cc = create :clinical_center, project_id: project.id, country_name: 'Czech Republic'
      create :project_visit_template, project_id: project.id
      sh = create :shipment, patient_codes: '12345', clinical_center_id: cc.id

      ProcessShipmentJob.new.perform(shipment: sh)

      expect(ClinicalUser.count).to eq 1
    end

    context 'no patient codes' do
      it "does nothing" do
        sh = build :shipment, patient_codes: nil

        ProcessShipmentJob.new.perform(shipment: sh)
      end
    end
  end
end