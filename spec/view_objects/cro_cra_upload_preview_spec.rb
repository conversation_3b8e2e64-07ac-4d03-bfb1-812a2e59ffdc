require 'rails_helper'
require 'rspec'

RSpec.describe CroCraUploadPreview do
  let(:file) { Rack::Test::UploadedFile.new(File.join(Rails.root, 'spec', 'support', 'cro_cra.csv'), 'csv') }
  let(:cro) { create :contract_research_organization }
  let(:project) { create :project, contract_research_organization: cro }
  let(:subject) { CroCraUploadPreview.new(cro: cro, file: file) }
  let!(:cro_domain) { create :cro_domain, contract_research_organization_id: cro.id, name: 'debil.com' }

  describe 'new_accounts' do
    it 'returns new researchers' do
      result = subject.new_accounts

      expect(result.size).to eq 3
    end
  end

  describe 'accounts_to_delete' do
    it 'returns researchers whose emails are not in the file, who have CRA/CRA+ role and match email domain' do
      researcher = create :researcher, email: '<EMAIL>'
      create :project_role, researcher: researcher, project: project, project_role: 'CRA'

      researcher_not_to_delete = create :researcher, email: '<EMAIL>'
      create :project_role, researcher: researcher_not_to_delete, project: project, project_role: 'Manager'

      expect(subject.accounts_to_delete).to eq [researcher]
    end
  end

  describe 'accounts_to_update' do
    it 'returns researchers from cro whose emails are in the file' do
      researcher = create :researcher, email: '<EMAIL>'
      create :project_role, researcher: researcher, project: project, project_role: 'CRA'

      expect(subject.accounts_to_update).to eq [researcher]
    end
  end
end
