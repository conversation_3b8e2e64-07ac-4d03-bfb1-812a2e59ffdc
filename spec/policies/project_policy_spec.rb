require 'rails_helper'

RSpec.describe ProjectPolicy do
  let(:researcher) { build :researcher }
  let(:cro) { build :contract_research_organization }
  let(:project) { build :project }

  permissions :new? do
    it 'allows access if researcher has a cro_role' do
      cro_role = double(exists?: true)
      allow(researcher).to receive(:cro_roles) { cro_role }
      expect(ProjectPolicy.new(researcher, project).new?).to eq true
    end

    it 'blocks access if researcher has no cro_roles' do
      expect(ProjectPolicy.new(researcher, project).new?).to eq false
    end
  end

  permissions :create? do
    it 'allows access if researcher\'s managed_cro is the records cro' do
      allow(researcher).to receive(:managed_cro) { cro }
      project = double(contract_research_organization: cro)
      expect(ProjectPolicy.new(researcher, project).create?).to eq true
    end

    it 'blocks access if researcher\'s managed_cro is not the records cro' do
      allow(researcher).to receive(:managed_cro) { cro }
      another_cro = build :contract_research_organization
      project = double(contract_research_organization: another_cro)
      expect(ProjectPolicy.new(researcher, project).create?).to eq false
    end

    it 'blocks access if researcher doesnt have a cro role' do
      expect(ProjectPolicy.new(researcher, project).create?).to eq(false)
    end
  end
end
