require 'rails_helper'
require 'rspec'

RSpec.describe ProjectRolePolicy do
  subject { described_class }
  let(:researcher) { create :researcher }
  let(:project) { create :project }

  permissions :manage? do
    context 'researcher has role CRA in the project' do
      let!(:researcher_role) { create :project_role, project_role: 'CRA', project_id: project.id, researcher_id: researcher.id }

      ['CRA', 'Investigator', 'Investigator+'].each do |role|
        it "allows access if the record role is #{ role }" do
          pr = build :project_role, project_id: project.id, project_role: role
          pr.save validate: false
          expect(subject).to permit(researcher, pr)
        end
      end

      ['Manager', 'Operator', 'Observer', 'CRA+'].each do |role|
        it "denies access if the record role is #{ role }" do
          pr = build :project_role, project_id: project.id, project_role: role
          pr.save validate: false
          expect(subject).not_to permit(researcher, pr)
        end
      end
    end
  end
end
