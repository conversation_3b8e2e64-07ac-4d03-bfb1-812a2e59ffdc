require 'rails_helper'

RSpec.feature "Tasks" do
  let(:researcher) { create :researcher, payclinical_employee: true }

  scenario 'researcher adds task' do
    another_researcher = create :researcher, payclinical_employee: true
    create :project_role, :operator, researcher: another_researcher
    desc = 'desc'
    title = 'title'
    deadline = Date.tomorrow

    login researcher
    visit v2_sponsor_payclinical_employees_tasks_path
    click_link 'Add Task'

    fill_in :task_title, with: title
    fill_in :task_description, with: desc
    select another_researcher.full_name, from: :task_assigned_to_id
    find('#task_deadline').set(deadline)
    click_button 'Save'

    expect(current_path).to eq v2_sponsor_payclinical_employees_tasks_path
    expect(Task.count).to eq 1
    task = Task.first
    expect(task.title).to eq title
    expect(task.description).to eq desc
    expect(task.deadline.to_date).to eq deadline
    expect(task.assigned_to).to eq another_researcher
    expect(task.assigned_by).to eq researcher
    expect(task.state).to eq 'new'
  end

  context 'HelpdeskEmailCalls' do
    let!(:helpdesk_email_call) { create :helpdesk_email_call, assigned_to_id: researcher.id, assigned_to_type: 'Researcher' }

    scenario "researcher marks call as called" do
      comment = 'comment'
      called_at = Date.yesterday
      login researcher
      visit v2_sponsor_payclinical_employees_tasks_path

      click_link 'Edit'
      fill_in :helpdesk_email_call_comment, with: comment
      select 'Completed', from: :helpdesk_email_call_state
      find('#helpdesk_email_call_called_at').set(called_at)
      click_button 'Save'

      expect(current_path).to eq v2_sponsor_payclinical_employees_tasks_path
      expect(helpdesk_email_call.reload.state).to eq 'completed'
      expect(helpdesk_email_call.called_at.to_date).to eq called_at
    end
  end
end