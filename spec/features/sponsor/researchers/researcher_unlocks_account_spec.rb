require "rails_helper"

RSpec.feature "Researcher profile", :type => :feature do
  let(:token) { '12345' }
  let!(:researcher) { create :researcher, locked_at: 1.day.ago, unlock_token: token }

  # scenario "gets redirected to root if token is not provided" do
  #   visit new_v2_sponsor_account_unlock_path
  #   expect(current_path).to eq(root_path)
  #   expect(researcher.reload.locked_at).not_to eq(nil)
  # end

  # scenario "unlocks account if token is correct" do
  #   visit new_v2_sponsor_account_unlock_path(token: token)
  #   expect(current_path).to eq(root_path)
  #   expect(researcher.reload.locked_at).to eq(nil)
  # end

  # scenario "redirects if token is not correct" do
  #   visit new_v2_sponsor_account_unlock_path(token: 'blabla')
  #   expect(current_path).to eq(root_path)
  #   expect(researcher.reload.locked_at).not_to eq(nil)
  # end
end
