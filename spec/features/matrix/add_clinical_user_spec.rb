require "rails_helper"

def fill_in_cu_form
  visit new_admin_clinical_center_clinical_user_path(clinical_center)
  fill_in "clinical_user_patient_code", with: "123azz"
  fill_in 'clinical_user_first_name', with: '<PERSON>'
  fill_in 'clinical_user_last_name', with: '<PERSON>'
  fill_in :clinical_user_account_number, with: '33116022020000000034906630'
  fill_in :clinical_user_bank_account_sec_code, with: '336630'
  select "male", from: "clinical_user_sex"
  select "lmp_acc", from: "clinical_user_transfer_destination"
end

RSpec.feature "Add clinical user", :type => :feature do
  let(:admin_user) { create :admin_user, role: 'admin' }
  let(:project) { create :project }
  let(:clinical_center) { create :clinical_center, :with_acc_nr, project_id: project.id }

  scenario 'adds clinical user' do
    sign_in_matrix(admin_user)
    fill_in_cu_form
    data_confirmed_at = 1.month.ago
    select_date data_confirmed_at, from: 'clinical_user_data_confirmed_at'
    click_button 'Zapisz uczestnika'

    expect(ClinicalUser.count).to eq(1)
    cu = ClinicalUser.first
    expect(cu.data_confirmed_at.to_date).to eq(data_confirmed_at.to_date)
    # expect(cu.fees.count).to eq(3)
  end

  scenario 'adds clinical_user and doesnt charge for this month' do
    sign_in_matrix(admin_user)
    fill_in_cu_form
    uncheck "Pobierz oplate za utrzymanie w tym miesiacu"
    click_button 'Zapisz uczestnika'

    expect(ClinicalUser.count).to eq(1)
    cu = ClinicalUser.first
    expect(cu.fees.count).to eq(0)
  end

  scenario 'change transfer_destination to clinical_center' do
    sign_in_matrix(admin_user)

    cu = create :clinical_user, transfer_destination: 'bank_account', clinical_center_id: clinical_center.id, project_id: project.id
    t1 = create :clinical_transfer, account_number: cu.account_number, clinical_user_id: cu.id

    visit edit_admin_clinical_user_path(cu)

    select "clinical_center", from: "clinical_user_transfer_destination"
    click_button 'Zapisz uczestnika'

    expect(t1.reload.account_number).to eq(clinical_center.account_number)
  end

  scenario 'change transfer_destination to bank' do
    sign_in_matrix(admin_user)

    cu = create :clinical_user, transfer_destination: 'lmp_acc', clinical_center_id: clinical_center.id, project_id: project.id, account_number: ''
    t1 = build :clinical_transfer, account_number: '', clinical_user_id: cu.id, name: nil
    t1.save! validate: false

    visit edit_admin_clinical_user_path(cu)

    fill_in "clinical_user_account_number", with: "33116022020000000034906630"
    fill_in 'clinical_user_bank_account_sec_code', with: '336630'
    select "bank_account", from: "clinical_user_transfer_destination"
    click_button 'Zapisz uczestnika'

    expect(cu.reload.transfer_destination).to eq('bank_account')
    expect(t1.reload.account_number).to eq(cu.account_number)
    expect(t1.name).to eq(cu.full_name)
  end

  scenario 'add clinical_user with lmp_acc destination and perm_data' do
   sign_in_matrix(admin_user)

   visit new_admin_clinical_center_clinical_user_path(clinical_center)
   fill_in "clinical_user_patient_code", with: "123azz"
   fill_in 'clinical_user_first_name', with: 'Bob'
   fill_in 'clinical_user_last_name', with: 'Marley'
   select "male", from: "clinical_user_sex"
   select "lmp_acc", from: "clinical_user_transfer_destination"
   data_confirmed_at = 1.month.ago
   select_date data_confirmed_at, from: 'clinical_user_data_confirmed_at'
   check "clinical_user_perm_data"
   fill_in :clinical_user_account_number, with: '33116022020000000034906630'
   fill_in :clinical_user_bank_account_sec_code, with: '336630'
   click_button 'Zapisz uczestnika'

   expect(ClinicalUser.count).to eq(1)
   expect(page).to have_content 'Sprawdz, czy pacjent dostarczył zgodę na przetwarzanie danych osobowych.'
 end
end
