require 'rails_helper'

RSpec.feature "Ad<PERSON> can sign project_debit for a researcher" do
  let(:pd) { create :project_debit, status: 'w' }
  let(:admin) { create :admin_user }

  scenario "sign project debit and NOT send any emails or sms" do
    r1 = create :researcher
    create :project_role, project: pd.project, researcher: r1, project_role: 'Manager'
    pds = create :project_debit_signature, project_debit_id: pd.id, signature_date: nil
    ActionMailer::Base.deliveries = []

    sign_in_matrix(admin)
    visit admin_project_debit_path pd
    click_link 'Podpisz w imieniu'

    select r1.full_name, from: "researcher"
    click_button '<PERSON>dpisz'

    expect(pd.reload.status).to eq('i')
    expect(pds.reload.signed_by_researcher_id).to eq(r1.id)
    expect(pds.signature_date).not_to be_nil
    expect(ActionMailer::Base.deliveries.size).to eq(0)
    expect(ClinicalMobileMessage.count).to eq(0)
  end
end

