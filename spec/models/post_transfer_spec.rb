require 'rails_helper'

RSpec.describe PostTransfer, :type => :model do
  it 'has a valid factory' do
    post_transfer = build :post_transfer
    expect(post_transfer).to be_valid
  end

  describe '#uninvoiced_sent_post_transfers_created_before' do
   it 'returns fees for post transfers that have been sent within or before given date range' do
    sent_post_transfer = create :post_transfer, state: PostTransfer::SENT, order_date: 1.month.ago
    ordered_post_transfer = create :post_transfer, state: PostTransfer::ORDERED, order_date: 1.month.ago
    old_sent_post_transfer = create :post_transfer, state: PostTransfer::SENT, order_date: 3.month.ago
    query = PostTransfer.uninvoiced_sent_post_transfers_created_before(1.month.ago.end_of_month)

    invoiced_sent_post_transfer = create :post_transfer, state: PostTransfer::SENT, order_date: 1.month.ago
    invoiced_ordered_post_transfer = create :post_transfer, state: PostTransfer::ORDERED, order_date: 1.month.ago
    invoiced_old_sent_post_transfer = create :post_transfer, state: PostTransfer::SENT, order_date: 3.month.ago
    query = PostTransfer.uninvoiced_sent_post_transfers_created_before(1.month.ago.end_of_month)
    expect(query).to include(sent_post_transfer, old_sent_post_transfer)
    expect(query.size).to eq 4
  end
end

describe '#get_total_amount' do
  context 'amount is set' do
    it 'returns the amount' do
      post_transfer = create :post_transfer, amount: 1
      expect(post_transfer.get_total_amount).to eq 1
    end
  end

  context 'description' do
    it 'returns sum of amounts from related clinical_transfers' do
      post_transfer = create :post_transfer, amount: nil
      allow(post_transfer).to receive(:transfers_total).and_return(2)
      expect(post_transfer.get_total_amount).to eq 2
    end
  end
end

describe '#not_from_test_projects' do
  it 'returns post_transfers that dont belong to a test project' do
    test_p = create :project, test_project: true
    non_test_p = create :project, test_project: false
    test_p_t = create :post_transfer, project: test_p
    non_test_p_t = create :post_transfer, project: non_test_p
    expect(PostTransfer.not_from_test_projects).to eq [non_test_p_t]
  end
end

describe '#set_address_fields' do
  it 'sets address fields to values of other post_transfer of the same user' do
    cu = create :clinical_user
    pt1 = create :post_transfer, clinical_user: cu, house_number: '10'
    pt2 = create :post_transfer, clinical_user: cu, house_number: nil
    main_pt = create :post_transfer, clinical_user: cu, house_number: nil

    main_pt.set_address_fields
    expect(main_pt.house_number).to eq('10')
  end
end
end
