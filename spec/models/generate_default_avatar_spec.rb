require 'rails_helper'
require 'rspec'

RSpec.describe GenerateDefaultAvatar do
  let(:researcher) { create :researcher }

  it 'is true when generates default avatar for user with full_name present' do
    researcher = build :researcher, first_name: "<PERSON>", last_name: "<PERSON><PERSON>"
    researcher.generate_default_avatar
    expect(researcher.avatar).to be_present
  end

  it 'is false when generates default avatar for user without full_name present' do
    researcher = build :researcher, first_name: "", last_name: ""
    researcher.generate_default_avatar
    expect(researcher.avatar).not_to be_present
  end

  it 'is false when generates default avatar for user with already uploaded avatar' do
    researcher = build :researcher, avatar: Rack::Test::UploadedFile.new(File.join(Rails.root, 'spec', 'support', 'img', 'logo_image.jpg'), 'image/jpg')

    uploaded_filename = researcher.avatar.filename
    researcher.generate_default_avatar

    expect(researcher.avatar).to be_present
    expect(researcher.avatar.filename).to eq uploaded_filename
  end

  it 'is true when generates default avatar for user with already uploaded avatar with force flag' do
    researcher = build :researcher, avatar: Rack::Test::UploadedFile.new(File.join(Rails.root, 'spec', 'support', 'img', 'logo_image.jpg'), 'image/jpg')

    uploaded_filename = researcher.avatar.filename
    researcher.generate_default_avatar(force: true)

    expect(researcher.avatar).to be_present
    expect(researcher.avatar.filename).not_to eq uploaded_filename
  end
  
  
end
