require 'rails_helper'
require 'rspec'

RSpec.describe AdminUser do
  let(:admin_user) { create :admin_user, role: 'user', email: '<EMAIL>' }
  let(:other_admin) { create :admin_user, role: 'user', email: '<EMAIL>' }
  let(:admin) { create :admin_user, role: 'admin', email: '<EMAIL>' }
  let(:researcher) { create :researcher }

  describe '#clinical_users_to_verify' do
    context 'admin_user with role "user"' do
      it 'returns unconfirmed clinical_users added by other admin' do
        clinical_user = create :clinical_user, added_by_admin_user_id: other_admin.id, perm_data: false
        clinical_user_confirmed = create :clinical_user, added_by_admin_user_id: other_admin.id, perm_data: true
        expect(admin_user.clinical_users_to_verify).to include *[clinical_user, clinical_user_confirmed]
      end

      it 'returns unconfirmed clinical_users added by a researcher' do
        clinical_user = build :clinical_user, added_by_admin_user_id: nil, added_by_researcher_id: researcher.id, perm_data: false
        clinical_user.save(validate: false)
        clinical_user_confirmed = build :clinical_user, added_by_admin_user_id: nil, added_by_researcher_id: researcher.id, perm_data: true
        expect(admin_user.clinical_users_to_verify).to eq [clinical_user]
      end

      it 'returns unconfirmed clinical_users with unknown added by' do
        clinical_user = build :clinical_user, added_by_admin_user_id: nil, added_by_researcher_id: nil, perm_data: false
        clinical_user.save(validate: false)
        clinical_user_confirmed = build :clinical_user, added_by_admin_user_id: nil, added_by_researcher_id: nil, perm_data: true
        expect(admin_user.clinical_users_to_verify).to eq [clinical_user]
      end

      it 'doesnt return unconfirmed clinical_users added by the admin_user' do
        clinical_user = create :clinical_user, added_by_admin_user_id: admin_user.id, perm_data: false
        expect(admin_user.clinical_users_to_verify).to eq []
      end
    end

    context 'admin_user with role admin' do
      it 'return all unconfirmed clinical_users' do
        clinical_user = create :clinical_user, added_by_admin_user_id: admin_user.id, perm_data: false
        clinical_user_2 = build :clinical_user, added_by_admin_user_id: nil, added_by_researcher_id: nil, perm_data: false
        clinical_user_2.save(validate: false)
        clinical_user_3 = create :clinical_user, added_by_admin_user_id: admin.id, perm_data: false
        expect(admin.clinical_users_to_verify).to include(clinical_user, clinical_user_2, clinical_user_3)
      end
    end
  end

  describe '#can_verify_clinical_user?' do
    context 'admin_user with role "user"' do
      it 'is false if clinical_user was added by the admin' do
        clinical_user = create :clinical_user, added_by_admin_user_id: admin_user.id, perm_data: false
        expect(admin_user.can_verify_clinical_user?(clinical_user)).to eq false
      end

      it 'is true if clinical_user was added by another admin_user' do
        clinical_user = create :clinical_user, added_by_admin_user_id: other_admin.id, perm_data: false
        expect(admin_user.can_verify_clinical_user?(clinical_user)).to eq true
      end

      it 'is true if admin_user has role "admin"' do
        clinical_user = create :clinical_user, added_by_admin_user_id: admin_user.id, perm_data: false
        admin_user.role = 'admin'
        expect(admin_user.can_verify_clinical_user?(clinical_user)).to eq true
      end

      it 'is true if clinical_user was added by a researcher' do
        clinical_user = build :clinical_user, added_by_researcher_id: researcher.id, perm_data: false, added_by_admin_user_id: nil
        clinical_user.save(validate: false)
        expect(admin_user.can_verify_clinical_user?(clinical_user)).to eq true
      end

      it 'is false if clinical_user was added_by_researcher and later edited by the admin_user' do
        clinical_user = build :clinical_user, added_by_researcher_id: researcher.id, perm_data: false, added_by_admin_user_id: admin_user.id
        clinical_user.save(validate: false)
        expect(admin_user.can_verify_clinical_user?(clinical_user)).to eq false
      end

      it 'is false if clinical_user has perm_data' do
        admin_user.role = 'admin'
        clinical_user = create :clinical_user, added_by_admin_user_id: admin_user.id, perm_data: true
        expect(admin_user.can_verify_clinical_user?(clinical_user)).to eq false
      end
    end
  end

  it 'strips space from phone number' do
    admin_user.phone_number = ' 123 123 123 '
    admin_user.save!
    expect(admin_user.phone_number).to eq '123123123'
  end
end