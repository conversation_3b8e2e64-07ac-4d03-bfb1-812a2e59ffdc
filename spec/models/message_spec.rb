require 'rails_helper'

RSpec.describe Message do
  let(:researcher) { create :researcher }
  let(:admin) { create :admin_user }
  let(:project) { create :project }
  let(:clinical_center) { create :clinical_center, project_id: project.id }
  let(:clinical_user) { create :clinical_user, project_id: project.id, clinical_center_id: clinical_center.id }

  it 'has a valid factory' do
    msg = build :message
    expect(msg).to be_valid
  end

  describe 'self.regarding_site_or_patients' do
    it 'returns all messages regarding the clinical_center' do
      cc = create :clinical_center
      m1 = create :message, clinical_center_id: cc.id
      m2 = create :message

      expect(Message.regarding_site_or_patients(clinical_center: cc)).to eq([m1])
    end

    it 'returns all messages regarding clinical_users from the clinical_center' do
      cc = create :clinical_center
      cu = create :clinical_user, clinical_center_id: cc.id, project_id: cc.project_id
      m1 = create :message, clinical_user_id: cu.id
      m2 = create :message

      expect(Message.regarding_site_or_patients(clinical_center: cc)).to eq([m1])
    end
  end

  describe 'append_summary_to_body' do
    it "updates body" do
      m = create :message, clinical_user_id: clinical_user.id, clinical_center_id: clinical_center.id, project_id: project.id, body: 'bla'

      expect(m.reload.body).to eq "bla [#{project.clinical_protocol_code}/#{clinical_center.clinical_center_code}/#{clinical_user.patient_code}]"
    end

    it "updates body" do
      m = create :message, clinical_user_id: nil, clinical_center_id: clinical_center.id, project_id: project.id, body: 'bla'

      expect(m.reload.body).to eq "bla [#{project.clinical_protocol_code}/#{clinical_center.clinical_center_code}/ANY]"
    end

    it "updates body" do
      m = create :message, clinical_user_id: nil, clinical_center_id: nil, project_id: project.id, body: 'bla'

      expect(m.reload.body).to eq "bla [#{project.clinical_protocol_code}/ANY]"
    end
  end
end
