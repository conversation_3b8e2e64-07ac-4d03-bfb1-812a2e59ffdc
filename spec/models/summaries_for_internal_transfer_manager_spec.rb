require 'rails_helper'

RSpec.describe SummariesForInternalTransferManager do
  describe '#summary_payment_options' do
    it 'returns options for paying for notes' do
      pds1 = build :project_debit_summary, saldo: -100
      pds2 = build :project_debit_summary, saldo: -50
      pds3 = build :project_debit_summary, saldo: -50
      pds4 = build :project_debit_summary, saldo: -30
      pds5 = build :project_debit_summary, saldo: -20

      subject = SummariesForInternalTransferManager.new(amount: 100, project_debit_summaries: [pds1, pds2, pds3, pds4, pds5])

      result = subject.summary_payment_options
      expect(result.map { |h| h[:notes_ids] }).to include [pds1.id]
    end
  end
end
