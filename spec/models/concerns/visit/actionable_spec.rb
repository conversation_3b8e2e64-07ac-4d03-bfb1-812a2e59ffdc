require 'rails_helper'

RSpec.describe VisitActionable do
  describe '#create_visit_action' do
    it 'creates start visit_action if visit_date is nil' do
      v = build :visit, visit_date: nil
      v.save validate: false

      expect_visit_has_action(v, :start)
    end

    it 'creates enter_costs visit_action if visit_date is not nil, state is PLANNED and no costs' do
      v = create :visit, visit_date: Time.new, state: Visit::PLANNED

      expect_visit_has_action(v, :enter_costs)
    end

    it 'creates confirm_costs visit_action if has costs but status is PLANNED' do
      v = build :visit, visit_date: Time.new, state: Visit::PLANNED
      allow(v).to receive(:has_any_costs?) { true }
      v.save!

      expect_visit_has_action(v, :confirm_costs)
    end

    it 'send_payment if visit has costs and visit_happened_checked and has visit_date' do
      v = build :visit, visit_date: Time.new, state: Visit::WAITING
      allow(v).to receive(:has_any_costs?) { true }
      v.save!

      expect_visit_has_action(v, :send_payment)
    end
  end
end
