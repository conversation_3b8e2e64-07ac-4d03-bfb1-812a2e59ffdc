require 'rails_helper'
require 'rspec'

RSpec.describe ClinicalUser do
  let(:project) { create :project }
  let(:active_center) { create :clinical_center, closed: false, clinical_center_code: '12312', project: project }
  let(:closed_center) { create :clinical_center, :closed, clinical_center_code: '98900', project: project, closed_by: 'Me' }
  let(:valid_acc_nr) { '30103000190109853000365273' }
  let(:clinical_user) { create :clinical_user }

  it 'has a valid factory' do
    clinical_user = build :clinical_user
    expect(clinical_user).to be_valid
  end

  describe "transfers_not_to_site" do
    it "returns users without transfers to sites" do
      cu1 = create :clinical_user
      cu2 = create :clinical_user
      cu3 = create :clinical_user
      create :clinical_transfer, clinical_user_id: cu1.id, transfered_to: 'clinical_center'
      create :clinical_transfer, clinical_user_id: cu3.id, transfered_to: nil
      ClinicalUser.where('id not in (?)', [cu1.id, cu2.id, cu3.id]).destroy_all

      expect(ClinicalUser.transfers_not_to_site.map(&:id).sort).to eq [cu2.id, cu3.id].sort
    end
  end

  describe 'reactivate' do
    let(:cu) { create :clinical_user, :closed }

    it 'reactivates' do
      expect { cu.reactivate }.to change { cu.closed }.from(true).to(false)
    end

    it 'charges fee reactivation and maintenance fee' do
      cu.created_at = 1.month.ago
      expect(Fee).to receive(:charge_for_patient_reactivation!).with(cu)
      expect(Fee).to receive(:charge_active_patient!).with(cu)
      cu.reactivate
    end

    it 'doesnt charge fee if patient was activated in the same month' do
      expect(Fee).not_to receive(:charge_for_patient_reactivation!).with(cu)
      cu.reactivate
    end

    # something is wrong here
    it 'doesnt charge fee if fee was collected this month already', :skip do
      cu.created_at = 1.month.ago
      Fee.charge_for_patient_reactivation!(cu)
      expect(Fee).not_to receive(:charge_for_patient_reactivation!).with(cu)
      cu.reactivate
    end

    it "doesnt charge fee if fee for new account was collected this month" do
      cu.created_at = 3.months.ago
      create :fee, clinical_user: cu, type_name: Fee::TYPE_NAMES['Patient']

      expect(Fee).not_to receive(:charge_for_patient_reactivation!).with(cu)
      cu.reactivate
    end

    it "doesnt charge fee if fee for account maintanance was collected this month" do
      cu.created_at = 3.months.ago
      create :fee, clinical_user: cu, type_name: Fee::TYPE_NAMES['Active patient']

      expect(Fee).not_to receive(:charge_for_patient_reactivation!).with(cu)
      cu.reactivate
    end
  end

  describe '#self.belonging_to_active_clinical_center' do
    it 'returns users who belong to not closed center' do
      user_in_open = create :clinical_user, clinical_center: active_center, project: project
      user_in_closed = create :clinical_user, clinical_center: closed_center, project: project
      expect(ClinicalUser.belonging_to_active_clinical_center).to eq [user_in_open]
    end
  end

  describe '#belonging_to_active_project' do
    it 'returns users belonging to active project' do
      active_project = create :project, closed: false
      closed_project = create :project, :closed
      active_cu = create :clinical_user, project: active_project
      closed_cu = create :clinical_user, project: closed_project
      expect(ClinicalUser.belonging_to_active_project).to eq [active_cu]
    end
  end

  describe '#self.chargable' do
    it 'returns users that can be charged fees' do
      user_in_open = create :clinical_user, clinical_center: active_center, project: project
      user_in_closed = create :clinical_user, clinical_center: closed_center, project: project
      deactivated_user_in_open = create :clinical_user, :closed, clinical_center: active_center, project: project
      user_without_project = build :clinical_user, project: nil
      user_without_project.save(validate: false)
      expect(ClinicalUser.chargable).to eq [user_in_open]
    end

    it 'doesnt include user beonging to closed projects' do
      active_project = create :project, closed: false
      closed_project = create :project, :closed
      active_cu = create :clinical_user, project: active_project
      closed_cu = create :clinical_user, project: closed_project
      expect(ClinicalUser.chargable).to eq [active_cu]
    end
  end

  describe '#active_for_in_months' do
    it 'returns 1M if cu was created in the given month' do
      cu = create :clinical_user
      expect(cu.active_for_in_months).to eq('1M')
    end

    it 'returns 3M if cu was created 3 months ago' do
      cu = create :clinical_user, created_at: 3.months.ago
      expect(cu.active_for_in_months).to eq('3M')
    end
  end

  # describe '#transfer_destination_matchers_account_number' do
  #   it 'is invalid if transfer_destination = bank_account but account_number is nil' do
  #     cu = build :clinical_user, account_number: nil, transfer_destination: 'bank_account'
  #     expect(cu).not_to be_valid
  #     expect(cu.errors.messages.keys).to eq [:transfer_destination]
  #   end

  #   it 'is invalid if transfer_destination = bank_account but account_number is 0' do
  #     cu = build :clinical_user, account_number: '0', transfer_destination: 'bank_account'
  #     expect(cu).not_to be_valid
  #     expect(cu.errors.messages.keys).to eq [:transfer_destination]
  #   end

  #   it 'is valid if transfer_destination = bank_account and account_number is a valid nr' do
  #     cu = build :clinical_user, account_number: valid_acc_nr, transfer_destination: 'bank_account'
  #     expect(cu).to be_valid
  #   end

  #   it 'is invalid if transfer_destination = post but account_number is nil' do
  #     cu = build :clinical_user, account_number: nil, transfer_destination: 'post'
  #     expect(cu).not_to be_valid
  #     expect(cu.errors.messages.keys).to eq [:transfer_destination]
  #   end

  #   it 'is invalid if transfer_destination = post but account_number is not 0' do
  #     cu = build :clinical_user, account_number: valid_acc_nr, transfer_destination: 'post'
  #     expect(cu).not_to be_valid
  #     expect(cu.errors.messages.keys).to eq [:transfer_destination]
  #   end

  #   it 'is valid if transfer_destination = post and account_number is 0' do
  #     cu = build :clinical_user, account_number: '0', transfer_destination: 'post'
  #     expect(cu).to be_valid
  #   end

  #   it 'is valid if transfer_destination = lmp_acc and account_number is nil' do
  #     cu = build :clinical_user, account_number: nil, transfer_destination: 'lmp_acc', perm_data: false
  #     expect(cu).to be_valid
  #   end

  #   it 'is invalid if transfer_destination = lmp_acc and account_number is valid account' do
  #     cu = build :clinical_user, account_number: valid_acc_nr, transfer_destination: 'lmp_acc'
  #     expect(cu).not_to be_valid
  #     expect(cu.errors.messages.keys).to eq [:transfer_destination]
  #   end

  #   it 'is invalid if transfer_destination = lmp_acc and account_number is 0' do
  #     cu = build :clinical_user, account_number: '0', transfer_destination: 'lmp_acc'
  #     expect(cu).not_to be_valid
  #     expect(cu.errors.messages.keys).to eq [:transfer_destination]
  #   end
  # end

  # describe '#transfer_destination_perm_data' do
  #   it 'is invalid if perm_data is false and transfer_destination is not lmp_acc' do
  #     cu = build :clinical_user, perm_data: false, transfer_destination: 'post', account_number: '0'
  #     expect(cu).not_to be_valid
  #   end

  #   it 'is invalid if perm_data is true and transfer_destination is lmp_acc' do
  #     cu = build :clinical_user, perm_data: true, transfer_destination: 'lmp_acc', account_number: nil
  #     expect(cu).not_to be_valid
  #   end

  #   {
  #     'post' => [true, '0'],
  #     'lmp_acc' => [false, nil],
  #     'bank_account' => [true, '30103000190109853000365273']
  #     }.each do |transfer_destination, values|
  #       it "is valid if perm_data is #{ values.first } and transfer_destination is #{ values.last }" do
  #         cu = build :clinical_user, perm_data: values.first, transfer_destination: transfer_destination, account_number: values.last
  #         expect(cu).to be_valid
  #       end
  #     end
  #   end

  describe '#closed_by' do
    it 'is required if closing the clinical_user' do
      expect(clinical_user).to be_valid
      clinical_user.closed = true
      expect(clinical_user).not_to be_valid
      expect(clinical_user.attribute_names).to include 'closed_by'
    end
  end

  describe '#not_test_project' do
    it 'returns clinical_users that dont belong to a test project' do
      test_project = create :project, test_project: true
      non_test_project = create :project, test_project: false
      c1 = create :clinical_user, project: test_project
      c2 = create :clinical_user, project: non_test_project
      expect(ClinicalUser.not_test_project).to eq [c2]
    end
  end

  describe '#update_unpaid_visits' do
    it 'updates unpaid visits\' state to unplanned if clinical_user is being closed' do
      v1 = create :visit, clinical_user: clinical_user, state: Visit::WAITING
      v2 = create :visit, clinical_user: clinical_user, state: Visit::SUCCESS
      clinical_user.closed = true
      clinical_user.closed_by = 'me'
      clinical_user.closed_reason = 'asd'
      clinical_user.save!
      expect(v1.reload.state).to eq Visit::UNPLANNED
      expect(v2.reload.state).to eq Visit::SUCCESS
    end
  end

  describe '#transfer_destination_post_has_address' do
    it 'is invalid if transfer_destination is post and city, street or zip_code is nil' do
      clinical_user = build :clinical_user, city: nil, street: 'asd', zip_code: 'asd', transfer_destination: 'post', account_number: '0'
      expect(clinical_user).not_to be_valid
      expect(clinical_user.attribute_names).to include 'transfer_destination'
    end

    it 'is valid if transfer_destination is post and city, street or zip_code are present' do
      clinical_user = build :clinical_user, city: 'waw', street: 'asd', zip_code: 'asd', transfer_destination: 'post', account_number: '0'
      expect(clinical_user).to be_valid
    end
  end

  describe '#active_or_closed_with_internal_transfers' do
    it 'returns active clinical_users or closed clinical_users with internal_transfers' do
      cu1 = create :clinical_user, closed: false
      cu2 = create :clinical_user, :closed
      cu3 = create :clinical_user, :closed
      t = build :clinical_transfer, clinical_user: cu3, account_number: nil
      t.save validate: false
      expect(ClinicalUser.active_or_closed_with_internal_transfers).to include(cu1, cu3)
    end
  end

  describe '#complete_bank_sec_code' do
    it 'adds zeros to the end of bank_account_sec_code if its present' do
      cu = build :clinical_user, bank_account_sec_code: '11'
      cu.valid?
      expect(cu.bank_account_sec_code).to eq '110000'
    end

    it 'doesnt modify bank_account_sec_code if its 6 chars long' do
      cu = build :clinical_user, bank_account_sec_code: '123456'
      cu.valid?
      expect(cu.bank_account_sec_code).to eq '123456'
    end
  end

  describe '#average_visit_cost' do
    it 'returns average cost of paid visits' do
      v1 = create :visit, clinical_user: clinical_user, state: 's'
      v2 = create :visit, clinical_user: clinical_user, state: 's'
      create :visit_payment_categorization, visit_id: v1.id, amount: 20
      create :visit_payment_categorization, visit_id: v2.id, amount: 10

      expect(clinical_user.average_visit_cost).to eq 15
    end
  end

  describe '#logged_in_within?' do
    it 'returns true if user logged in withing given nr of days' do
      cu = build :clinical_user, last_sign_in_at: 20.days.ago
      expect(cu.logged_in_within?(days: 30)).to eq true
    end

    it 'returns false if user didnt log in withing given nr of days' do
      cu = build :clinical_user, last_sign_in_at: 30.days.ago
      expect(cu.logged_in_within?(days: 20)).to eq false
    end

    it 'returns false if user never logged in' do
      cu = build :clinical_user, last_sign_in_at: nil
      expect(cu.logged_in_within?(days: 20)).to eq false
    end
  end

  describe '#unconfirmed_with_uploaded_form' do
    it 'returns unconfirmed clinical_users who have at least one clinical_user_form' do
      c1 = create :clinical_user, perm_data: false
      c2 = create :clinical_user, perm_data: true
      c3 = create :clinical_user, perm_data: false
      cuf = build :clinical_user_form, clinical_user_id: c3.id
      cuf.save validate: false
      expect(ClinicalUser.unconfirmed_with_uploaded_form).to eq [c3]
    end
  end

  describe 'unconfirmed' do
    it 'returns clinical_users with null data_confirmed_at' do
      c1 = create :clinical_user, data_confirmed_at: nil
      c2 = create :clinical_user, data_confirmed_at: Time.new, transfer_destination: 'post'

      expect(ClinicalUser.unconfirmed).to eq [c1]
    end

    it 'returns clinical_users with data_confirmed_at present and lmp transfer_destination' do
      c1 = create :clinical_user, data_confirmed_at: Time.new, transfer_destination: 'lmp_acc'
      c2 = create :clinical_user, data_confirmed_at: Time.new, transfer_destination: 'post'

      expect(ClinicalUser.unconfirmed).to eq [c1]
    end
  end

  describe '#with_no_internal_transfers' do
    it 'returns clinical_users with no processed interneal transfers' do
      cu1 = create :clinical_user, transfer_destination: 'lmp_acc', perm_data: false
      ct = create :clinical_transfer, account_number: nil, state: 'w', clinical_user_id: cu1.id

      cu2 = create :clinical_user, transfer_destination: 'lmp_acc', perm_data: false
      cd = create :clinical_transfer, account_number: nil, state: 'b', clinical_user_id: cu2.id
      result_ids = ClinicalUser.with_no_processed_internal_transfers.map(&:id)
      expect(result_ids).to include cu1.id
      expect(result_ids).not_to include cu2.id
    end
  end

  it '2 users can have the same phone_number' do
    cu1 = create :clinical_user, phone_number: '*********'
    cu2 = build :clinical_user, phone_number: '*********'
    expect(cu2).to be_valid
  end

  describe 'update_transfers_account_numbers' do
    it 'is called after changing account_number' do
      cu = create :clinical_user, account_number: '', transfer_destination: 'lmp_acc', perm_data: false
      expect(cu).to receive(:update_transfers_account_numbers)

      cu.account_number = valid_acc_nr
      cu.save!
    end

    it 'is called after changing transfer_destination' do
      cu = create :clinical_user, transfer_destination: 'lmp_acc'
      expect(cu).to receive(:update_transfers_account_numbers)

      cu.transfer_destination = 'bank_account'
      cu.save!
    end

    it 'updates transfers without acc nr' do
      cu = create :clinical_user, account_number: '', transfer_destination: 'lmp_acc', perm_data: false
      ct1 = build :clinical_transfer, clinical_user_id: cu.id, account_number: '', state: ClinicalTransfer::PROCESSING
      ct1.save! validate: false
      ct2 = build :clinical_transfer, clinical_user_id: cu.id, account_number: nil, state: ClinicalTransfer::PROCESSING
      ct2.save! validate: false

      cu.account_number = valid_acc_nr
      cu.transfer_destination = 'bank_account'
      cu.save!

      expect(ct1.reload.account_number).to eq(valid_acc_nr)
      expect(ct2.reload.account_number).to eq(valid_acc_nr)
    end
  end

  it 'is invalid without account_number' do
    cu1 = build :clinical_user, account_number: '', transfer_destination: 'bank_account'
    cu2 = build :clinical_user, account_number: '', transfer_destination: 'lmp_acc', perm_data: false
    cu3 = build :clinical_user

    expect(cu1).to_not be_valid
    expect(cu2).to be_valid
    expect(cu3).to be_valid
  end

  context 'perm_data' do
    it 'is invalid if perm_data is true and name is missing' do
      cu1 = build :clinical_user, perm_data: true, last_name: nil
      cu2 = build :clinical_user, perm_data: true, last_name: 'avc', first_name: 'asd'

      expect(cu1).to be_invalid
      expect(cu2).to be_valid
    end
  end

  describe 'patient_code' do
    it 'is uniq in project among undeleted patients' do
      code = '123'
      new_project = create :project
      cc = create :clinical_center, project_id: new_project.id
      create :clinical_user, patient_code: code, project_id: new_project.id, clinical_center_id: cc.id, deleted_at: Time.new
      diff_project_cu = build :clinical_user, patient_code: code, project_id: new_project.id, clinical_center_id: cc.id
      expect(diff_project_cu).to be_valid
    end

    it 'is uniq in project among patients' do
      code = '123'
      cu = create :clinical_user, patient_code: code, project_id: project.id, clinical_center_id: active_center.id
      new_cu = build :clinical_user, patient_code: code, project_id: project.id, clinical_center_id: active_center.id, account_type: cu.account_type
      expect(new_cu).not_to be_valid

      new_project = create :project
      cc = create :clinical_center, project_id: new_project.id
      diff_project_cu = build :clinical_user, patient_code: code, project_id: new_project.id, clinical_center_id: cc.id
      expect(diff_project_cu).to be_valid
    end
  end

  describe 'bank_account_sec_code' do
    it "is invalid on admin context" do
      cu = build :clinical_user, bank_account_sec_code: '123456'
      expect(cu).to be_valid
      expect(cu.valid?(:admin)).to eq false
    end
  end

  describe 'disable_modifications_on_closed_account' do
    it "allows modification if current researcher is operator" do
      researcher = create :researcher
      clinical_user = create :clinical_user, :closed

      Researcher.current = researcher
      expect(clinical_user).to_not be_valid

      create :project_role, project_role: 'Operator', researcher_id: researcher.id, project_id: clinical_user.project_id
      Researcher.current = Researcher.find(researcher.id)
      expect(clinical_user).to be_valid
    end
  end

  describe 'account_number' do
    describe 'clinical_user from non-PL site' do
      it "is not validated" do
        cc = create :clinical_center, country_name: 'Germany'
        cu = build :clinical_user, account_number: '123qwe', clinical_center_id: cc.id, project_id: cc.project_id, swift: '123'

        expect(cu).to be_valid
      end
    end

    describe 'clinical_user from PL site' do
      it "is validated" do
        cc = create :clinical_center, country_name: 'Poland'
        cu = build :clinical_user, account_number: '123qwe', clinical_center_id: cc.id, project_id: cc.project_id

        expect(cu).not_to be_valid
      end
    end
  end

  describe '#custody_acc_transfers_total' do
    it "returns 0 if user has no transfers" do
      cu = build :clinical_user

      expect(cu.custody_acc_transfers_total).to eq 0
    end

    context 'transfers as arguments' do
      it "returns clinical_user_balance_after from the first transaction" do
        cu = build :clinical_user
        tr = double('ClinicalTransfer', clinical_user_balance_after: 10, transfer_type: 'bla')

        expect(cu.custody_acc_transfers_total(all_transfers: [tr])).to eq 10
      end
    end

    it "returns sum of existing transfers" do
      cu = create :clinical_user
      tr = build :clinical_transfer, clinical_user: cu, amount: 10, account_number: nil, state: ClinicalTransfer::PROCESSING
      tr.save! validate: false

      expect(cu.custody_acc_transfers_total).to eq 10
    end
  end

  describe '#sec_code_valid?' do
    context 'clinical_user not in PL' do
      it "is valid if eq first 2 and last 4 chars of account_number" do
        clinical_user = build :clinical_user, account_number: 'CZ12345678', bank_account_sec_code: 'CZ5678'
        allow(clinical_user).to receive(:in_poland?) { false }

        expect(clinical_user.sec_code_valid?).to eq true
      end
    end
  end

  it "removes mask from phone number" do
    cu = build :clinical_user
    cu.phone_number = '(+48) 123-123-123'
    cu.valid?
    expect(cu.phone_number).to eq '+***********'
  end

  describe '#limit_per_patient_for_all_payments' do
    it 'returns projects limit_per_patient_for_all_payments' do
      project = create :project, limit_per_patient_for_all_payments: 100
      clinical_user = create :clinical_user, project: project, limit_per_patient_for_all_payments: nil

      expect(clinical_user.limit_per_patient_for_all_payments).to eq project.limit_per_patient_for_all_payments
    end

    it 'returns clinical_users limit_per_patient_for_all_payments' do
      project = create :project, limit_per_patient_for_all_payments: 100
      clinical_user = create :clinical_user, project: project, limit_per_patient_for_all_payments: 200

      expect(clinical_user.limit_per_patient_for_all_payments).to eq 200
    end
  end

  it 'is valid with existing patient_code but different type' do
    clinical_user = create :clinical_user
    new_user = clinical_user.dup
    new_user.account_type = create(:account_type)
    new_user.connected_account_label = 'hey'

    expect(new_user).to be_valid
  end

  it 'is valid with existing patient_code and non-default type' do
    clinical_user = create :clinical_user
    new_user = clinical_user.dup
    new_user.account_type = create(:account_type)

    expect(new_user).not_to be_valid
    new_user.connected_account_label = 'hey'

    expect(new_user).to be_valid
    new_user.save!

    another_user = new_user.dup
    expect(another_user).to be_valid
  end

  it 'assigns default account type' do
    clinical_user = build :clinical_user, account_type: nil

    expect(clinical_user).to be_valid
    expect(clinical_user.account_type).to be_present
  end
end
