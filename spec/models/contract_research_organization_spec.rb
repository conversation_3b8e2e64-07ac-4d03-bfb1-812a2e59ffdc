require 'rails_helper'
require 'rspec'

RSpec.describe Project do
  let(:cro) { create :contract_research_organization }

  it 'has a valid factory' do
    cro = build :contract_research_organization
    expect(cro).to be_valid
  end

  describe '#set_patient_reactivation_fee' do
    it 'sets patient_reactivation_fee' do
      cro = create :contract_research_organization, fee_patient_add: 10, patient_reactivation_fee: nil

      expect(cro.reload.patient_reactivation_fee).to eq cro.fee_patient_add
    end
  end

  describe 'dynamic processing_type scopes' do
    it 'generates copes for all processing_types' do
      cro = create :contract_research_organization, processing_type: 'standard'
      expect(ContractResearchOrganization.standard_processing).to eq [cro]
    end
  end

  describe 'dynamic invoice_type scopes' do
    it 'generates copes for all invoice_types' do
      cro = create :contract_research_organization, invoice_type: 'standard'
      expect(ContractResearchOrganization.standard_invoice).to eq [cro]
    end
  end

  describe '#get_active_account_number' do
    it 'gets the active account number' do
      cro = create :contract_research_organization, account_number: '2', active_account_number: 'pir_account_number'
      cro.pir_account_number = '1'
      expect(cro.get_active_account_number).to eq '1'
    end
  end

  describe '#copy_main_visit_payment_categories' do
    it 'creates visit_payment_categories based on main visit_payment_categories' do
      vpc = create :visit_payment_category
      cro = create :contract_research_organization
      expect(cro.reload.visit_payment_categories.count).to eq 1
      expect(cro.reload.visit_payment_categories.first.base_visit_payment_category_id).to eq vpc.id
    end

    it 'doesnt create a copy if one already exisits' do
      vpc = create :visit_payment_category
      cro = create :contract_research_organization
      cro.copy_main_visit_payment_categories
      expect(cro.reload.visit_payment_categories.count).to eq 1
      expect(cro.reload.visit_payment_categories.first.base_visit_payment_category_id).to eq vpc.id
    end
  end

  describe '#has_copy_of_visit_payment_category?' do
    it 'is true if cro has a visit_payment_category that is a copy of another visit_payment_category' do
      base_vpc = create :visit_payment_category, is_main: true
      expect(cro.visit_payment_categories.count).to eq 1
      expect(cro.has_copy_of_visit_payment_category?(base_vpc)).to be_truthy
    end

    it 'is false if cro doesnt have a visit_payment_category that is a copy of another visit_payment_category' do
      base_vpc = create :visit_payment_category, is_main: true
      cro.visit_payment_categories.delete_all
      expect(cro.has_copy_of_visit_payment_category?(base_vpc)).to be_falsey
    end
  end

  describe '#transfer_title' do
    it 'must be on of predefined values in TRANSFER_TITLES' do
      cro.transfer_title = 0
      expect(cro).to be_valid
      cro.transfer_title = 200
      expect(cro).not_to be_valid
    end
  end

  describe '#super_managers' do
    it 'returns researchers with cro_role in the cro' do
      cro_role = create :cro_role, contract_research_organization_id: cro.id

      expect(cro.super_managers).to eq [cro_role.researcher]
    end
  end

  describe '#cras_not_logged_in_within' do
    it 'returns cras that havent logged in within given time or are not confirmed, sorting: first unconfirmed, then latest sign in' do
      p1 = create :project, contract_research_organization_id: cro.id
      r1 = create :researcher, data_confirmed_at: 1.day.ago, last_sign_in_at: 1.year.ago
      create :project_role, researcher: r1, project: p1, project_role: 'CRA'

      r2 = create :researcher, data_confirmed_at: nil, last_sign_in_at: 1.year.ago
      create :project_role, researcher: r2, project: p1, project_role: 'CRA+'

      r3 = create :researcher, data_confirmed_at: 1.day.ago, last_sign_in_at: 7.month.ago
      create :project_role, researcher: r3, project: p1, project_role: 'CRA+'

      r4 = create :researcher, data_confirmed_at: 1.day.ago, last_sign_in_at: 7.days.ago
      create :project_role, researcher: r4, project: p1, project_role: 'CRA+'

      result = cro.cras_not_logged_in_within
      expect(result.map(&:id)).to eq [r2.id, r1.id, r3.id]
    end
  end

  describe '.auto_sign_debits' do
    it 'returns CRO with auto_sign_debits true' do
      cro_auto = create :contract_research_organization, auto_sign_debits: true
      cro_not_auto = create :contract_research_organization, auto_sign_debits: false

      expect(ContractResearchOrganization.auto_sign_debits).to eq [cro_auto]
    end
  end

  describe 'vat_rate' do
    it 'returns rate based on company_type' do
      pl_cro = ContractResearchOrganization.new(company_type: 'polish')
      expect(pl_cro.vat_rate).to eq 23

      fr_cro = ContractResearchOrganization.new(company_type: 'foreign')
      expect(fr_cro.vat_rate).to eq 0
    end
  end

  describe "account_number" do
    it "is equal to first currency account number" do
      expect(cro.reload.account_number).to_not be_nil
      expect(cro.account_number).to eq cro.currency_accounts.first.account_number
    end
  end

  describe "create_credit_currency_account" do
    it "creates credit currency account" do
      cro

      expect(CurrencyAccount::ForCroCredit.count).to eq 3
    end
  end

  it 'touches projects after save' do
    pr = create :project, contract_research_organization: cro

    expect { cro.update(name: 'bla') }.to change { pr.reload.updated_at }
  end
end
