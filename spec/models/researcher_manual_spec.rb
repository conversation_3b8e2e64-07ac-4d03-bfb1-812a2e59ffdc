require 'rails_helper'

RSpec.describe ResearcherManual, :type => :model do
  let(:researcher) { create :researcher }
  let(:rm_for_all) { create :researcher_manual, for_manager: true, for_cra: true, for_inv: true }
  let(:rm_for_managers) { create :researcher_manual, for_manager: true }
  let(:rm_for_cra_m) { create :researcher_manual, for_manager: true, for_cra: true }
  let(:cro) { create :contract_research_organization }
  let(:cro_project) { create :project, contract_research_organization: cro }

  it 'has a valid factory' do
    rm = build :researcher_manual
    expect(rm).to be_valid
  end

  describe 'self.accessible_to' do
    it 'returns accessible manuals' do
      rm = create :researcher_manual
      create :researcher_manual_role, researcher_manual: rm, contract_research_organization: cro, role: 'CRA'
      create :project_role, researcher: researcher, project_role: 'CRA', project: cro_project

      unavailable_rm = create :researcher_manual
      create :researcher_manual_role, researcher_manual: rm, role: 'CRA'

      expect(ResearcherManual.accessible_to(researcher)).to eq [rm]
    end

    it 'returns nothing' do
      rm = create :researcher_manual
      create :researcher_manual_role, researcher_manual: rm, contract_research_organization: cro, role: 'CRA'

      expect(ResearcherManual.accessible_to(researcher).none?).to eq true
    end
  end
end
