require 'rails_helper'

RSpec.describe Citibank::CsvReport::CsvParser do
  let(:file) { Rack::Test::UploadedFile.new(File.join(Rails.root, 'spec', 'support', 'csv', 'citi_parser_csv.csv'), 'csv') }
  let(:subject) { Citibank::CsvReport::CsvParser.new(file: file, dkp: 1, citi_type: 'bla') }

  describe "#rows" do
    it "returns rows from original CSV file with updated info" do
      expected = ['expected']

      allow(Citibank::CsvReport::RowParser).to receive_message_chain(:new, :updated_row) { expected }

      expect(subject.rows).to eq [expected]
    end
  end
end
