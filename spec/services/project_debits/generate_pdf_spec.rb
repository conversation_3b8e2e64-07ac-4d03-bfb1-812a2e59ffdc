require 'rails_helper'

RSpec.describe ProjectDebits::GeneratePdf do
  describe '#call' do
    it "generates project_debit_signatures" do
      pd = create :project_debit
      visit = create :visit, transfer_approved_by_researcher_id: create(:researcher).id
      ct = create :clinical_transfer, project_debit_id: pd.id, visit_id: visit.id

      expect { ProjectDebits::GeneratePdf.call(project_debit: pd) }.to change { pd.project_debit_signatures.count }.by(1)
    end
  end
end