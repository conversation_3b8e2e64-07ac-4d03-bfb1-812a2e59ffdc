require 'rails_helper'

RSpec.describe ClinicalTransfers::CreateOutgoingTransferFromReturnTransfer do
  describe 'call' do
    it 'creates outgoing transfer' do
      return_transfer = return_transfer = create :clinical_transfer, transfer_type: :return_transfer, flow_direction: ClinicalTransfer::INCOMING

      described_class.call(clinical_transfer: return_transfer)

      expect(ClinicalTransfer.count).to eq 2
      expect(ClinicalTransfer.order('created_at desc').first.flow_direction).to eq ClinicalTransfer::OUTGOING
    end
  end
end
