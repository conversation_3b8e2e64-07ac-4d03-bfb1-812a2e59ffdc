require 'rails_helper'

RSpec.describe GenerateBulkInvoice do
  let(:cro) { create :contract_research_organization, bulk_invoice: true }

  describe '#call' do
    it 'generates bulk invoice' do
      expect(BulkInvoices::GenerateXlsJob).to receive_message_chain(:new, :perform)

      service = GenerateBulkInvoice.new(cro: cro, start_date: Date.today, end_date: Date.tomorrow)
      service.call

      expect(BulkInvoice.count).to eq 1
    end

    it 'preserves id' do
      date_from = Date.today
      date_to = Date.tomorrow

      old_invoice = create :bulk_invoice, created_for_from: date_from, created_for_to: date_to, contract_research_organization: cro
      service = GenerateBulkInvoice.new(cro: cro, start_date: date_from, end_date: date_to)
      service.call

      expect(BulkInvoice.count).to eq 1
      expect(BulkInvoice.first.id).to eq old_invoice.id
    end

    it 'saves summary_report' do
      service = GenerateBulkInvoice.new(cro: cro, start_date: Date.today, end_date: Date.tomorrow, order_number: 3)
      service.call

      bi = BulkInvoice.last
      expect(bi.summary_file_path).to be_present
      expect(bi.summary_file_path).to include('_SUMMARY.xlsx')
    end

    context 'with order_number' do
      it 'generates bulk invoice with order_number' do
        service = GenerateBulkInvoice.new(cro: cro, start_date: Date.today, end_date: Date.tomorrow, order_number: 3)
        service.call

        expect(BulkInvoice.count).to eq 1
        expect(BulkInvoice.first.number).to include '0003'
      end
    end
  end
end
