require 'rails_helper'

RSpec.describe ProjectDebitSummaries::GenerateBulkPdf do
  let(:pds) { create :project_debit_summary, file_path: nil }
  let(:pd) { create :project_debit, project_debit_summary_id: nil }

  describe '#call' do
    it "generates pdf" do
      ProjectDebitSummaries::GenerateBulkPdf.new(project_debit_summary: pds, project_debits: [pd]).call

      expect(pd.reload.project_debit_summary_id).to eq pds.id
    end
  end
end