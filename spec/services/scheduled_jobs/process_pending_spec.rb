require 'rails_helper'

RSpec.describe ScheduledJobs::ProcessPending do
  describe "#call" do
    it "processes jobs" do
      create :scheduled_job, state: 'pending', command: 'Project.count'

      expect(Project).to receive(:count)
      ScheduledJobs::ProcessPending.call
    end

    it "does not process jobs that started" do
      job = create :scheduled_job, state: 'pending', command: 'Project.count', started_at: Time.new

      expect(Project).to_not receive(:count)
      ScheduledJobs::ProcessPending.new(pending_jobs: [job]).call
    end

    it "updates job if there is an error" do
      job = create :scheduled_job, state: 'pending', command: "raise('bla')"

      ScheduledJobs::ProcessPending.new(pending_jobs: [job]).call

      expect(job.reload.state).to eq 'failed'
      expect(job.error).to eq 'bla'
    end
  end
end