require 'rails_helper'
require 'rspec'

RSpec.describe AutoSignDebits do
  it 'signs debits' do
    cro = create :contract_research_organization, auto_sign_debits: true, auto_sign_debit_period: 14
    pr = create :project, contract_research_organization_id: cro.id
    pd = create :project_debit, status: ProjectDebit::WAITING, project_id: pr.id
    pds = create :project_debit_signature, project_debit_id: pd.id, signature_hash: '123', signature_date: nil, created_at: 14.days.ago

    expect_any_instance_of(ProjectDebitSignature).to receive(:sign_document).with(hash_including(auto_signed: true))

    AutoSignDebits.new.call
  end

  it 'signs debits' do
    cro = create :contract_research_organization, auto_sign_debits: true, auto_sign_debit_period: 7
    pr = create :project, contract_research_organization_id: cro.id
    pd = create :project_debit, status: ProjectDebit::WAITING, project_id: pr.id
    pds = create :project_debit_signature, project_debit_id: pd.id, signature_hash: '123', signature_date: nil, created_at: 3.days.ago

    expect_any_instance_of(ProjectDebitSignature).not_to receive(:sign_document)

    AutoSignDebits.new.call
  end

  it 'signs debits' do
    cro = create :contract_research_organization, auto_sign_debits: true, auto_sign_debit_period: 7
    pr = create :project, contract_research_organization_id: cro.id
    pd = create :project_debit, status: ProjectDebit::WAITING, project_id: pr.id
    pds = create :project_debit_signature, project_debit_id: pd.id, signature_hash: '123', signature_date: nil, created_at: 8.days.ago

    expect_any_instance_of(ProjectDebitSignature).to receive(:sign_document).with(hash_including(auto_signed: true))

    AutoSignDebits.new.call
  end
end
