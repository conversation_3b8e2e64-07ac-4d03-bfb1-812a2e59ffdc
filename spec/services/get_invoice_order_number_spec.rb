require 'rails_helper'
require 'rspec'

RSpec.describe GetInvoiceOrderNumber do
  let(:today) { Date.today }
  describe '#call' do
    it 'return 1 if there are no invoice for givern created_for' do
      expect(GetInvoiceOrderNumber.call(created_for_to: today)).to eq 1
    end

    it 'returns 1 if there are other invoices but their order number is higher than 1' do
      invoice = build :invoice, created_for_to: today, number: "0019-0817/0002/0047", project_id: nil
      invoice.save!(validate: false)
      expect(GetInvoiceOrderNumber.call(created_for_to: today)).to eq 20
    end

    it 'returns last order_number + 1 ' do
      invoice = build :invoice, created_for_to: today, number: "0001-0817/0002/0047", project_id: nil
      invoice.save!(validate: false)
      expect(GetInvoiceOrderNumber.call(created_for_to: today)).to eq 2
    end

    it 'returns the nr skipped' do
      invoice = build :invoice, created_for_to: today, number: "0001-0817/0002/0047", project_id: nil
      invoice.save!(validate: false)
      invoice = build :invoice, created_for_to: today, number: "0003-0817/0002/0047", project_id: nil
      invoice.save!(validate: false)
      expect(GetInvoiceOrderNumber.call(created_for_to: today)).to eq 4
    end

    context 'order_number with letter' do
      it 'returns last order_number + 1 ' do
        invoice = build :invoice, created_for_to: today, number: "F0001-0817/0002/0047", project_id: nil
        invoice.save!(validate: false)
        expect(GetInvoiceOrderNumber.call(created_for_to: today)).to eq 2
      end
    end
  end
end
