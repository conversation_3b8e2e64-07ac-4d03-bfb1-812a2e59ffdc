job_type :rake, "{ cd /home/<USER>/app/clinical/production/current > /dev/null; } && RAILS_ENV=:environment bundle exec rake :task --silent :output"
job_type :script, "{ cd /home/<USER>/app/clinical/production/current > /dev/null; } && RAILS_ENV=:environment bundle exec script/:task :output"
job_type :runner, "{ cd /home/<USER>/app/clinical/production/current > /dev/null; } && RAILS_ENV=:environment bundle exec rails runner ':task' :output"

set :job_template, "bash -l -c 'source /home/<USER>/.rvm/environments/ruby-3.3.5@clinical_production && :job'"

every :day, at: '5:00am' do
  runner "Fee.monthly_charge_uncharged_patients", output: "log/fees.log"
end

every :day, at: '16:00' do
  runner  'ClinicalTransfersReport.cron_send_transfers_confirmation_per_clinical_center', output: 'log/cron_transfer_confirmations.log'
end

every :day, at: '12:30' do
  runner  'Project.find_projects_with_low_saldo_and_send_notifications', output: 'log/projects_with_low_saldo_notification.log'
end

# Testowy sms o 8 rano do DJ
every :day, at: '8:00am' do
  runner 'ClinicalMobileMessage.send_test_message_cron', output: "log/test_sms.log"
  runner 'SendUnresolvedTransfersReport.new.call'
  runner 'ClinicalMailer.with({}).send_closed_pd_report.deliver'
  runner 'AutoSignDebits.new.call', output: "log/auto_sign_debits.log"
  runner 'CheckShipmentProcessing.new.call'
end

every :day, at: '8:10am' do
  runner 'SmsQueue.send_queued_messages', output: "log/queued_sms.log"
end

every 5.minutes do
  runner 'SmsQueue.send_failed_messages', output: "log/send_failed_sms.log"
end

# Notyfikacje o liczbie zestawień:
# closed_and_opened_debits_email_stats
every [:wednesday, :thursday], at: '11:00am' do
  runner "ProjectDebit.closed_and_opened_debits_email_stats", output: 'log/debit_notes_notification.log'
end

every :monday, :at => '01:00' do
  runner 'SendUnsignedSignaturesReminders.new.call', output: "log/send_unsigned_signatures_reminders.log"
end

every :day, :at => '23:59' do
  runner "ProjectDebitsCron.close_project_debits", output: "log/cron_project_debit.log"
  runner "ResetDemoProjects.new.call"
end

every :day, :at => '23:55' do
  runner 'SendLimitedWaitingTransfersEmailPerProject.new.call', output: 'log/send_waiting_transfers_email_per_project.log'
end

# kazdego 1 miesiąca, o 8:00 raport z podsumowaniem przelwow in i out.
every '0 8 1 * *' do
  runner "ResearcherMailer.with({}).send_project_personnel_report.deliver", output: "log/send_project_personnel_report.log"
  runner "SendNoLoginResearchersReport.new.call"
  runner 'Cros::CreateBulkInvoices.call', output: 'log/cros/create_bulk_invoices.log'
end

every '0 1 1 * *' do
  runner 'GenerateProjectDebitsAndInvoices.new.call', output: "log/generate_pds_and_invoices.log"
  runner 'GenerateProjectStatements.new.call', output: 'log/project_statements.log'
  runner 'Cros::FindBestResearchers.call'
end

every '0 2 1 * *' do
  runner 'Fees::ChargeForSites.call', output: 'log/charge_for_sites.log'
  runner 'Fees::ChargePremiumProjects.call', output: 'log/charge_premium_projects.log'
end

every '0 3 1 * *' do
  runner 'AuditSystem.call', output: 'log/audit_system.log'
end

every '33 7 1 * *' do
  runner "Project.generate_project_reports", output: "log/generate_project_reports.log"
end

every :day, at: '11:59pm' do
  runner 'SendSessionHistoryReport.new.call', output: 'log/send_session_history_report.log'
end

every :day, at: '08:00' do
  runner "ResendInvitationsToResearchers.new.call"
end

every :day, at: '13:00' do
  runner "Currencies::UpdateRates.new.call", output: 'log/currencies_update_rates.log'
end

every 10.minutes do
  runner "ProcessShipments.new.call", output: "log/process_shipments.log"
end

every 2.minutes do
  runner 'ScheduledJobs::ProcessPending.call', output: "log/process_pending_jobs.log"
end

every :day, at: '08:05, 12:35, 15:35, 18:35, 21:05' do
  runner 'Alior::BankConnect::DownloadFiles.call', output: "log/download_alior_files.log"
end

every :day, at: '03:00' do
  runner 'Researchers::DeactivateForInactivity.call'
end

every :day, at: '01:00' do
  runner 'Projects::CreateAndSendLowBalancePrepaidNote.call'
  runner 'ClinicalCenterRoles::DeleteOutdated.call'
end