pl:
  activerecord:
    errors:
      models:
        project:
          attributes:
            saldo:
              greater_than_or_equal_to: Na koncie projektu nie ma wystar<PERSON><PERSON><PERSON><PERSON>ch środków, aby wykonać przelew. Proszę o kontakt z osobą odpowiedzialną za finansowanie badania.
        researcher:
          attributes:
            email:
              not_found: "<PERSON><PERSON><PERSON> adres email nie został odnaleziony."

    models:
      account_type: Typ konta
      corrective_note: Nota uznaniowa
      fee_plan_rule_enablement: Polaczenia plan-funkcja
      km_country_rate: Stawka KM
      training: Realizacja szkolenia
      training_template: Standardowe szkolenie
      survey/question: Pytanie
      survey/answer: Odpowiedź
      survey: Ankieta
      label: Etykieta
      recommendation: Rekomendacja review
      researcher_training: Sz<PERSON><PERSON><PERSON> badacza
      complaint: Reklamacje i skargi
      security_incident: Operational Incident
      fee_plan_rule: Funkcje planów
      sla_activity: SLA Activity
      note_correction: Noty uznaniowe z tytulu anulowanych prezelewow w postpaid
      action_reason: Tekst Action Required
      user_action_requirement: Action required pacjentów
      admin_task: Zadania
      post_transfer: Przekaz pocztowy
      lmp_setting: Ustawienia LMP
      sponsor: Sponsor
      sponsor_representant: Osoba reprezentująca sponsora
      user: Użytkownik
      users: Użytkownicy
      clinical_user: uczestnik
      clinical_users: uczestnicy
      visit: wizyta
      visits: wizyty
      project: projekt
      projects: projekty
      researcher: badacz
      researchers: badacze
      clinical_center: ośrodek
      clinical_centers: ośrodki
      clinical_transfer: przelew
      clinical_transfers: przelewy
      project_role: Rola badacza w projekcie
      project_roles: Rola badacza w projekcie
      clinical_center_role: Rola badacza w ośrodku
      clinical_center_roles: Rola badacza w ośrodku
      clinical_user_inquiries: Zapytania pacjentów
      clinical_user_account_activity: Historia akcji na koncie pacjenta
      visit_payment_category: Kategoria płatności
      invoice: Faktura
      project_invoice: Faktura projektu
      bulk_invoice: Faktura zbiorcza
      fee: Opłata
      currency_rate: Kurs walut
      admin_user: Administrator
      comment: Komentarz
      project_visit_template: Szablon wizyt
      project_debit_summary: Nota obciazeniowa
      clinical_user_form: Formularz danych pacjenta
      currency_exchange: Wymiana waluty
      project_return_funds_note: Nota uznaniowa z tytulu zwroty srodkow z kont prepaid
      researcher_manual_role: Rola CRO
    attributes:
      km_country_rate:
        country_name: Kraj
        amount: Stawka
      training_template:
        name: Nazwa
        code: Kod
      training:
        training_template: Standardowe szkolenie
        date: Data
        online_link: Link do szkolenia online
        done: Zrealizowane
      label:
        color:
          top_enroller: zielony
          Gold: pomaranczowy
          red: czerwony
          Silver: szary
          Limited: niebieski
          one: Kolor
      change_log:
        sent: Wyslany
      cost_summary:
        start_balance: Saldo na poczatek
        end_balance: Saldo na koniec
        incomes_total: Kwota zasilen
        outcomes_total: Kwota obciazen
      bank_account:
        number: Numer konta
        currency: Waluta
      internal_transfer:
        amount: Kwota
      currency_exchange:
        real_exchange_date: data rzeczywistego przewalutowania
        real_total: rzeczywista kwota docelowa
        real_amount: rzeczywista kwota zrodlowa
        success: czy transkacja walutowa zakonczona
        amount: Kwota początkowa
        total: Kwota końcowa
        base_currency: Waluta z
        target_currency: Waluta docelowa
        spread_rate: Spread
        currency_rate: Kurs
      calls_and_meeting:
        status: Status
        status/planned: Zaplanowany
        status/cancelled: Anulowany
        status/done: Ukończony
      complaint:
        filing_date: Data złożenia
        resolve_date: Data rozwiązania
      security_incident:
        risk_category_text: Kategoria
        risk_category: Kategoria
        loss_amount: Rzeczywista strata
        expected_loss: Przewidywana strata
        added_by_email: Osoba zgłaszająca
        state: Status
        title: Nazwa incydentu
        body: "Opis incydentu"
        filing_date: "Data wykrycia"
        sent_to_knf: Raport przeslany do KNF?
        resolve_date: "Data rozwiązania"
        incident_type: "Rodzaj incydentu"
        cause_of_incident: "Przyczyna wystąpienia"
        detection_date: "Data wystąpienia"
        affected_countries: "Kraje objęte skutkami incydentu"
        primary_contact_person: "Podstawowa osoba kontaktowa"
        additional_contact_person: "Dodatkowa osoba kontaktowa"
        current_state: "Status incydentu"
        general_impact: "Ogólny wpływ"
        affected_payment_services: "Usługi płatnicze objęte skutkami incydentu"
        affected_transactions_percentage: "% Transakcji objętych skutkami incydentu"
        affected_users_percentage: "% Użytkowników usług płatniczych objętych skutkami incydentu"
        economical_impact: "Wpływ ekonomiczny"
        corrective_actions: "Jakie czynności/środki zostały podjęte dotychczas lub są planowane w celu przywrócenia sytuacji po nastąpieniu incydentu?"
        escalation: "Przekazanie na wyższy szczebel"
        others_affected: "Inni DUP lub określona infrastruktura, potencjalnie objęci skutkami incydentu"
      workload:
        time: Time in mins
      user_action_requirement:
        clinical_center: Site
      message:
        cc_to: CC (oddzielone przecinkiem)
        clinical_center_id: Ośrodek
        project_id: Badanie
        sender_id: Od
        receiver_id: Do
        clinical_user_id: Pacjent
      clinical_center_message:
        send_to_researcher_ids: Send to
        cc_to_researcher_ids: CC to
      available_project_patient_form:
        available_cro_patient_form: Formularz przypisany do CRO
        real_patient_form: Formularz pacjenta
      admin_task:
        researcher: Badacz
        clinical_user: Pacjent
        project: Badanie
        contract_research_organization: CRO
        description: Opis zadania
        finished_at: Wykonane dnia
        clinical_center: Ośrodek
      visit_payment_category:
        name_pl: Nazwa PL
        name_en: Nazwa EN
        abbr: Skrót
        visible: Widoczność

      visit_note:
        body: Treść
      fee:
        created_at: Za miesiąc
        type_names:
          r: Dodanie badacza
          p: Dodanie pacjenta
          a: Utrzymanie pacjenta
          t: Zlecenie przelewu
          m: Przekaz pocztowy
          q: Projekt premium
          z: Reaktywacja pacjenta
          s: Start projektu
          c: Wprowadzenie danych
          w: Utrzymanie ośrodka
          x: Minimalna liczba pacjentów
      clinical_user_inquiry:
        message: Wiadomość

      correspondence:
        comments: Komentarz
        delivery_type: Rodzaj przesyłki
        received_date: Data otrzymania
        state: Status
      post_transfer:
        received_at: Odebrane dnia
        invoice_id: Faktura
        invoiced: Zrefakturowany
        amount: Kwota
        cost: Koszt
        order_date: Data zlecenia
        transfer_number: ID przekazu
        created_at: Data zarejestrowania
        received_by_patient_on: Odebrany przez pacjenta dnia
      invoice:
        projection_sent_at: Projekt kwoty wysłany
        is_valid: Zweryfikowana
        gross_total_amount: Kwota brutto
        created_for: Miesiąc wystawienia
      currency_rate:
        base_currency: Waluta bazowa
        target_currency: Waluta docelowa
      lmp_setting:
        invoice_acc_nr: Numer konta dla faktur
      clinical_center_role:
        valid_to: Termin waznosci
        sp_admin: Administrator Site Payments
        clinical_center: Ośrodek
        clinical_center_id: Ośrodek

      user:
        email: Adres email
        password: Hasło
        password_confirmation: Potwierdzenie hasła
        remember_me: Zapamiętaj mnie
        phone_number: Numer telefonu
        clinical_center_id: Ośrodek
        project_id: Projekt

      sponsor:
        email: Adres email
        password: Hasło
        password_confirmation: Potwierdzenie hasła
        remember_me: Zapamiętaj mnie
        phone_number: Nr telefonu
        screen_name: Nazwa sponsora
        name: Nazwa
        city: Miasto
        street: Ulica
        zip_code: Kod pocztowy
        state: Województwo
        fax: Fax
        tax_id_1: NIP
        tax_id_2: Regon
        business_entity: Forma organizacyjna
        tax_id_3: Numer KRS lub numer ewidencyjny
        created_at: Data utworzenia rekordu
      project_debit_summary:
        credited_to: Kredyt udzielony do
        credit_source: Żródło kredytu
        credit_payment_type: Sposób spłaty kredytu
        credit_application: Wniosek o kredyt
        credited_by_researcher_id: Wnioskodawca o kredyt
        credit_paid_at: Kredyt spłacony
        created_at: Data wygenerowania
        sent_at: Data wysłania
        paid_at: Data opłacenia
        credited_at: Data skredytowania
        states:
          c: wygenerowana
          s: Nota wysłana
          p: opłacona
          cancelled: anulowana
          z: anulowana
      project:
        investigator_info: Informacja dla investigators
        cra_managers_info: Informacja dla cra/managers
        future_visits_allowed: Czy mozna wpisywac wizyty z przyszla data?
        fee_plan_cost: Koszt planu
        auto_credit_debit_summaries: Automatyczne kredytowanie not obciazeniowych
        site_cost_rec: Cost recommendations for sites
        cra_cost_rec: Cost recommendations for CRAs
        manager_cost_rec: Cost recommendations for Managers
        allow_visit_payments_with_costs_above_limits: Zezwalaj na platnosci z kosztami powyzej limitow
        po: PO dla faktur
        note_po: PO dla not
        noblewell_project: Czy to projekt wlasny/Noblewell
        auto_generate_notes_when_balance_low: automatyczne wystawianie not jezeli saldo spadnie ponizej limitu
        low_balance_notification_limit: Kwota limitu ponizej ktorego wysylamy alert
        site_reimbursement_currency: Waluta wynagrodzenia badaczy i ośrodków
        citi_transfer_fee: Oplata za wprowadzenie danych za wizyte
        plan_fee_type: Typ opłat premium
        patient_reactivation_fee: Opłata za reaktywację pacjenta
        one_min_rate: Opłata za minutę workload
        site_fee: Pakiet wsparcia Premium dla ośrodków
        premium_plan_fee: Pakiet wsparcia SLA Silver flat rate / per patient
        super_premium_plan_fee: Pakiet wsparcia SLA Gold flat rate / per patient
        platinum_plan_fee: Pakiet wsparcia SLA Platinum flat rate / per patient
        delete_fees_created_after_closed_at: Skasuj oplaty za projekt pobrane po dniu zamknięcia
        close_patient_after_last_transfer: Czy zamykac konto pacjenta po ostatnim przelewie?
        auth_approver_cc: cc email do Authorized approver (oddzielone spacja)
        send_app_req: Czy wysylac emaile APPROVAL REQUIRED w tym projekcie?
        country_name: Kraj
        fee_plan: Plan ekskluzywny
        premium_free_to: Trial period
        send_email_after_confirm_cu: Wysyłaj email do badacza po aktywowaniu konta pacjenta
        transfer_limit: Limit max przelewu
        administrator_id: Opiekun
        auto_accept_over_limit_transfer: Automatyczna akceptacja przelewu przekraczającego limit
        visit_amount_difference_alert_limit: Limit różnicy kosztu wizyty dla alertu
        clinical_protocol_code: Kod protokołu badania klinicznego
        name: Nazwa sponsora
        saldo: Saldo projektu
        send_login_report: Wysyłać raport logowań?
        notify_investigator: 'Wyslij notyfikacje do Investigator po dodaniu nowego pacjenta'
        notify_cra: 'Wyslij notyfikacje do CRA po dodaniu nowego pacjenta'
        notify_manager: 'Wyslij notyfikacje do Manager po dodaniu nowego pacjenta'
        currency: 'Waluta'
        fee_currency: 'Waluta do faktur'
        summary_locale: 'Jezyk noty obciazeniowej'
        locale: 'Język'
        account_number: Dodatkowy numer rachunku
        pir_account_number: Numer PIR
        active_account_number: Aktywny numer rachunku
        closed: 'Zdezaktywowany'
        closed_at: "Zdezaktywowany o"
        closed_by: Zdezaktywowany przez
        closed_reason: Powód deaktywacji
        summary_per_cc: wystawianie not obciążeniowych dla każdego ośrodka oddzielnie
        fee_patient_add: Opłata za dodanie pacjenta
        fee_active_patient: Opłata za utrzymanie konta pacjenta
        tier_1_transfer_fee: Opłata za przelew do krajów grupy 1 (tańsze)
        tier_2_transfer_fee: Opłata za przelew do krajów grupy 1 (droższe)
        fee_post_transfer: Opłata za przekaz pocztowy
      clinical_user:
        account_type: Typ konta
        transfer_destination: transfer destination
        bank_account_sec_code: kod bezpieczeństwa banku
        correct_acc_nr: Prawidłowe numery kont sprawdzone przez badaczy
        correct_acc_nr_raw: Prawidłowe numery kont sprawdzone przez badaczy
        reason_for_action: Reason for action required
        sms_notifications: Wyrażam zgodę na otrzymywanie powiadomień SMS o zleconych płatnościach oraz o logowaniu.
        email_notifications: Wyrażam zgodę na otrzymywanie powiadomień email o zleconych płatnościach oraz o logowaniu.
        email: Email
        transfer_limit: Limit max przelewu
        provided_data_form_type: Na podstawie formularza
        juvenile: Nieletni
        parent_first_name: Imię rodzica
        parent_last_name: Nazwisko rodzica
        parent_phone_number: Telefon rodzica
        data_confirmed_at: Dane potwierdzone dnia
        first_name: Imię
        last_name: Nazwisko
        street: Ulica
        house_number: Numer domu
        room_number: Numer lokalu
        city: Miasto
        zip_code: Kod pocztowy
        second_phone_number: Drugi numer telefonu
        account_number: Numer konta
        comments: Komentarz
        transfers_to_clinical_center: Przelew do ośrodka
        patient_code: Numer pacjenta
        phone_number: Telefon
        password: Hasło
        current_password: Obecne hasło
        closed: 'Zdezaktywowany'
        closed_at: "Zdezaktywowany o"
        closed_by: Zdezaktywowany przez
        closed_reason: Powód deaktywacji
        perm_data: Potwierdzone dane
        should_sign_new_form: Czy pacjent ma podpisac nowy formularz?
        password_confirmation: 'Potwierdź hasło'
        sex:
          male: Mężczyzna
          female: Kobieta
          unknown: Płeć nieznana
        sex: Płeć
        project_visit_template_id: Harmonogram wizyt
        clinical_center_id: Ośrodek

      site_review:
        added_by_researcher: Reviewer
        project: Study
        resource: Site
        resource_of_ClinicalCenter_type_id: Site
        resource_of_ClinicalCenter_type_contract_research_organization_id: CRO
        resource_of_ClinicalCenter_type_project_id: Study
        created_at: When
      researcher:
        show_predicted_costs: "Use the CostAdvisor&trade; while entering costs for new visits (Silver and Gold projects only)"
        failed_login_notification_allowed: "Notify me about incorrect login attempts"
        different_ip_notification_allowed: "Notify me about logins from new locations or unknown networks"
        new_patient_notifications: "Notify me about new subjects added to my projects"
        receive_transfer_sent_confirmation_email: "Send me proof of payments when new transactions are completed"
        site_payments_payment_notifications: "Send me proof of site payments when new transactions are completed"
        failed_login_sms_notification: "Notify me about first incorrect login attempt a day"
        deactivated_after_inactive_at: Zdezaktywowany za brak logowania
        trained_on: Przeszkolony dnia
        trained: Szkolony
        data_confirmed_at: Data aktywacji
        # receive_transfer_sent_confirmation_email: Czy badacz chce otrzymywac emaile o wysłanych przelewach
        receive_waiting_transfers_email: Czy badacz chce otrzymywac emaile o przelewach do akceptacji
        pdf_locale: Język dokumentów PDF
        locked_to: Konto zablokowane do
        # new_patient_notifications: Powiadomienia o nowych pacjentach
        patient_inquiry_notifications: Wiadomości od pacjentów
        researcher_inquiry_notifications: Wiadomości od badaczy
        pass: Hasło
        password: Hasło
        password_confirmation: Potwierdzenie hasła
        current_password: Aktualne hasło
        email: Email
        remember_me: Zapamiętaj mnie
        first_name: Imię
        last_name: Nazwisko
        locale: Język
        phone_number: Telefon
      clinical_transfer:
        source_acc_nr: Nr konta nadawcy
        source_name: Nazwa nadawcy
        status_change_date: Data zmiany statusu
        name: Nazwa odbiorcy
        amount: Kwota przelewu
        states:
          w: Do akceptacji
          d: Oczekujący na środki
          p: Zaaprobowany
          s: Wykonany
          b: Wykonany
          c: Odrzucony
          u: Nierozstrzygnięty
          g: Zgrupowany
          default: Nieokreślony
        destinations:
          clinical_center: Ośrodek
          clinical_user: Pacjent
      clinical_center:
        default_show_acc: Domyslnie pokaz rachunek osrodka
        name: Nazwa ośrodka
        clinical_center_code: Numer ośrodka
        street: Ulica
        zip_code: Kod pocztowy
        city: Miejscowość
        supervisor_full_name: Główny badacz
        transfer_limit: Limit max przelewu
        closed: 'Zdezaktywowany'
        closed_at: "Zdezaktywowany o"
        closed_by: Zdezaktywowany przez
        closed_reason: Powód deaktywacji
        first_name: Imię koordynatora
        last_name: Nazwisko koordynatora
        phone_number: Numer telefonu koordynatora
        account_number: Numer konta
        can_receive_transfers: Zgoda na wysylanie przelewow na konto osrodka
        transfer_receiver: Odbiorca przelewu
        states:
          a: Aktywny
          b: Zablokowany
          f: Zakończony
          default: Nieokreślony
      project_debit:
        amount_available_for_topup: Kwota dostępna do zasilenia przelewów
        topup_amount: Dotychczas zasilono kwotą
        status: Status
        statuses:
          s: Bieżące
          w: Do opłacenia
          p: Wykonane
          i: Podpisy zebrane
          c: Anulowane
          default: Nieznany
      project_role:
        project_role: Rola
      contract_research_organization:
        investigator_info: Informacja dla investigators
        cra_managers_info: Informacja dla cra/managers
        account_number_for_returns: Rachunek bankowy do zwrotu srodkow
        auto_accept_transfer_amount_range: automatyczna akceptacje przelewow zleconych przez investigator+ - tolerancja w % (wpisz 10 aby miec 10%)
        auto_accept_transfer_within_amount_range: automatyczna akceptacje przelewow zleconych przez investigator+
        post_paid_sign_notes_by_manager_only: Dla projektow post-paid zestawienia/batche moze podpisywac tylko manager
        invite_investigator: Moze zapraszac investigator
        invite_investigator_plus: Moze zapraszac investigator+
        invite_cta: Moze zapraszac CTA
        invite_cra: Moze zapraszac CRA
        invite_cra_plus: Moze zapraszac CRA+
        invite_manager: Moze zapraszac Manager
        note_po: PO dla not
        po: PO dla faktur
        summary_per_debit: "Czy nota obejmuje tylko jedno zestawienie?"
        po_required_for_post_paid_note: PO wymagany na nocie post-paid
        surveys_active: Aktywne ankiety
        exchange_diff_ct_3: Roznice kursowe ct-3
        fakir_nota_dt_2: Fakir nota dt-2
        fakir_nota_CT: Fakir nota ct-1
        fakir_nota_DT: Fakir nota dt-1
        sp_manager_max_payment_limit: Limit kwoty przelewu SP dla Managera
        sp_note_payment_due: Termin płatności noty SP (w dniach)
        auto_create_invoice: Automatyczne generowanie faktur
        plan_fee_type: Typ opłat premium
        fee_currency: Waluta opłat
        fee_plan: Pakiet wsparcia SLA
        site_fee: Pakiet wsparcia Premium dla ośrodków
        bulk_notes: Noty zbiorcze
        importance: Ważność
        min_patient_nr: Minimalna liczba pacjentów w projekcie
        country_code: Kod kraju firmy
        fakir_invoice_description: FAKIR OPIS FAKTURY
        fakir_invoice_gross_acc: FAKIR KONTO BRUTTO FAKTURA
        fakir_invoice_net_acc: FAKIR KONTO NETTO FAKTURA
        fakir_invoice_vat_acc: FAKIR KONTO VAT FAKTURA
        fakir_vat_amount: FAKIR STAWKA VAT
        fakir_vat_category: FAKIR KATEGORIA VAT
        company_type: Typ firmy
        auto_sign_debit_period: Po ilu dniach zestawienie bedzie sie kwalifikowalo do automatycznego podpisywania
        citi_transfer_fee: Oplata za wprowadzenie danych za wizyte
        start_up_fee: Opłata za dodanie projektu
        auto_sign_debits: Czy automatycznie podpisywac noty jezeli przez 2 tygodnie od wystawienia nie zostana podpisane?
        cu_receive_sms: Domyslnie pacjenci otrzymuja SMSy
        inv_can_edit_visit: czy investigator bez plusa moze wpisywac date i kwote do wizyty i je zapisywac
        user_can_edit_visit: czy pacjent moze wpisywac date i kwote na widoku wizyty u zalogowanego pacjenta
        researcher_can_inv_inv: Czy badacz moze zaprosic Investigator
        researcher_can_inv_inv_plus: Czy badacz moze zaprosic Investigator+
        researcher_can_inv_cra: Czy badacz moze zaprosic CRA
        researcher_can_inv_cra_plus: Czy badacz moze zaprosic CRA+
        bulk_invoice: Faktury zbiorcze?
        system_invoices: Czy faktury sa generowane z systemu?
        closed_at: Deaktywowany dnia
        shipment_report_email: Email do wysyłki raportu shipments
        shipment_report_email_cc: CC do wysyłki raportu shipments
        send_app_req: Czy wysylac emaile APPROVAL REQUIRED w projektach tego CRO?
        debit_allowed_prefered: Preferowany typ projekt
        premium_plan_fee: Pakiet wsparcia SLA Silver flat rate / per patient
        super_premium_plan_fee: Pakiet wsparcia SLA Gold flat rate / per patient
        platinum_plan_fee: Pakiet wsparcia SLA Platinum flat rate / per patient
        transfer_otp_required: Czy przelewy zatwierdzane kodem z 2 fact auth
        two_factor_inv: 2 stopniowe logowanie dla Investigator/Investigator+
        two_factor_cra: 2 stopniowe logowanie dla CRA/CRA+
        two_factor_manager: 2 stopniowe logowanie dla Manager
        cra_manager_can_view_visit_documents: CRA, CRA+ i Manager moga robic View kosztu wizyt
        project_super_premium_fee: Kwota za projekt super-premium
        invoice_currency: Waluta faktur
        hide_cu_acc_nr: Ukryj numery kont pacjentów
        company_invoice_payment_end_days: Liczba dni do terminu platnosci dla faktur
        company_pds_payment_end_days: Liczba dni do terminu platnosci dla not
        fee_patient_add: Opłata za dodanie pacjenta
        fee_active_patient: Opłata za utrzymanie konta pacjenta
        fee_researcher_add: Opłata za dodanie badacza
        tier_1_transfer_fee: Opłata za przelew do krajów grupy 1 (tańsze)
        tier_2_transfer_fee: Opłata za przelew do krajów grupy 2 (droższe)
        fee_post_transfer: Opłata za przekaz pocztowy
        administrator_id: Opiekun
        pdf_researcher_id_name: Nazwa ID badacza w pdfie
        close_pd_freq: 'Częstotliwość zamykania debetów'
        researcher_for_pds_email: 'Email do wysyłki noty'
        pds_email_raw: 'Email do wysyłki noty (oddzielone spacją)'
        researcher_for_invoice_email: 'Email do wysyłki faktur'
        project_report_email: 'Email do wysyłki raportów z projektów'
        project_report_email_raw: 'Email do wysyłki raportów z projektów (oddzielone spacją)'
        invoice_email_cc:  'Email do wysyłki faktur CC'
        invoice_email_cc_raw: 'Email do wysyłki faktur CC (oddzielone spacją)'
        pds_email_cc: 'Email do wysyłki noty CC'
        pds_email_cc_raw: 'Email do wysyłki noty CC (oddzielone spacją)'
        project_report_email_cc: 'Email do wysyłki raportów z projektów - CC'
        project_report_email_cc_raw: 'Email do wysyłki raportów z projektów - CC (oddzielone spacją)'
        pds_pdf_template: 'Szablon noty obciazeniowej'
        pd_pdf_template: 'Szablon zestawienia'
        transfer_confirmation_template: 'Szablon potwierdzenia przelewów'
        patient_data_template: 'Szablon formularza dla pacjenta'
        fee_locale: 'Język raportu opłat (XLS)'
        invoice_locale: 'Język faktury'
        locale: 'Język not'
        refund_acc_nr: Numer konta do zwrotów
        refund_swift: Numer SWIFT do zwrotów
        send_report_to_cra: Wyślij raport do CRA
        send_report_to_managers: Wyślij raport do menadźerów
        currency: Waluta not
        account_number: Dodatkowy numer rachunku
        pir_account_number: Numer PIR
        active_account_number: Aktywny numer rachunku
        transfer_title: Tytuł przelewów
      visit:
        clinical_user: Numer pacjenta
      researcher_training:
        training: Realizacja szkolenia
        researcher: Badacz
        completed: Wzial udzial
      site_setting:
        vip_investigator_info: Informacje dla VIP investigator
        investigator_info: Informacja dla investigators
        cra_managers_info: Informacja dla cra/managers
        helpdesk_email_manager_response_email_subject: Email subject odpowiedzi helpdesk dla Managers
        helpdesk_email_manager_response_email_body: Email body odpowiedzi helpdesk dla Managers
        helpdesk_email_manager_response_sms_body: SMS body odpowiedzi helpdesk dla Managers
        helpdesk_email_cra_response_email_subject: Email subject odpowiedzi helpdesk dla CRAs
        helpdesk_email_cra_response_email_body: Email body odpowiedzi helpdesk dla CRAs
        helpdesk_email_cra_response_sms_body: SMS body odpowiedzi helpdesk dla CRAs
        helpdesk_email_investigator_response_email_subject: Email subject odpowiedzi helpdesk dla Investigators
        helpdesk_email_investigator_response_email_body: Email body odpowiedzi helpdesk dla Investigators
        helpdesk_email_investigator_response_sms_body: SMS body odpowiedzi helpdesk dla Investigators
        helpdesk_email_not_registered_response_email_subject: Email subject odpowiedzi helpdesk dla niezarejestrowanych badaczy
        helpdesk_email_not_registered_response_email_body: Email body odpowiedzi helpdesk dla niezarejestrowanych badaczy
        helpdesk_email_not_registered_response_sms_body: SMS body odpowiedzi helpdesk dla niezarejestrowanych badaczy
        helpdesk_email_response_cc: Email CC odpowiedzi helpdesk (oddzielone przecinkami)
      helpdesk_email:
        contains_personal_data_form: Does this email contain the subject’s personal data form?