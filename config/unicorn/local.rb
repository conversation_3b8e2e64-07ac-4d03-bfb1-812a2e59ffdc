# test unicorn locally
# bundle exec unicorn -c config/unicorn/local.rb

# Set the environment
rails_env = ENV['RAILS_ENV'] || 'development'

# Load the Rails application
require File.expand_path('../environment', __dir__)

# Set the working application directory
working_directory Rails.root.to_s

# Unicorn PID file location
pid "#{Rails.root}/tmp/pids/unicorn.pid"

# Path to logs
stderr_path "#{Rails.root}/log/unicorn.#{rails_env}.log"
stdout_path "#{Rails.root}/log/unicorn.#{rails_env}.log"

# Change this line:
# listen "#{Rails.root}/tmp/sockets/unicorn.sock", backlog: 64

# To this:
listen 3000, tcp_nopush: true

# Number of processes
worker_processes rails_env == 'production' ? 4 : 2

# Time-out
timeout 30

# Load the app into the master before forking workers
preload_app true

# Set up database connections before forking workers
before_fork do |server, _worker|
  defined?(ActiveRecord::Base) && ActiveRecord::Base.connection.disconnect!

  # Before forking, kill the master process that belongs to the .oldbin PID.
  # This enables 0 downtime deploys.
  old_pid = "#{server.config[:pid]}.oldbin"
  if File.exist?(old_pid) && server.pid != old_pid
    begin
      Process.kill("QUIT", File.read(old_pid).to_i)
    rescue Errno::ENOENT, Errno::ESRCH
      # someone else did our job for us
    end
  end
end

after_fork do |_server, _worker|
  defined?(ActiveRecord::Base) && ActiveRecord::Base.establish_connection
end