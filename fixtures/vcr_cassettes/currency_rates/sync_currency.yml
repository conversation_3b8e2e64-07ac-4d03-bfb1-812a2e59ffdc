---
http_interactions:
- request:
    method: get
    uri: https://api.nbp.pl/api/exchangerates/rates/a/GBP/2020-02-07
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-cache
      Pragma:
      - no-cache
      Content-Type:
      - application/json; charset=utf-8
      Expires:
      - "-1"
      Etag:
      - '"jUuOSaeQbtAISJTPbmQXUzBI9YOcWsUy4OMz/YRKa1E="'
      Date:
      - Mon, 11 May 2020 05:59:17 GMT
      Transfer-Encoding:
      - chunked
      Strict-Transport-Security:
      - max-age=63072000
      Set-Cookie:
      - ROUTEID=32; path=/
    body:
      encoding: ASCII-8BIT
      string: '{"table":"A","currency":"funt szterling","code":"GBP","rates":[{"no":"026/A/NBP/2020","effectiveDate":"2020-02-07","mid":5.0359}]}'
    http_version: null
  recorded_at: Mon, 11 May 2020 05:59:17 GMT
recorded_with: VCR 5.1.0
