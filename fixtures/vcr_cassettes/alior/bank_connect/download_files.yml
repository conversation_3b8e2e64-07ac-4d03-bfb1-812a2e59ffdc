---
http_interactions:
- request:
    method: get
    uri: https://bankconnect.aliorbank.pl/bankconnect/bank-connect/service02.wsdl
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: ''
    headers:
      Content-Type:
      - text/xml
      Date:
      - Fri, 08 May 2020 11:19:32 GMT
      Set-Cookie:
      - 7ed77e9164=!s8k9ynuWCosnt6i4q0oMzXz7/1KUQxzz5L9kIyjzzKPJzW+zDCC1rAsCazYYH+fXe9Bt7hEC3iZU7Q==;
        path=/; Httponly; Secure
      - TS01b44089=01eeb70fb938cef92548c2207594f8f42252b2a6f66508e7b2ac1e010c563993fda6a6fb53420a26281fcfc9d3dbcb529b9b887b8971890e13638b36ff860d49c28f08e05f;
        Path=/; Domain=.bankconnect.aliorbank.pl
      Strict-Transport-Security:
      - max-age=********
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\"?><wsdl:definitions xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\"
        xmlns:sch=\"urn:bph:cp:businessnet:bankconnect:Service.01\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"
        xmlns:tns=\"urn:bph:cp:businessnet:bankconnect:Service.02\" targetNamespace=\"urn:bph:cp:businessnet:bankconnect:Service.02\">\n
        \ <wsdl:types xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    <xs:schema
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\" xmlns=\"urn:bph:cp:businessnet:bankconnect:Service.02\"
        xmlns:Q1=\"urn:swift:xsd:camt.003.001.04\" xmlns:Q13=\"urn:bph:cp:businessnet:bankconnect:ServiceError.01\"
        xmlns:Q15=\"urn:iso:std:iso:20022:tech:xsd:pain.001.001.03\" xmlns:Q16=\"urn:iso:std:iso:20022:tech:xsd:pain.002.001.03\"
        xmlns:Q17=\"urn:ca:std:ccs:bph:tech:xsd:past.001.001.02\" xmlns:Q18=\"urn:ca:std:cdc:tech:xsd:itin.002.001.01\"
        xmlns:Q19=\"urn:ca:std:cdc:tech:xsd:itsr.002.001.01\" xmlns:Q2=\"urn:swift:xsd:camt.004.001.04\"
        xmlns:Q20=\"urn:swift:xsd:camt.008.001.04\" xmlns:Q21=\"nbp.Receipt.camt.025.001.02\"
        xmlns:Q3=\"urn:bph:cp:businessnet:bankconnect:GetAccountReport.01\" xmlns:Q4=\"urn:iso:std:iso:20022:tech:xsd:camt.052.001.01\"
        xmlns:Q5=\"urn:bph:cp:businessnet:bankconnect:GetStatement.01\" xmlns:Q6=\"urn:iso:std:iso:20022:tech:xsd:camt.053.001.01\"
        xmlns:Q7=\"urn:bph:cp:businessnet:bankconnect:GetDocuments.01\" xmlns:Q8=\"urn:bph:cp:businessnet:bankconnect:DocumentsList.01\"
        xmlns:S1=\"http://www.w3.org/2000/09/xmldsig#\" attributeFormDefault=\"unqualified\"
        elementFormDefault=\"qualified\" targetNamespace=\"urn:bph:cp:businessnet:bankconnect:Service.01\">\n\n\t\n\t<xs:import
        namespace=\"urn:swift:xsd:camt.003.001.04\" schemaLocation=\"../xsd/services/GetAccount.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"urn:swift:xsd:camt.004.001.04\"
        schemaLocation=\"../xsd/services/ReturnAccount.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"urn:bph:cp:businessnet:bankconnect:GetAccountReport.01\" schemaLocation=\"../xsd/services/GetAccountReport.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"urn:iso:std:iso:20022:tech:xsd:camt.052.001.01\"
        schemaLocation=\"../xsd/services/BankToCustomerAccountReport.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"urn:bph:cp:businessnet:bankconnect:GetStatement.01\" schemaLocation=\"../xsd/services/GetStatement.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"urn:iso:std:iso:20022:tech:xsd:camt.053.001.01\"
        schemaLocation=\"../xsd/services/BankToCustomerStatement.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"urn:bph:cp:businessnet:bankconnect:GetDocuments.01\" schemaLocation=\"../xsd/services/GetDocuments.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"urn:bph:cp:businessnet:bankconnect:DocumentsList.01\"
        schemaLocation=\"../xsd/services/DocumentsList.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"urn:bph:cp:businessnet:bankconnect:ServiceError.01\" schemaLocation=\"../xsd/services/ServiceError.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"urn:iso:std:iso:20022:tech:xsd:pain.001.001.03\"
        schemaLocation=\"../xsd/services/pain.001.001.03.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"urn:iso:std:iso:20022:tech:xsd:pain.002.001.03\" schemaLocation=\"../xsd/services/pain.002.001.03.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"http://www.w3.org/2000/09/xmldsig#\"
        schemaLocation=\"../xsd/services/xmldsig-core-schema.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"urn:ca:std:ccs:bph:tech:xsd:past.001.001.02\" schemaLocation=\"../xsd/services/past.001.001.02.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"urn:ca:std:cdc:tech:xsd:itin.002.001.01\"
        schemaLocation=\"../xsd/services/itin.002.001.01.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"urn:ca:std:cdc:tech:xsd:itsr.002.001.01\" schemaLocation=\"../xsd/services/itsr.002.001.01.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import namespace=\"urn:swift:xsd:camt.008.001.04\"
        schemaLocation=\"../xsd/services/camt.008.001.04.xsd\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:import
        namespace=\"nbp.Receipt.camt.025.001.02\" schemaLocation=\"../xsd/services/camt.025.001.02.xsd\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t<xs:element name=\"GetAccountBalanceRequest\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element maxOccurs=\"1\"
        minOccurs=\"1\" name=\"Document\" type=\"Q1:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\t\t\t\t\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetAccountBalanceResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q2:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetOperationsHistoryRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q3:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetOperationsHistoryResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q4:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetStatementRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q5:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetStatementResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q6:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetDocumentRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q7:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetDocumentResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q8:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetOrdersStateRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q17:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetOrdersStateResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q16:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\t\n\t<xs:element
        name=\"DomesticTransferRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" ref=\"S1:Signature\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"DomesticTransferResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"ForeignTransferRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\t\t\t\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" ref=\"S1:Signature\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"ForeignTransferResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\t\n\t<xs:element
        name=\"GetPaymentStatusReportRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\t\t\t\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q17:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"GetPaymentStatusReportResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\t\n\t<xs:element
        name=\"SEPATransferRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\t\t\t\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" ref=\"S1:Signature\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"SEPATransferResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"TransPayTransferRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" ref=\"S1:Signature\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\n\t<xs:element
        name=\"TransPayTransferResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"Document\" type=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        maxOccurs=\"1\" minOccurs=\"1\" name=\"DocumentError\" type=\"Q13:DocumentError\"
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\t\n\n\n\t<xs:element
        name=\"TransPrzekazTransferRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        ref=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        ref=\"S1:Signature\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\n\t<xs:element
        name=\"TransPrzekazTransferResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:choice xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        ref=\"Q15:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        ref=\"Q13:DocumentError\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:choice>\n\t\t</xs:complexType>\n\t</xs:element>\t\n\n\t<xs:element
        name=\"UploadInitiationRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        minOccurs=\"0\" ref=\"Q18:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        ref=\"S1:Signature\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"UploadInitiationResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        ref=\"Q19:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"CancelTransactionRequest\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        minOccurs=\"0\" ref=\"Q20:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t\t<xs:element
        minOccurs=\"0\" ref=\"S1:Signature\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n\t<xs:element
        name=\"CancelTransactionResponse\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t<xs:complexType
        xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t<xs:sequence xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n\t\t\t\t<xs:element
        ref=\"Q21:Document\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"/>\n\t\t\t</xs:sequence>\n\t\t</xs:complexType>\n\t</xs:element>\n\n</xs:schema>\n
        \ </wsdl:types>\n  <wsdl:message name=\"TransPrzekazTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:TransPrzekazTransferRequest\" name=\"TransPrzekazTransferRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetOperationsHistoryResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetOperationsHistoryResponse\" name=\"GetOperationsHistoryResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"TransPayTransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:TransPayTransferResponse\" name=\"TransPayTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetStatementRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetStatementRequest\" name=\"GetStatementRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"UploadInitiationResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:UploadInitiationResponse\" name=\"UploadInitiationResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetOrdersStateResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetOrdersStateResponse\" name=\"GetOrdersStateResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetAccountBalanceRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetAccountBalanceRequest\" name=\"GetAccountBalanceRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"DomesticTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:DomesticTransferRequest\" name=\"DomesticTransferRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetOrdersStateRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetOrdersStateRequest\" name=\"GetOrdersStateRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetPaymentStatusReportRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetPaymentStatusReportRequest\" name=\"GetPaymentStatusReportRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"CancelTransactionResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:CancelTransactionResponse\" name=\"CancelTransactionResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetDocumentRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetDocumentRequest\" name=\"GetDocumentRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"DomesticTransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:DomesticTransferResponse\" name=\"DomesticTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"SEPATransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:SEPATransferRequest\" name=\"SEPATransferRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"SEPATransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:SEPATransferResponse\" name=\"SEPATransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetAccountBalanceResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetAccountBalanceResponse\" name=\"GetAccountBalanceResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetDocumentResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetDocumentResponse\" name=\"GetDocumentResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetOperationsHistoryRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetOperationsHistoryRequest\" name=\"GetOperationsHistoryRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"ForeignTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:ForeignTransferRequest\" name=\"ForeignTransferRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"CancelTransactionRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:CancelTransactionRequest\" name=\"CancelTransactionRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetPaymentStatusReportResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetPaymentStatusReportResponse\" name=\"GetPaymentStatusReportResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"UploadInitiationRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:UploadInitiationRequest\" name=\"UploadInitiationRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"TransPrzekazTransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:TransPrzekazTransferResponse\" name=\"TransPrzekazTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"GetStatementResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:GetStatementResponse\" name=\"GetStatementResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"TransPayTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:TransPayTransferRequest\" name=\"TransPayTransferRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:message name=\"ForeignTransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:part element=\"sch:ForeignTransferResponse\" name=\"ForeignTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:part>\n  </wsdl:message>\n
        \ <wsdl:portType name=\"services\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:operation name=\"TransPrzekazTransfer\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <wsdl:input message=\"tns:TransPrzekazTransferRequest\" name=\"TransPrzekazTransferRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:input>\n      <wsdl:output
        message=\"tns:TransPrzekazTransferResponse\" name=\"TransPrzekazTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:output>\n    </wsdl:operation>\n
        \   <wsdl:operation name=\"GetOperationsHistory\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <wsdl:input message=\"tns:GetOperationsHistoryRequest\" name=\"GetOperationsHistoryRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:input>\n      <wsdl:output
        message=\"tns:GetOperationsHistoryResponse\" name=\"GetOperationsHistoryResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:output>\n    </wsdl:operation>\n
        \   <wsdl:operation name=\"TransPayTransfer\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <wsdl:input message=\"tns:TransPayTransferRequest\" name=\"TransPayTransferRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:input>\n      <wsdl:output
        message=\"tns:TransPayTransferResponse\" name=\"TransPayTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:output>\n    </wsdl:operation>\n
        \   <wsdl:operation name=\"GetStatement\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <wsdl:input message=\"tns:GetStatementRequest\" name=\"GetStatementRequest\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n    </wsdl:input>\n      <wsdl:output
        message=\"tns:GetStatementResponse\" name=\"GetStatementResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"UploadInitiation\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:UploadInitiationRequest\"
        name=\"UploadInitiationRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:UploadInitiationResponse\"
        name=\"UploadInitiationResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"GetOrdersState\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:GetOrdersStateRequest\"
        name=\"GetOrdersStateRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:GetOrdersStateResponse\"
        name=\"GetOrdersStateResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"GetAccountBalance\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:GetAccountBalanceRequest\"
        name=\"GetAccountBalanceRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:GetAccountBalanceResponse\"
        name=\"GetAccountBalanceResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"DomesticTransfer\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:DomesticTransferRequest\"
        name=\"DomesticTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:DomesticTransferResponse\"
        name=\"DomesticTransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"GetPaymentStatusReport\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:GetPaymentStatusReportRequest\"
        name=\"GetPaymentStatusReportRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:GetPaymentStatusReportResponse\"
        name=\"GetPaymentStatusReportResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"CancelTransaction\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:CancelTransactionRequest\"
        name=\"CancelTransactionRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:CancelTransactionResponse\"
        name=\"CancelTransactionResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"GetDocument\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:GetDocumentRequest\"
        name=\"GetDocumentRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:GetDocumentResponse\"
        name=\"GetDocumentResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"SEPATransfer\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:SEPATransferRequest\"
        name=\"SEPATransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:SEPATransferResponse\"
        name=\"SEPATransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"ForeignTransfer\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <wsdl:input message=\"tns:ForeignTransferRequest\"
        name=\"ForeignTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:input>\n      <wsdl:output message=\"tns:ForeignTransferResponse\"
        name=\"ForeignTransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   </wsdl:output>\n    </wsdl:operation>\n  </wsdl:portType>\n  <wsdl:binding
        name=\"servicesSoap11\" type=\"tns:services\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <soap:binding style=\"document\" transport=\"http://schemas.xmlsoap.org/soap/http\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n    <wsdl:operation
        name=\"TransPrzekazTransfer\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"TransPrzekazTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"TransPrzekazTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"GetOperationsHistory\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"GetOperationsHistoryRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"GetOperationsHistoryResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"TransPayTransfer\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"TransPayTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"TransPayTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"GetStatement\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"GetStatementRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"GetStatementResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"UploadInitiation\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <soap:operation soapAction=\"\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      <wsdl:input
        name=\"UploadInitiationRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"UploadInitiationResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"GetOrdersState\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"GetOrdersStateRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"GetOrdersStateResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"GetAccountBalance\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <soap:operation soapAction=\"\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      <wsdl:input
        name=\"GetAccountBalanceRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"GetAccountBalanceResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"DomesticTransfer\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"DomesticTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"DomesticTransferResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"GetPaymentStatusReport\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <soap:operation soapAction=\"\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      <wsdl:input
        name=\"GetPaymentStatusReportRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"GetPaymentStatusReportResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"CancelTransaction\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"CancelTransactionRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"CancelTransactionResponse\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n        <soap:body use=\"literal\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      </wsdl:output>\n
        \   </wsdl:operation>\n    <wsdl:operation name=\"GetDocument\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:operation soapAction=\"\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     <wsdl:input name=\"GetDocumentRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"GetDocumentResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"SEPATransfer\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <soap:operation soapAction=\"\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      <wsdl:input
        name=\"SEPATransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"SEPATransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:output>\n    </wsdl:operation>\n    <wsdl:operation name=\"ForeignTransfer\"
        xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n      <soap:operation soapAction=\"\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n      <wsdl:input
        name=\"ForeignTransferRequest\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:input>\n      <wsdl:output name=\"ForeignTransferResponse\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \       <soap:body use=\"literal\" xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n
        \     </wsdl:output>\n    </wsdl:operation>\n  </wsdl:binding>\n  <wsdl:service
        name=\"servicesService\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \   <wsdl:port binding=\"tns:servicesSoap11\" name=\"servicesSoap11\" xmlns:wsdl=\"http://schemas.xmlsoap.org/wsdl/\">\n
        \     <soap:address location=\"https://bankconnect.aliorbank.pl:443/bankconnect/bank-connect\"
        xmlns:soap=\"http://schemas.xmlsoap.org/wsdl/soap/\"/>\n    </wsdl:port>\n
        \ </wsdl:service>\n</wsdl:definitions>"
    http_version: null
  recorded_at: Fri, 08 May 2020 11:19:32 GMT
- request:
    method: post
    uri: https://bankconnect.aliorbank.pl/bankconnect/bank-connect
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><soapenv:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:urn="urn:bph:cp:businessnet:bankconnect:Service.01"
        xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ins0="urn:bph:cp:businessnet:bankconnect:Service.01"
        xmlns:x="http://schemas.xmlsoap.org/soap/envelope/" xmlns:urn7="urn:bph:cp:businessnet:bankconnect:GetDocuments.01"><soapenv:Body><ins0:GetDocumentRequest><urn:Document><urn7:GetRptLst><urn7:MsgId><urn7:Id>LMP202005081588936771</urn7:Id></urn7:MsgId><urn7:RptLstQryDef><urn7:RptLstCrit><urn7:NewCrit><urn7:SchCrit><urn7:RptValDt><urn7:DtSch><urn7:FrDt>2020-05-01</urn7:FrDt><urn7:ToDt>2020-05-07</urn7:ToDt></urn7:DtSch></urn7:RptValDt></urn7:SchCrit></urn7:NewCrit></urn7:RptLstCrit></urn7:RptLstQryDef></urn7:GetRptLst></urn:Document></ins0:GetDocumentRequest></soapenv:Body></soapenv:Envelope>
    headers:
      Soapaction:
      - '"GetDocument"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '926'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: ''
    headers:
      Accept:
      - text/xml, text/html, image/gif, image/jpeg, *; q=.2, */*; q=.2
      Soapaction:
      - '""'
      Content-Type:
      - text/xml;charset=utf-8
      Content-Length:
      - '2871'
      Date:
      - Fri, 08 May 2020 11:19:32 GMT
      Set-Cookie:
      - 7ed77e9164=!cBA0k8q/WffOoZC4q0oMzXz7/1KUQ/4w1g0NXxfK+7MBSxHweVbqr0F8p7iF03ibI7JrhrSaCCp8aw==;
        path=/; Httponly; Secure
      - TS01b44089=01eeb70fb9250acaf14d054eefa3409bea2b3ac93c010fec1c2ca7d986689175554a9cd72a51ed4cbdee78248f106aa62357a70f87c0fb5d642722ed9eb593dde17a85313f;
        Path=/; Domain=.bankconnect.aliorbank.pl
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: <SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"><SOAP-ENV:Header/><SOAP-ENV:Body><bc1:GetDocumentResponse
        xmlns:bc1="urn:bph:cp:businessnet:bankconnect:Service.01" xmlns:bc2="urn:bph:cp:businessnet:bankconnect:DocumentsList.01"
        xmlns:bc3="urn:swift:xsd:camt.004.001.04" xmlns:bc4="urn:iso:std:iso:20022:tech:xsd:camt.052.001.01"
        xmlns:bc5="urn:swift:xsd:camt.003.001.04" xmlns:bc6="urn:iso:std:iso:20022:tech:xsd:camt.053.001.01"
        xmlns:ns10="urn:iso:std:iso:20022:tech:xsd:pain.002.001.03" xmlns:ns11="urn:ca:std:cdc:tech:xsd:ccmt.002.001.01"
        xmlns:ns12="urn:ca:std:cdc:tech:xsd:ccmt.004.001.01" xmlns:ns13="urn:ca:std:cdc:tech:xsd:ccmt.006.001.01"
        xmlns:ns14="urn:ca:std:cdc:tech:xsd:itsr.002.001.01" xmlns:ns15="nbp.Receipt.camt.025.001.02"
        xmlns:ns16="urn:swift:xsd:camt.008.001.04" xmlns:ns17="http://www.w3.org/2000/09/xmldsig#"
        xmlns:ns18="urn:ca:std:cdc:tech:xsd:ccmt.008.001.01" xmlns:ns19="urn:ca:std:cdc:tech:xsd:ccmt.001.001.01"
        xmlns:ns20="urn:ca:std:cdc:tech:xsd:ccmt.003.001.01" xmlns:ns21="urn:ca:std:ccs:bph:tech:xsd:past.001.001.02"
        xmlns:ns22="urn:bph:cp:businessnet:bankconnect:GetDocuments.01" xmlns:ns23="urn:bph:cp:businessnet:bankconnect:GetStatement.01"
        xmlns:ns24="urn:ca:std:cdc:tech:xsd:ccmt.007.001.01" xmlns:ns25="urn:bph:cp:businessnet:bankconnect:GetAccountReport.01"
        xmlns:ns26="urn:ca:std:cdc:tech:xsd:itin.002.001.01" xmlns:ns27="urn:ca:std:cdc:tech:xsd:ccmt.005.001.01"
        xmlns:ns8="urn:bph:cp:businessnet:bankconnect:ServiceError.01" xmlns:ns9="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03"><bc1:Document><bc2:RptLst><bc2:MsgId><bc2:Id>BC_DL_20200508_131932238</bc2:Id></bc2:MsgId><bc2:LstNtry><bc2:RptNm>460011141975D20200504.txt</bc2:RptNm><bc2:CreDt>2020-05-04</bc2:CreDt><bc2:RptDtls><bc2:RptType>TCL</bc2:RptType><bc2:RptSize>844</bc2:RptSize><bc2:RptURL>https://bankconnect.aliorbank.pl/bankconnect/do/getCMReport?rp_link=data_cm%2F********%2Farch%2F20200504TCL460011141975D20200504.txt&amp;rp_size=844&amp;rp_na</bc2:RptURL></bc2:RptDtls></bc2:LstNtry><bc2:LstNtry><bc2:RptNm>460011141975D20200506.txt</bc2:RptNm><bc2:CreDt>2020-05-06</bc2:CreDt><bc2:RptDtls><bc2:RptType>TCL</bc2:RptType><bc2:RptSize>251</bc2:RptSize><bc2:RptURL>https://bankconnect.aliorbank.pl/bankconnect/do/getCMReport?rp_link=data_cm%2F********%2Farch%2F20200506TCL460011141975D20200506.txt&amp;rp_size=251&amp;rp_na</bc2:RptURL></bc2:RptDtls></bc2:LstNtry><bc2:LstNtry><bc2:RptNm>460011141975D20200507.txt</bc2:RptNm><bc2:CreDt>2020-05-07</bc2:CreDt><bc2:RptDtls><bc2:RptType>TCL</bc2:RptType><bc2:RptSize>328</bc2:RptSize><bc2:RptURL>https://bankconnect.aliorbank.pl/bankconnect/do/getCMReport?rp_link=data_cm%2F********%2Farch%2F20200507TCL460011141975D20200507.txt&amp;rp_size=328&amp;rp_na</bc2:RptURL></bc2:RptDtls></bc2:LstNtry></bc2:RptLst></bc1:Document></bc1:GetDocumentResponse></SOAP-ENV:Body></SOAP-ENV:Envelope>
    http_version: null
  recorded_at: Fri, 08 May 2020 11:19:32 GMT
- request:
    method: get
    uri: https://bankconnect.aliorbank.pl/bankconnect/do/getCMReport?rp_link=data_cm/********/arch/20200504TCL460011141975D20200504.txt&rp_na&rp_size=844
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: ''
    headers:
      Content-Disposition:
      - attachment; filename="20200504TCL460011141975D20200504.txt";
      Expires:
      - '0'
      Cache-Control:
      - must-revalidate, post-check=0, pre-check=0
      Pragma:
      - public
      Content-Type:
      - application/octet-stream
      Date:
      - Fri, 08 May 2020 11:19:32 GMT
      Set-Cookie:
      - 7ed77e9164=!NJv2Tq0cRlCxR524q0oMzXz7/1KUQzeN1+x4F8tEYn3sQNL3mhPo0SNdaRVkzbCzi03FmQ3lsf94kg==;
        path=/; Httponly; Secure
      - TS01b44089=01eeb70fb9d41f769750a2f0b04e5d4aa4e18d742f357f56c64ee6b48e635d6fb8ca4624b4f812a0fbd9a5da87062ac65a5b143dd6fa3e553cf17c018b73054320d8f47773;
        Path=/; Domain=.bankconnect.aliorbank.pl
      Strict-Transport-Security:
      - max-age=********
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        MjAyMDA1MDR8NDQyNDkwMTAyODM1NjUwMDAyMDEwMDAwMDF8MDUxMHw3MjAwMHw0ODEwMzAxNTA4MDAwMDAwMDUwMzY1NzA1OHxNU0QgUE9MU0tBIFNQLiBaIE8uTy4gMDAtODY3IFdBUlNaQf5XQf7+fE1TRCBQb2xza2EgU3AuIHogby5vIDIxMDAwMjc3MzggNjct/jAwMjQtMDAwMi0wMjIgNzIwLjAwICD+/nw1OA0KMjAyMDA1MDR8OTcyNDkwMTAyODM1NjUwMDAxMDEwMDAwMDF8MDUxMHwxNzcyNzN8NTcxNjAwMTEyNzAwMDMwMTI3NjkzMTAwMDF8SVFWSUEgUkRTIFBPTEFORCBVTC4gRE9NQU5JRVdTS0EgNDj+/v58NTYtMDAxNi0wMjIwLTAwMDEtMDYyMy5QTDUyMjI5OTMzNDb+IC4gLiAuICD+/nw1OQ0KMjAyMDA1MDR8OTcyNDkwMTAyODM1NjUwMDAxMDEwMDAwMDF8MDUxMHwyNTA3NHw1NzE2MDAxMTI3MDAwMzAxMjc2OTMxMDAwMXxJUVZJQSBSRFMgUE9MQU5EIFVMLiBET01BTklFV1NLQSA0OP7+/nw2MC0wMDExLTAzMjAtMDAwMS0wNjQ4LlBMNTIyMjk5MzM0Nv4gLiAuIC4gIP7+fDYwDQoyMDIwMDUwNHw5NzI0OTAxMDI4MzU2NTAwMDEwMTAwMDAwMXwwNTEwfDQwMTE4NHw1NzE2MDAxMTI3MDAwMzAxMjc2OTMxMDAwMXxJUVZJQSBSRFMgUE9MQU5EIFVMLiBET01BTklFV1NLQSA0OP7+/nwxMi0wMDQyLTAyMjAtMDIyMC0wMDAxLTA2MzkuUEw1MjIyOf4gOTMzNDYuIC4gLiAg/v58NjENCjIwMjAwNTA0fDk3MjQ5MDEwMjgzNTY1MDAwMTAxMDAwMDAxfDA1MTB8MjA0MTc3fDU3MTYwMDExMjcwMDAzMDEyNzY5MzEwMDAxfElRVklBIFJEUyBQT0xBTkQgVUwuIERPTUFOSUVXU0tBIDQ4/v7+fDY1LTAwMjUtMDMyMC0wMDAxLTA2NTUuUEw1MjIyOTkzMzQ2/iAuIC4gLiAg/v58NjINCg==
    http_version: null
  recorded_at: Fri, 08 May 2020 11:19:32 GMT
- request:
    method: get
    uri: https://bankconnect.aliorbank.pl/bankconnect/do/getCMReport?rp_link=data_cm/********/arch/20200506TCL460011141975D20200506.txt&rp_na&rp_size=251
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: ''
    headers:
      Content-Disposition:
      - attachment; filename="20200506TCL460011141975D20200506.txt";
      Expires:
      - '0'
      Cache-Control:
      - must-revalidate, post-check=0, pre-check=0
      Pragma:
      - public
      Content-Type:
      - application/octet-stream
      Date:
      - Fri, 08 May 2020 11:19:35 GMT
      Set-Cookie:
      - 7ed77e9164=!m0ezdKtA4zqOYRS4q0oMzXz7/1KUQ++DFiy6VDQPW7WCsl2eOtOeLr4s2t8u9WUL/lt2II6akNx6Ig==;
        path=/; Httponly; Secure
      - TS01b44089=01eeb70fb9f415b7ea1ea460f78bca05fe5dbf257b91dc3b038f8b17492cdd41c8a207ce3d03583ac94f83faac8b364afa9fff2085fe4120b10d6bb0d1cc1b89d4d41de08a;
        Path=/; Domain=.bankconnect.aliorbank.pl
      Strict-Transport-Security:
      - max-age=********
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        MjAyMDA1MDZ8NDQyNDkwMTAyODM1NjUwMDAyMDEwMDAwMDF8MDUxMHw1ODg4NjR8NDgxMDMwMTUwODAwMDAwMDA1MDM2NTcwNTh8TVNEIFBPTFNLQSBTUC4gWiBPLk8uIDAwLTg2NyBXQVJTWkH+V0H+/nxNU0QgUG9sc2thIFNwLiB6IG8ubyAyMTAwMDI3NzY2IDk0Lf4wMDE1LTA0MjAtMDAwIDEsNzQ4LjcgMjEwMDAyNzc2NCA3OP4tMDAwMy0wNDIwLTAwMCA0ODAuMDAgMjEwMDAyNzc0MCA0MP4tMDAyNS0wNDIwLTAwMCAxLDA0My4xfDY0DQo=
    http_version: null
  recorded_at: Fri, 08 May 2020 11:19:35 GMT
- request:
    method: get
    uri: https://bankconnect.aliorbank.pl/bankconnect/do/getCMReport?rp_link=data_cm/********/arch/20200507TCL460011141975D20200507.txt&rp_na&rp_size=328
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: ''
    headers:
      Content-Disposition:
      - attachment; filename="20200507TCL460011141975D20200507.txt";
      Expires:
      - '0'
      Cache-Control:
      - must-revalidate, post-check=0, pre-check=0
      Pragma:
      - public
      Content-Type:
      - application/octet-stream
      Date:
      - Fri, 08 May 2020 11:21:02 GMT
      Set-Cookie:
      - 7ed77e9164=!gVHcSMqIloSF2fa4q0oMzXz7/1KUQ9WecVypsOzGh+GlR3sM7JeHfPiL14RLy2hAok0DGHbz6HexPg==;
        path=/; Httponly; Secure
      - TS01b44089=01eeb70fb9888e7fc7894ed8756442e58ad3216aa3e84e075526a5e280ea2e414d095b435406c74d69037439a678ceed4c5d36a4378a941eaefcb53a4fc5edcec4d85c6dd9;
        Path=/; Domain=.bankconnect.aliorbank.pl
      Strict-Transport-Security:
      - max-age=********
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        MjAyMDA1MDd8OTcyNDkwMTAyODM1NjUwMDAxMDEwMDAwMDF8MDUxMHwyMzIzNXw1NzE2MDAxMTI3MDAwMzAxMjc2OTMxMDAwMXxJUVZJQSBSRFMgUE9MQU5EIFVMLiBET01BTklFV1NLQSA0OP7+/nw4MC0wMDA2LTA0MjAtMDAwMS0wNjc2LlBMNTIyMjk5MzM0Nv4gLiAuIC4gIP7+fDY2DQoyMDIwMDUwN3w5NzI0OTAxMDI4MzU2NTAwMDEwMTAwMDAwMXwwNTEwfDE5MjIzfDU3MTYwMDExMjcwMDAzMDEyNzY5MzEwMDAxfElRVklBIFJEUyBQT0xBTkQgVUwuIERPTUFOSUVXU0tBIDQ4/v7+fDEwLTAwMDUtMDQyMC0wMDAxLTA2NzUuUEw1MjIyOTkzMzQ2/iAuIC4gLiAg/v58NjcNCg==
    http_version: null
  recorded_at: Fri, 08 May 2020 11:21:02 GMT
recorded_with: VCR 5.1.0
