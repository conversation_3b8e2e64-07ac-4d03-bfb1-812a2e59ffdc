#----------------------------------------------------------------------------
# Ignore these files when commiting to a git repository.
#
# See http://help.github.com/ignore-files/ for more about ignoring files.
#
# The original version of this file is found here:
# https://github.com/RailsApps/rails-composer/blob/master/files/gitignore.txt
#
# Corrections? Improvements? Create a GitHub issue:
# http://github.com/RailsApps/rails-composer/issues
#----------------------------------------------------------------------------
db/data.yml

#rvm
.ruby-gemset

# schema.rb
db/schema.rb
# bundler state
/.bundle
/vendor/bundle/
/vendor/ruby/

/config/bnp_certs/*
/config/bnp_secrets.yml
/config/database.yml

# minimal Rails specific artifacts
db/*.sqlite3
/log/*
/tmp/*
# various artifacts

*.sublime-project
*.sublime-workspace
**.war
*.rbc
*.sassc
.rspec
.redcar/
.sass-cache
/config/config.yml
#/config/database.yml
/coverage.data
/coverage/
/db/*.javadb/
/db/*.sqlite3
/doc/api/
/doc/app/
/doc/features.html
/doc/specs.html
/public/cache
/public/stylesheets/compiled
/public/system
/public/system/*
/public/assets/*
/spec/tmp/*
/cache
/capybara*
/capybara-*.html
/gems
/specifications
rerun.txt
pickle-email-*.html

# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile ~/.gitignore_global
#
# Here are some files you may want to ignore globally:

# scm revert files
**.orig

# Mac finder artifacts
.DS_Store

# Netbeans project directory
/nbproject/

# RubyMine project files
.idea

# Textmate project files
/*.tmproj

# vim artifacts
**.swp
.ruby-version
.ruby-gemset

/spec/examples.txt
/spec/*.log
/public/uploads/tmp/*
tmp_qr.png
.tabnineignore
TAGS
.byebug_history

/config/master.key
public/fixture.txt
config/certs/alior/bankconnect/cert.pem
config/certs/alior/bankconnect/key.pem
/config/sms_services.yml
.vscode/settings.json
*.sql.gz
/*.sql
Guardfile
/config/api.prod.pfx
