class CreateTasks < ActiveRecord::Migration[4.2]
  def change
    create_table :tasks do |t|
      t.integer :assigned_to_id
      t.string :assigned_to_type
      t.integer :assigned_by_id
      t.string :assigned_by_type
      t.string :state, default: :pending
      t.datetime :called_at
      t.integer :helpdesk_email_id
      t.text :comment

      t.timestamps
    end
    add_index :tasks, [:assigned_to_id, :assigned_to_type]
    add_index :tasks, [:assigned_by_id, :assigned_by_type]
    add_index :tasks, :helpdesk_email_id
  end
end
