class CreateSitePaymentsPayments < ActiveRecord::Migration[6.0]
  def change
    create_table :site_payments_payments do |t|
      t.decimal :amount
      t.references :to_researcher, foreign_key: { to_table: :researchers }
      t.references :note, foreign_key: { to_table: :site_payments_notes }
      t.references :added_by_researcher, foreign_key: { to_table: :researchers}

      t.timestamps
    end
  end
end
