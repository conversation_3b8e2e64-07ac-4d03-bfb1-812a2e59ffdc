class CreateFeePlanRuleEnablements < ActiveRecord::Migration[7.2]
  def change
    create_table :fee_plan_rule_enablements do |t|
      t.references :fee_plan, foreign_key: true
      t.references :fee_plan_rule, foreign_key: true

      t.timestamps
    end

    FeePlanRule.find_each do |rule|
      FeePlan.all.each do |plan|
        FeePlanRuleEnablement.create!(fee_plan: plan, fee_plan_rule: rule) if rule.send("#{plan.internal_name}")
      end
    end
  end
end
