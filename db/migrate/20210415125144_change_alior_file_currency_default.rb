class ChangeAliorFileCurrencyDefault < ActiveRecord::Migration[6.0]
  def change
    AliorFile.where(currency: nil).each do |file|
      transfer = file.clinical_transfers.last

      next unless transfer

      file.currency = transfer.final_currency
      file.save!
    end

    change_column_default :alior_files, :currency, to: 'PLN', from: nil

    AliorFile.where(currency: nil).each do |file|
      file.currency = 'PLN'
      file.save!
    end
  end
end
