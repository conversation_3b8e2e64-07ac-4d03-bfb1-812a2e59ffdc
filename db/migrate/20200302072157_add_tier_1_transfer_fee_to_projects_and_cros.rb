class AddTier1TransferFeeToProjectsAndCros < ActiveRecord::Migration[4.2]
  def change
    add_column :projects, :tier_1_transfer_fee_project, :decimal
    add_column :projects, :tier_2_transfer_fee_project, :decimal

    Project.find_each do |pr|
      pr.tier_1_transfer_fee_project = pr.fee_transfer_project
      pr.tier_2_transfer_fee_project = pr.fee_transfer_project
      pr.save! validate: false
    end

    add_column :contract_research_organizations, :tier_1_transfer_fee, :decimal
    add_column :contract_research_organizations, :tier_2_transfer_fee, :decimal

    ContractResearchOrganization.find_each do |cro|
      cro.tier_1_transfer_fee = cro.fee_transfer
      cro.tier_2_transfer_fee = cro.fee_transfer
      cro.save! validate: false
    end
  end
end
