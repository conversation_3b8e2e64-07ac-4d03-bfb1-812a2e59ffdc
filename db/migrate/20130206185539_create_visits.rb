# -*- encoding : utf-8 -*-
class CreateVisits < ActiveRecord::Migration[4.2]
  def change
    create_table :visits do |t|
      t.integer :clinical_user_id
      t.foreign_key :clinical_users
      t.integer :researcher_id
      t.foreign_key :researcher
      t.decimal :amount
      t.string :name
      t.string :state, :limit => 1
      t.datetime :status_change_date

      t.timestamps
    end
  end
end
