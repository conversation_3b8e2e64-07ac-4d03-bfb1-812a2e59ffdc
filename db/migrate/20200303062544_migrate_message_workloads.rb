class MigrateMessageWorkloads < ActiveRecord::Migration[4.2]
  def up
    MessageWorkload.find_each do |mw|
      ResourceWorkload.create!(
        resource_id: mw.message_id,
        resource_type: 'Message',
        workload_id: mw.workload_id,
        added_by_type: mw.added_by_type,
        added_by_id: mw.added_by_id,
        type: 'MessageWorkload'
      )
    end

    drop_table :message_workloads
  end

  def down
  end
end
