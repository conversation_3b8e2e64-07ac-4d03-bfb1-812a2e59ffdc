class CreateFeePlans < ActiveRecord::Migration[7.2]
  def up
    create_table :fee_plans do |t|
      t.string :internal_name, null: false
      t.string :title, null: false
      t.string :tooltip_text, null: false
      t.string :title_short, null: false
      t.string :message_priority, null: false
      t.boolean :fee_based_on_workload, null: false, default: false
      t.string :notes_priority, null: false
      t.string :tag_text
      t.string :tag_color
      t.string :support_text
      t.string :processing_message

      t.timestamps
    end

    %i[free free_plus premium super_premium platinum].each do |plan|
      message_priority = case plan.to_s
                         when 'premium'
                           "high"
                         when 'super_premium'
                           "urgent"
                         when 'platinum'
                           "urgent"
                         else
                           "normal (2-3 working days)"
                         end

      notes_priority = case plan.to_s
                       when 'free', 'free_plus'
                         'normal'
                       when 'premium'
                         'medium'
                       when 'super_premium'
                         'high'
                       when 'platinum'
                         'urgent'
                       end

      tag_text = case plan.to_s
                 when 'free'
                   'Limited Support'
                 when 'free_plus'
                   'Bronze+ limited'
                 when 'premium'
                   'Silver'
                 when 'super_premium'
                   'Gold'
                 when 'platinum'
                   'Platinum'
                 end

      tag_color = case plan.to_s
                  when 'free'
                    '#00afff'
                  when 'free_plus'
                    '#4b9dae'
                  when 'premium'
                    '#b7b7b7'
                  when 'super_premium'
                    '#FDBB2F'
                  when 'platinum'
                    '#4b9dae'
                  end

      support_text = case plan.to_s
                     when 'super_premium', 'platinum'
                       'Premium support'
                     when 'premium'
                       'Extended support'
                     else
                       'Limited support'
                     end

      processing_message = case plan.to_s
                           when 'free', 'free_plus'
                             "Your request will be processed with a normal priority under the terms and conditions of Bronze SLA (Service Level) for {{clinical_protocol_code}} clinical trial. Please upgrade your service level to Silver SLA or Gold SLA for priority requests and the shortest response time."
                           when 'premium'
                             "Your request will be processed with a higher priority under the terms and conditions of Silver SLA (Service Level) for {{clinical_protocol_code}} clinical trial. Please upgrade your service level to Gold SLA for urgent requests and the shortest response time."
                           when 'super_premium'
                             "Your request will be processed with an urgent priority under the terms and conditions of Gold SLA (Service Level) for {{clinical_protocol_code}} clinical trial. Our team will respond as soon as possible."
                           when 'platinum'
                             "Your request will be processed with an urgent priority under the terms and conditions of Gold SLA (Service Level) for {{clinical_protocol_code}} clinical trial. Our team will respond as soon as possible."
                           end
      FeePlan.create!(
        internal_name: plan,
        title: I18n.t("enumerize.project.fee_plan.#{plan}"),
        tooltip_text: I18n.t("enumerize.project.fee_plan_tooltip.#{plan}"),
        title_short: I18n.t("enumerize.project.fee_plan_name_only.#{plan}"),
        message_priority: message_priority,
        fee_based_on_workload: plan == :free_plus,
        notes_priority: notes_priority,
        tag_text: tag_text,
        tag_color: tag_color,
        support_text: support_text,
        processing_message: processing_message,
      )
    end
  end

  def down
    drop_table :fee_plans, force: :cascade
  end
end
