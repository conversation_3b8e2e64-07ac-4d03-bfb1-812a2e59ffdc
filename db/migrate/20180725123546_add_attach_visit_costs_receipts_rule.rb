class AddAttachVisitCostsReceiptsRule < ActiveRecord::Migration[4.2]
  def up
    FeePlanRule.create!(
      name: "attach_visit_costs_receipts",
      free: false,
      free_plus: false,
      platinum: true,
      premium: true,
      super_premium: true,
      description: 'zalaczanie rachunkow dla kosztow'
      )
  end

  def down
    FeePlanRule.find_by_name('attach_visit_costs_receipts').destroy
  end
end
